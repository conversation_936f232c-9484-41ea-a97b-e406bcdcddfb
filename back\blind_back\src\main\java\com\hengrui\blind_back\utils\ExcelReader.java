package com.hengrui.blind_back.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.builder.ExcelReaderSheetBuilder;
import com.hengrui.blind_back.parse_excel_toDB.entity.DBDefineEntity;
import com.hengrui.blind_back.parse_excel_toDB.listener.DBDefineListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName ExcelReader
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/2 9:18
 * @Version 1.0
 **/
@Slf4j
@Component
public class ExcelReader {

    public static List<DBDefineEntity> readExcel(String filePath) {
        ExcelReaderBuilder readerBuilder = EasyExcel.read(filePath, DBDefineEntity.class, new DBDefineListener());
        ExcelReaderSheetBuilder sheetBuilder = readerBuilder.sheet();
        return sheetBuilder.doReadSync();
    }

    public static List<DBDefineEntity> setStudyId(List<DBDefineEntity> list){

        return null;
    }
}
