package com.hengrui.blind_back.protocol_process_chart.controller;

import com.hengrui.blind_back.protocol_process_chart.service.ProtocolChatService;
import com.hengrui.blind_back.uat.controller.UatController;
import com.hengrui.blind_back.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName ProtocolChatController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/12 13:59
 * @Version 1.0
 **/

@RestController
@Slf4j
public class ProtocolChatController {
    @Autowired
    ProtocolChatService protocolChatService;

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/submitToPPCSAS")

    public Map<String, Object> submitToUATSAS(String taskId,
                                              String server,
                                              String projectId) {
        ProtocolChatController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> results = protocolChatService.submitToPPCSAS(taskId, projectId, server);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        return result;
    }


}
