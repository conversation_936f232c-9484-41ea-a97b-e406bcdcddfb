package com.hengrui.blind_back.ecrf_unlock.utils;

import com.sas.iom.SAS.ILanguageService;
import com.sas.iom.SAS.ILanguageServicePackage.CarriageControlSeqHolder;
import com.sas.iom.SAS.ILanguageServicePackage.LineTypeSeqHolder;
import com.sas.iom.SAS.IWorkspace;
import com.sas.iom.SAS.IWorkspaceHelper;
import com.sas.iom.SASIOMDefs.GenericError;
import com.sas.iom.SASIOMDefs.StringSeqHolder;
import com.sas.services.connection.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.*;

@Slf4j
@Component
public class EcrfSasUtil {

    public void connect(String path) throws ConnectionFactoryException, IOException, GenericError {
        ZeroConfigWorkspaceServer server = new ZeroConfigWorkspaceServer();
        ManualConnectionFactoryConfiguration config = new ManualConnectionFactoryConfiguration(server);
        ConnectionFactoryManager manager = new ConnectionFactoryManager();
        ConnectionFactoryInterface factory = manager.getFactory(config);
        SecurityPackageCredential cred = new SecurityPackageCredential();
        ConnectionInterface cx = factory.getConnection(cred);
        try {
            // Narrow the connection from the server.
            org.omg.CORBA.Object obj = cx.getObject();
            IWorkspace iWorkspace = IWorkspaceHelper.narrow(obj);

            ILanguageService iLanguageService = iWorkspace.LanguageService();
            File file = new File(path);
            InputStreamReader in = new InputStreamReader ( new FileInputStream(file),"UTF-8");
            BufferedReader buffer = new BufferedReader(in);
            StringBuffer stringBuffer = new StringBuffer();
            if (buffer.readLine() != null){
                stringBuffer.append(buffer);
                stringBuffer.append("\n");
            }
            iLanguageService.Submit(stringBuffer.toString());
            //sas log 获取
            CarriageControlSeqHolder logCarriageControlHldr = new CarriageControlSeqHolder();
            LineTypeSeqHolder logLineTypeHldr = new LineTypeSeqHolder();
            StringSeqHolder logHldr = new StringSeqHolder();
            iLanguageService.FlushLogLines(Integer.MAX_VALUE,logCarriageControlHldr, logLineTypeHldr, logHldr);
            String[] logLines = logHldr.value;
            for ( int i=0; i < logLines.length; i++){
                log.info(logLines[i]);
            }
        } finally{
            cx.close();
        }
    }

    public String readSAS(String path, Integer id, String project) throws IOException {

        String envir = null;
        switch (id){
            case 1:
                envir = "Production";
                break;
            case 2:
                envir = "Validation";
                break;
            case 3:
                envir = "Sandbox";
                break;
        }

        System.out.println(envir);
        System.out.println(project);

        String replaceStr1 = "&envir";
        String replaceStr2 = "&project.";

        File file = new File(path);
        InputStreamReader in = new InputStreamReader ( new FileInputStream(file),"GBK");
        BufferedReader bufIn = new BufferedReader(in);

        // 替换
        String line = null;
        StringBuffer stringBuffer = new StringBuffer();
        while ((line = bufIn.readLine()) != null) {
            line = line.replaceAll(envir, replaceStr1).replaceAll(project,replaceStr2);
//            line = line.replaceFirst(envir,replaceStr1).replaceFirst(project,replaceStr2);
            stringBuffer.append(line);
        }
        return stringBuffer.toString();
    }

}
