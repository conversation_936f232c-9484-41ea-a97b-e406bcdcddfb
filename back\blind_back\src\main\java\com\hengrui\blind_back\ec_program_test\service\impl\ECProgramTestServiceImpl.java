package com.hengrui.blind_back.ec_program_test.service.impl;

import cn.hutool.json.JSONObject;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.hengrui.blind_back.blind.constant.BlindConstant;
import com.hengrui.blind_back.blind.utils.Decode64Util;
import com.hengrui.blind_back.blind.utils.MinioUtil;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.ec_program_test.service.ECProgramTestService;
import com.hengrui.blind_back.ecrf_unlock.utils.FileUtil;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.hengrui.blind_back.utils.CallPython;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName ECProgramTestServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/26 17:01
 * @Version 1.0
 **/
@Slf4j
@Service
public class ECProgramTestServiceImpl  implements ECProgramTestService {
    @Autowired
    CallPython callPython;

    @Autowired
    MinioUtil minioUtil;

    @Autowired
    Decode64Util decode64Util;
    @Override
    public Map<String, String> getLogicCheckSetting(String taskId, String projectId, String fid, String fileSuffix) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId="";
        String tableId="";
        if(!ObjectUtils.isEmpty(formInfo.get("recordId"))){
            recordId= formInfo.get("recordId");
            tableId=formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id",recordId);
            object.put("formData",formData);
            projectId=object.toJSONString();
        }else{
            tableId=projectId;
            recordId=taskId;
        }
        String data = formInfo.get("param");
        String bbh = "";
        if (!data.isEmpty()) {
            JSONObject formInfoData = new JSONObject(data);
            bbh = formInfoData.get("bbh").toString();
        }
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        ECProgramTestServiceImpl.log.info("------------获取到的formId是：" + formId + "------------");
        ECProgramTestServiceImpl.log.info("------------获取到的studyId是：" + studyId + "------------");
        Map<String, String> result = new HashMap<>();
        List<Map<String, String>> filesFromEDC = new ArrayList<>();
        Map<String, String> fileObject = new HashMap<>();
        String fileName=studyId+"_EC_"+bbh+"_";
        fileObject.put("fid", fid);
        fileObject.put("fileType", ".xlsx");
        fileObject.put("name", fileName);
        filesFromEDC.add(fileObject);

        String uuid = ULIDGenerator.generateULID();
        Map<String, String> ENVInfo = new HashMap<>();
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("ENV", "UAT");
        ENVInfo.put("data_type", "Logic_Check_Setting");
        ENVInfo.put("data_format", "Excel");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", recordId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", tableId);
        ENVInfo.put("fileSuffix", fileSuffix);
        ENVInfo.put("isLatest", "");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        Map<String,String> pyResult=callPython.downloadEDCServerFile(ENVInfo, filesFromEDC);
        String results = pyResult.get("original_name");
        result.put("result", results);
        //call python program
        return result;
    }

    @Override
    public Map<String, String> getTestDataSet(String taskId, String projectId, String fid, String fileSuffix) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId="";
        String tableId="";
        if(!ObjectUtils.isEmpty(formInfo.get("recordId"))){
            recordId= formInfo.get("recordId");
            tableId=formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id",recordId);
            object.put("formData",formData);
            projectId=object.toJSONString();
        }else{
            tableId=projectId;
            recordId=taskId;
        }
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        ECProgramTestServiceImpl.log.info("------------获取到的formId是：" + formId + "------------");
        ECProgramTestServiceImpl.log.info("------------获取到的studyId是：" + studyId + "------------");
        Map<String, String> result = new HashMap<>();
        List<Map<String, String>> filesFromEDC = new ArrayList<>();
        Map<String, String> fileObject = new HashMap<>();
        fileObject.put("fid", fid);
        fileObject.put("fileType", ".xlsx");
        filesFromEDC.add(fileObject);

        String uuid = ULIDGenerator.generateULID();
        Map<String, String> ENVInfo = new HashMap<>();
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("ENV", "UAT");
        ENVInfo.put("data_type", "data_set");
        ENVInfo.put("data_format", "Excel");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", recordId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", tableId);
        ENVInfo.put("fileSuffix", fileSuffix);
        ENVInfo.put("isLatest", "");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        Map<String,String> pyResult=callPython.downloadEDCServerFile(ENVInfo, filesFromEDC);
        String results = pyResult.get("original_name");
        result.put("result", results);
        //call python program
        return result;
    }

    @Override
    public Map<String, String> getDVS(String taskId, String projectId) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String dataId="";

        String recordId="";
        String tableId="";
        if(!ObjectUtils.isEmpty(formInfo.get("recordId"))){
            recordId= formInfo.get("recordId");
            tableId=formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id",recordId);
            object.put("formData",formData);
            projectId=object.toJSONString();
        }else{
            tableId=projectId;
            recordId=taskId;
        }
        String studyId = formInfo.get("studyId");
        String data = formInfo.get("param");
        log.info("获取到的表单信息为:{}", data);


        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        //1.下载edc导出的逻辑核查文件
        String uuid = ULIDGenerator.generateULID();
        Map<String, String> ENVInfo = new HashMap<>();
        ENVInfo.put("uuid", uuid);
        JSONObject formInfoDatas = new JSONObject(data);
        if (!data.isEmpty()&&!ObjectUtils.isEmpty(formInfoDatas.get("edcinst1"))) {
            ENVInfo.put("ENV", formInfoDatas.get("edcinst1").toString());
        }else{
            ENVInfo.put("ENV", "UAT");
        }

        ENVInfo.put("data_type", "Logic_Check_Setting");
        ENVInfo.put("data_format", "Excel");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", recordId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", tableId);
        ENVInfo.put("fileSuffix", "a");
        ENVInfo.put("isLatest", "");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        String EDCFN=ENVInfo.get("studyId") + "_" + ENVInfo.get("ENV") + "_" + ENVInfo.get("uuid") + "_" + ENVInfo.get("fileSuffix");
        String EDCFileName=ENVInfo.get("studyId") + "_" + ENVInfo.get("ENV") + "_" + ENVInfo.get("uuid") + "_" + ENVInfo.get("fileSuffix") + ".xlsx";
        String  filePath = SASOnlieConstant.EDC_DATA_LOCAL_FOLDER + BlindConstant.FILE_SEPARATOR + EDCFileName;

        List<Map<String, String>> filesFromEDC = new ArrayList<>();

        Map<String,String> pyResult=callPython.downloadEDCServerFile(ENVInfo, filesFromEDC);
        String results = pyResult.get("original_name");

        Path sourcePath = Paths.get(filePath);
        Path targetPath = Paths.get("/home/<USER>/8087/DVS/", sourcePath.getFileName().toString());
        try {
            // Move the file to the target directory
            Files.move(sourcePath, targetPath);
            log.info("File moved successfully!");
        } catch (IOException e) {
            log.info("Error moving file: " + e.getMessage());
        }



        //dvs版本号
        String dvs_version = "";
        String version_zt="";
        String bbh="";
        //方案版本号
        String protocol_v="";
        //dvs版本日期
        String dvs_v_date="";

        if (!data.isEmpty()) {
            JSONObject formInfoData = new JSONObject(data);
            dvs_version = formInfoData.get("dvs_version").toString();
            version_zt= formInfoData.get("version_zt").toString();
            bbh= formInfoData.get("bbh").toString();
            protocol_v= formInfoData.get("protocol_v").toString();
            dvs_v_date= formInfoData.get("dvs_v_date").toString();
            dataId = formInfoData.get("id").toString();
        }
        //项目数据经理
        String pdm = CDTMSAPI.getTDMName(studyId);
        //医学经理
        String mdm = CDTMSAPI.getCraNameAndEmail(studyId, "2", formInfo.get("tableId"));
        //项目经理
        String pm = CDTMSAPI.getCraNameAndEmail(studyId, "3", formInfo.get("tableId"));
        //统计师
        String stat = CDTMSAPI.getCraNameAndEmail(studyId, "5", formInfo.get("tableId"));
        //编程经理
        String cm = CDTMSAPI.getCraNameAndEmail(studyId, "6", formInfo.get("tableId"));
        String used_language = CDTMSAPI.getStudyLanguage(studyId);
        //逻辑核查sheet的拼表操作
        String  sample_file="";
        String lang="";
        String sheetName="04人工核查";
        if(used_language.equals("CH")){
            lang="zh";
            sample_file="数据核查说明模版";
        }else{
            lang="en";
            sample_file="DataCheckIlliustrateTemplate";
            sheetName="04 Manual Check";
        }
        String pythonCommand = "python3.9";
        String scriptPath = "/home/<USER>/8087/DVS/xlsxConcatenate.py";
        String sourceFile = EDCFN;
        String outputFile =studyId+"_"+bbh;
        CallPython.runPy(pythonCommand,scriptPath,sourceFile,outputFile,sample_file,lang);
        //回填封面表单信息
        //根据模板类型获取模板文件
        String templateFileName = "/home/<USER>/8087/DVS/"+studyId+"_"+bbh+".xlsx";
        String fileName = "/home/<USER>/8087/DVS/"+studyId+"_"+bbh+"_DVS.xlsx";
        Map<String, Object> map = MapUtils.newHashMap();
        map.put("caseNum", studyId);
        map.put("DVSVersionNum", bbh);
        map.put("DVSVersionDate", dvs_v_date);
        map.put("PDM", pdm);
        map.put("MDM", mdm);
        map.put("PM", pm);
        map.put("STAT", stat);
        map.put("CM", cm);
        if(bbh.equals("V0.001D")){
            //设置宋体
            ExcelWriter excelWriter = null;
            try {
                excelWriter = EasyExcel.write(fileName).withTemplate(templateFileName).build();
                WriteSheet writeSheet0 = EasyExcel.writerSheet(0).build();
                excelWriter.fill(map, writeSheet0);
            } finally {
                if (excelWriter != null) {
                    excelWriter.finish();
                }
            }
            // Now, set the font to 宋体
            try (Workbook workbook = new XSSFWorkbook(new FileInputStream(fileName))) {
                Sheet sheet = workbook.getSheetAt(0);
                Font font = workbook.createFont();
                font.setFontName("宋体"); // Set font to 宋体
                font.setFontHeightInPoints((short) 12); // Set font size if needed

                // Iterate through the rows and cells to apply the font
                for (Row row : sheet) {
                    for (Cell cell : row) {
                        if (row.getRowNum() != 0 && row.getRowNum() != 6 && row.getRowNum() != 14 && row.getRowNum() != 15&& row.getRowNum() != 16) {
                            CellStyle cellStyle = workbook.createCellStyle();
                            cellStyle.setFont(font);
                            cell.setCellStyle(cellStyle);
                        }
                    }
                }

                // Write the changes back to the file
                try (FileOutputStream outputStream = new FileOutputStream(fileName)) {
                    workbook.write(outputStream);
                } catch (FileNotFoundException e) {
                    throw new RuntimeException(e);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            } catch (FileNotFoundException e) {
                throw new RuntimeException(e);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

            //上传文件到表单数据核查说明(DVS) 字段
            formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
            FileUtil.uploadSASOutputFile(dataId,formId, "dvs_doc", fileName, "study_dvs", ENVInfo.get("requestPrefix"),studyId+"_"+bbh+"_DVS.xlsx","xlsx");
            //上传到minio
            File file = new File(fileName);
            String md5 = decode64Util.getMd5(file);
            minioUtil.uploadNormalFile(file,md5,studyId,"DVS","dvs",studyId+"_"+bbh+"_DVS.xlsx");
        }else{
            //查询上一个审核版本的DVS文件
            String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "study_dvs");
            String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
            if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
                String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
                String result = CDTMSAPI.getDataListInfo(token, "study_dvs", "obj.studyid='" + studyInt + "'" + "and obj.dvs_doc is not null", "edit", "obj.lastmodifytime desc");
                log.info(result);
                JSONArray objects = JSONArray.parseArray(result);
                com.alibaba.fastjson.JSONObject previousVersionObject = com.hengrui.blind_back.utils.FileUtils.findPreviousVersionObject(objects,bbh);

                if(!ObjectUtils.isEmpty(previousVersionObject)&&!previousVersionObject.isEmpty()){
                    String lastVersionfilePath="/home/<USER>/8087/DVS/"+ studyId+"_"+previousVersionObject.get("bbh").toString()+"_last"+".xlsx";
                    //下载上一个版本的文件到本地
                    String dvsDoc = previousVersionObject.get("dvs_doc").toString();
                    String regex = "\\*([A-Z0-9]+\\.xlsx)\\|";
                    Pattern pattern = Pattern.compile(regex);
                    Matcher matcher = pattern.matcher(dvsDoc);
                    String ufn="";
                    if (matcher.find()) {
                        ufn = matcher.group(1);
                       log.info(ufn);
                    } else {
                       log.info("No match found");
                    }
                    log.info("-------------------------------------------------------found the file name is {}---------------------------",ufn);
                    if(!ufn.isEmpty()){
                        try {
                            CDTMSAPI.downloadDataByUserSync("study_dvs",token,ufn,lastVersionfilePath);
                            //逻辑核查sheet的拼表操作  studyId+"_"+bbh+".xlsx"
                            //再执行旧版新版人工核查拼接操作
                   /*         String command="python3.9 /home/<USER>/8087/DVS/xlsx2xlsx.py  --source_file='" + studyId+"_"+previousVersionObject.get("bbh").toString()+"_last"+"'"
                                    +" --target_file='" + studyId+"_"+bbh+  "'"+"'"
                                     +" --sheet_name='" + "人工核查"+  "'";
                            CallPython.executeLatestFill(command);*/
                             pythonCommand = "python3.9";
                             scriptPath = "/home/<USER>/8087/DVS/xlsx2xlsx.py";
                             sourceFile = studyId+"_"+previousVersionObject.get("bbh").toString()+"_last";
                             outputFile =studyId+"_"+bbh;
                            CallPython.runPyBak(pythonCommand,scriptPath,sourceFile,outputFile,sheetName);
                            //回填封面信息
                            ExcelWriter excelWriter = null;
                            try {
                                excelWriter = EasyExcel.write(fileName).withTemplate(templateFileName).build();
                                WriteSheet writeSheet0 = EasyExcel.writerSheet(0).build();
                                excelWriter.fill(map, writeSheet0);
                            } finally {
                                if (excelWriter != null) {
                                    excelWriter.finish();
                                }
                            }
                            formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
                            //上传到minio
                            File file = new File(fileName);
                            String md5 = decode64Util.getMd5(file);
                            minioUtil.uploadNormalFile(file,md5,studyId,"DVS","dvs",studyId+"_"+bbh+"_DVS.xlsx");
                            FileUtil.uploadSASOutputFile(dataId,formId, "dvs_doc", fileName, "study_dvs", ENVInfo.get("requestPrefix"),studyId+"_"+bbh+"_DVS.xlsx","xlsx");

                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
            }
        }

        //2.

        //3.获取上一个版本的DVS文件(如果存在执行4,5,6)

        //4.下载edc导出的逻辑核查文件

        //5.逻辑核查sheet的拼表操作

        //6.执行旧版新版人工核查拼接操作

        //7.回填封面表单信息

        //8.上传文件到表单数据核查说明(DVS) 字段
        return null;
    }
}
