package com.hengrui.blind_back.blind.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.google.gson.Gson;
import com.hengrui.blind_back.blind.constant.BlindConstant;
import com.hengrui.blind_back.blind.entity.CSVMappingEntity;
import com.hengrui.blind_back.blind.entity.CSVMetaEntity;
import com.hengrui.blind_back.blind.entity.CSVTableDataEntity;
import com.hengrui.blind_back.blind.mapper.BlindBackMapper;
import com.hengrui.blind_back.blind.mapper.EDMCDTMSInfoMapper;
import com.hengrui.blind_back.blind.mapper.EDMUAPInfoMapper;
import com.hengrui.blind_back.blind.service.BlindFunctionService;
import com.hengrui.blind_back.blind.utils.*;
import com.hengrui.blind_back.clean_tools.service.CleanToolsService;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.rtsm.service.RTSMService;
import com.hengrui.blind_back.sas_check_content.service.SASCheckContentService;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.sas.iom.SASIOMDefs.GenericError;
import com.sas.services.connection.ConnectionFactoryException;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.exception.ZipException;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.util.*;

@Slf4j
@Service
public class BlindFunctionServiceImpl implements BlindFunctionService {

    @Autowired
    BlindBackMapper blindBackMapper;

    @Autowired
    ParseCSVToDB parseCSVToDB;

    @Autowired
    SASTrigger sasTrigger;

    @Autowired
    Decode64Util decode64Util;

    @Autowired
    MinioUtil minioUtil;

    @Autowired
    MailSendUtil mailSendUtil;

    @Autowired
    EDMUAPInfoMapper edmuapInfoMapper;

    @Autowired
    EDMCDTMSInfoMapper edmcdtmsInfoMapper;


    @Override
    public CSVTableDataEntity getCSVTable(String fileId) {
        CSVTableDataEntity csvTable = new CSVTableDataEntity();
        if (!fileId.isEmpty()) {
            //获取csv表头
            List<CSVMappingEntity> csvCol = blindBackMapper.getCSVCol(fileId);
            //获取csv表体
            List<CSVMetaEntity> csvMeta = blindBackMapper.getCSVMeta(fileId);
            // Combine csvCol and csvMeta into csvTable
            csvTable.setMetalist(csvMeta);
            csvTable.setMappinglist(csvCol);
        }

        return csvTable;
    }

    @Override
    public String submitJsonParams(String jsonData, String blindDemand, String blindMember, String fileName, String projectName, String userName, String role, String originalFileId, String exDataId, String token, String dataType, String blindOperateName, String blindMemberWithMail) {
        String operateId = "";
        Map<String, Object> sasLogPath = new HashMap<>();
        try {
            jsonData = URLDecoder.decode(jsonData, "UTF-8");
            token = URLDecoder.decode(token, "UTF-8");
            fileName = URLDecoder.decode(fileName, "UTF-8");
            blindOperateName = URLDecoder.decode(blindOperateName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        //先把除了file_id以外的数据存储到历史操作记录中
        String recordsAdd = insertBlindRecords(jsonData, blindDemand, blindMember, userName, projectName, fileName, role, exDataId, dataType, blindOperateName, blindMemberWithMail);
        if (!ObjectUtils.isEmpty(recordsAdd) && !recordsAdd.isEmpty()) {
            String[] s = recordsAdd.split("_");
            //在此生成uuid
            operateId = s[0];
        }
        //将json参数文件上传到sas服务器读取的指定位置
        String jsonFileName = blindBackMapper.getJsonFileName(projectName, fileName);
        //json文件存储sas网盘位置
        String paramsPath = BlindConstant.SAS_JSON_PATH + BlindConstant.FILE_SEPARATOR + jsonFileName + ".json";
        // 标记文件生成是否成功
        boolean flag = true;
        String jsonMinioPath = jsonFileName + ".json";
        String originalCSVMinioPath = "";
        try {
            // 保证创建一个新文件
            File file = new File(paramsPath);
            if (!file.getParentFile().exists()) {
                // 如果父目录不存在，创建父目录
                file.getParentFile().mkdirs();
            }
            if (file.exists()) { // 如果已存在,删除旧文件
                file.delete();
            }
            file.createNewFile();
            // 将格式化后的字符串写入文件
            Writer write = new OutputStreamWriter(new FileOutputStream(file), "UTF-8");
            write.write(jsonData);
            write.flush();
            write.close();
//            String md5 = decode64Util.getMd5(file);
            //上传到minio
//            String folderName=projectName+"/json_blind/";
//            minioUtil.uploadObject(file,md5,folderName,jsonFileName);
            //上传到sas网盘
//            jsonMinioPath="externalfile/"+folderName+jsonFileName + ".json";

        } catch (Exception e) {
            flag = false;
            e.printStackTrace();
        }

        //获取csv原始路径,实体文件与页面展示的文件名不一致
        String csvFilePath = getCSVFilePath(projectName, fileName, token);
        File csvFile = new File(csvFilePath);
        //将csv文件移动到网盘
        String sasCSVFilePath = BlindConstant.SAS_ORA_CSV_PATH + BlindConstant.FILE_SEPARATOR + csvFile.getName();
        File sasCSVFile = new File(sasCSVFilePath);
        try {
            // Copy the file
            FileUtils.copyFile(csvFile, sasCSVFile);
            log.info("原始文件移动到sas网盘成功.");
        } catch (IOException e) {
            log.info("原始文件移动到sas网盘失败: " + e.getMessage());
        }
//        String csvMd5 = decode64Util.getMd5(csvFile);
//        String csvFolderName=projectName+"/csv_raw/";
//        //上传到minio
//        minioUtil.uploadObject(csvFile,csvMd5,csvFolderName,fileName);
        //上传到sas网盘
//        originalCSVMinioPath="externalfile/"+csvFolderName+fileName;
        //生成json格式文件成功，调用sas程序传递存储的参数路径和原始csv路径 paramsPath filePath
        String sasCodePath = BlindConstant.SAS_CODE_PATH;
        try {
            sasLogPath = sasTrigger.runCreateSAS(sasCodePath, jsonMinioPath, sasCSVFile.getName(), projectName);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (GenericError e) {
            throw new RuntimeException(e);
        } catch (ConnectionFactoryException e) {
            throw new RuntimeException(e);
        }
        Boolean triggerResult = (Boolean) sasLogPath.get("isSuccess");
        //判断调用sas程序的结果
        if (triggerResult == true) {
            //调用sas成功
            // Find the last index of the dot (.)
            int dotIndex = fileName.lastIndexOf(".");
            // Get the substring from the original string
            String strings = fileName.substring(0, dotIndex);
            String blindedFileName = strings;
            log.info("编盲后文件名的拆分字符串是:                                    " + blindedFileName);
            String destFilePath = projectName + "/csv_blind/" + blindedFileName + "_blinding.csv";
            String destCompareFilePath = projectName + "/csv_blind/" + blindedFileName + "_compare.xlsx";
            //本地文件存储路径
            mailSendUtil.createFolder(BlindConstant.BLINDED_CSV_FOLDER + BlindConstant.FILE_SEPARATOR + operateId);
            //本地数据校验文件存储路径
            mailSendUtil.createFolder(BlindConstant.BLINDED_COMPARE_FOLDER + BlindConstant.FILE_SEPARATOR + projectName + "-" + operateId);
            //编盲后文件存放路径
            String localFilePath = BlindConstant.BLINDED_CSV_FOLDER + BlindConstant.FILE_SEPARATOR + operateId + BlindConstant.FILE_SEPARATOR + blindedFileName + "_blinding.csv";
            String localCompareFilePath = BlindConstant.BLINDED_COMPARE_FOLDER + BlindConstant.FILE_SEPARATOR + projectName + "-" + operateId + BlindConstant.FILE_SEPARATOR + blindedFileName + "_compare.xlsx";
            Base64.Encoder encoder = Base64.getEncoder();
            String compareFileURL = "";
            String previewFileURL = "";
            String tempUrl = BlindConstant.COMPARE_URL_PREFIX + projectName + "-" + operateId + "/" + blindedFileName + "_compare.xlsx";
            try {
                byte[] textByte = tempUrl.getBytes("UTF-8");
                compareFileURL = encoder.encodeToString(textByte);
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }

            try {
                String encodeData = URLEncoder.encode(compareFileURL, "UTF-8")
                        .replaceAll("\\+", "%20")
                        .replaceAll("\\!", "%21")
                        .replaceAll("\\'", "%27")
                        .replaceAll("\\(", "%28")
                        .replaceAll("\\)", "%29")
                        .replaceAll("\\~", "%7E");
                //文件预览地址处理
                previewFileURL = BlindConstant.FILE_URL_PREFIX + encodeData;
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }

            //需要完成sas程序的运行，将sas程序运行的结果解析并存储到数据库中,使用minio下载,第一个参数是minio对应bucket下的文件路径，第二个参数是下载到本地的文件地址
            minioUtil.downloadObject(destFilePath, localFilePath);
            //下载编盲前后比对数据，更新该记录
            minioUtil.downloadObject(destCompareFilePath, localCompareFilePath);

            //解析入库,返回入库后的文件id
            String fileID = parseCSVToDB.parseCSVToDB(localFilePath);
            if (!ObjectUtils.isEmpty(fileID) && !fileID.isEmpty()) {
                //更新存储编盲记录，包括编盲后的文件存放位置,包括对应的原始文件id,还要存放编盲后文件存放在本地服务器的位置
                blindBackMapper.updateFileID(fileID, operateId, originalFileId, localFilePath, sasLogPath.get("logPath").toString(), "Y", previewFileURL);
            }
            log.info("调用接口的时间戳：{} 和返回值是:{}", System.currentTimeMillis(), fileID);
            return fileID;
        } else {
            blindBackMapper.updateFileID("", operateId, originalFileId, "", sasLogPath.get("logPath").toString(), "N", "");
            log.info("调用接口的时间戳：{} 和返回值是:{}", System.currentTimeMillis(), "");
            return "";
        }
    }

    @Override
    public String getCSVFile(String projectName, String fileName, String token) {
        try {
            projectName = URLDecoder.decode(projectName, "UTF-8");
            token = URLDecoder.decode(token, "UTF-8");
            fileName = URLDecoder.decode(fileName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        String fileId = "";
        String isApproved = "";
        String qcStatus = "";
        //先找编盲记录表，如果存在编盲记录，则返回最新一次未通过QC的编盲记录，否则返回原始文件的id
        //找原始记录表该项目该文件根据更新时间最新的一条记录的审核状态，如果通过，重新下载原始文件解析入库，如果没通过，用最新的未通过的原始文件的id的数据
        Map<String, Object> objectMap = blindBackMapper.getOriginalIdAndStatus(projectName, fileName);
        if (!ObjectUtils.isEmpty(objectMap)) {
            isApproved = objectMap.get("is_approve").toString();
            fileId = objectMap.get("id").toString();
        }
        if (ObjectUtils.isEmpty(fileId) || fileId.isEmpty() || isApproved.equals("1")) {
            //从前端获取项目的名称、项目的文件名，进行csv文件的下载、解析，并将文件的存储路径返回（或者上传到minio）
            String filePath = getCSVFilePath(projectName, fileName, token);
            if (!ObjectUtils.isEmpty(filePath) && !filePath.isEmpty()) {
                fileId = parseCSVToDB.parseCSVToDB(filePath);
                //原始文件记录表，新增一条原始记录
                blindBackMapper.insertOriginalFileRecord(fileId, projectName, fileName, filePath);
            }
        }
        return fileId;
    }

    @Override
    public List<Map<String, String>> getBlindHistory(String projectName, String fileName) {
        //通过项目名和文件名获取编盲记录表中的blind_records和create_time
        if (!projectName.isEmpty() && !fileName.isEmpty()) {
            return blindBackMapper.getBlindHistory(projectName, fileName);
        } else {
            return null;
        }

    }

    @Override
    public JSON getBlindOperate(String historyId) {
        if (!historyId.isEmpty()) {
            //获取历史操作字符串，转为json对象，返回给前端
            String operateString = blindBackMapper.getBlindOperate(historyId);
            JSONObject result = JSONObject.parseObject(operateString, Feature.OrderedField);
            return result;
        } else {
            return null;
        }


    }


    /**
     * 1.把文件下载凑齐
     * 2.用公邮发送通知
     * 3.保存发送记录
     *
     * @param req
     * @param fileName
     * @param projectName
     * @param userName
     * @return
     */
    @Override
    public List<Map<String, Object>> fileUpload(HttpServletRequest req, String fileName, String projectName, String userName, String isApprove, String fileId, String url, String exDataId, String comments) {
        List<Map<String, Object>> results = new ArrayList<>();
        //如果isApprove有值，则是EDM(QC)发送的邮件，否则为EDM发送的邮件
        Map<String, String> edmInfo = new HashMap<>();
        //type=0 是edm发送的qc邮件，type=1 是edmqc发送的qc 邮件 type=2是edm发送的最终结果邮件
        String type = "";
        //EDM获取文件ID
        String originalFileId = "";
        String edmMail = "";
        if (!ObjectUtils.isEmpty(isApprove) && !isApprove.isEmpty()) {
            //查询原始记录id
            originalFileId = blindBackMapper.getOriginalFileId(fileId);
            //查询的是项目组内，EDM角色人员的邮箱
            edmMail = edmcdtmsInfoMapper.getEDMMail(projectName);
            type = "1";
            //更新编盲记录里的这个fileId对应的状态，
            blindBackMapper.updateBlindStatus(fileId, isApprove, userName);
            //更新原始记录中文件的编盲审核状态
            blindBackMapper.updateOriginalFileStatus(originalFileId, isApprove);
        } else {
            fileId = blindBackMapper.getUnApprovedFileIdByOriginalId(fileId);
            //查询的是项目组内，EDM(QC)角色人员的邮箱
            edmMail = edmcdtmsInfoMapper.getEDMQCMail(projectName);
            type = "0";
        }
        //获取编盲后的文件 查询编盲记录中对应的文件路径
        String blindFilePath = blindBackMapper.getBlindFilePath(fileId);
        //要发送为附件的文件列表
//        List<File> files = new ArrayList<>();
        //获取编盲后的文件，同时获取编盲后文件名
//        File blindFile = new File(blindFilePath);
//        String BlindedFilName=blindFile.getName();
//        files.add(blindFile);
//        for (MultipartFile f : file) {
//            File tempFile = mailSendUtil.MultipartFileToFile(f);
//            files.add(tempFile);
//        }
        //list转数组
//        File[] fileArray = files.toArray(new File[files.size()]);
        ArrayList<String> tos = new ArrayList();
        //获取发件人的署名信息，名字和电话
        String phoneNumber = "";
        String signName = "";
        //查询当前用户的电话个姓名
        edmInfo = edmcdtmsInfoMapper.getUserInfoByMail(userName);
        if (!ObjectUtils.isEmpty(edmInfo)) {
            phoneNumber = edmInfo.get("COL_SJH");
            signName = edmInfo.get("COL_RY");
        }

        //测试环境用
        if (!ObjectUtils.isEmpty(edmInfo) && !ObjectUtils.isEmpty(edmMail) && !edmMail.isEmpty()) {
            //邮件非空校验，之后发送
            tos.add(edmMail);
            String QCResult = "";
            if (isApprove.equals("0")) {
                QCResult = "未通过";
            } else if (isApprove.equals("1")) {
                QCResult = "通过";
            }

            mailSendUtil.batchSendMails(null, tos, comments.isEmpty() ? "" : comments.replaceAll("\r\n", "<br/>"), fileName, projectName, type, QCResult, "", url, signName, phoneNumber);
            //邮件发送记录:记录文件id 原始文件id，操作人，文件名，收件人邮箱，发送时间，
            String id = ULIDGenerator.generateULID();
            blindBackMapper.insertEmailRecords(id, fileId, originalFileId, userName, projectName, fileName, edmMail, type, exDataId, comments);
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("status", "success");
            resultMap.put("url", blindFilePath);
            resultMap.put("fileId", fileId);
            results.add(resultMap);
        }

        return results;

    }

    //获取未发送原始文件id
    @Override
    public String getUnBlindOriginFileId(String fileName, String projectName) {
        String fileId = "";
        if (!ObjectUtils.isEmpty(fileName) && !ObjectUtils.isEmpty(projectName) && !fileName.isEmpty() && !projectName.isEmpty()) {
            //先获取该项目该文件最新的原始文件记录的状态is_approve：0.过程中未通过 1.通过
            fileId = blindBackMapper.unBlindFileId(fileName, projectName);
        }
        return fileId;
    }

    //获取项目成员信息
    @Override
    public List<Map<String, Object>> getProjectMembersInfo(String projectName) {
        try {
            projectName = URLDecoder.decode(projectName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        //获取当前项目组内的参与人员
        if (!ObjectUtils.isEmpty(projectName) && !projectName.isEmpty()) {
            return edmcdtmsInfoMapper.getProjectMemberMail(projectName);
        }
        return null;
    }


    //发送文件给项目组的参与人员
    @Override
    public List<Map<String, Object>> sendFinalData(List<String> emails, String fileName, String projectName, String userName, String exDataId) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> results = new ArrayList<>();
        //1.根据项目名和文件名获取最新的编盲通过后的文件id
        String approvedFileId = blindBackMapper.getApprovedFileId(fileName, projectName);
        if (!ObjectUtils.isEmpty(approvedFileId) && !approvedFileId.isEmpty()) {
            //2.如果id查不到，则不显示发送文件按钮 根据这个文件id查询邮件发送记录表，看是否有最终的文件发送记录
//            int isSend = blindBackMapper.getMailIsSend(approvedFileId);
            //该文件id没有发送记录，则发送邮件
            String approvedFilePath = blindBackMapper.getApprovedFilePath(approvedFileId);
            if (!ObjectUtils.isEmpty(approvedFilePath) && !approvedFilePath.isEmpty()) {
                File approvedFile = new File(approvedFilePath);
                String md5 = decode64Util.getMd5(approvedFile);
                //截取前8位作为密码
                String md5Sub = md5.substring(0, 8);

                String[] split = fileName.split(".csv");
                String txtName = BlindConstant.Params_Path + "password" + "_" + split[0] + "_blinding.txt";
                String content = md5Sub;
                //密码写入txt文件
                try (BufferedWriter writer = new BufferedWriter(new FileWriter(txtName))) {
                    writer.write(content);
                } catch (IOException e) {
                    e.printStackTrace();
                }
                //list转数组
                List<File> files = new ArrayList<>();
                try {
                    //将文件压缩并加密
                    File packageFile = MailSendUtil.packageFolderWithPassword(approvedFilePath, BlindConstant.BLINDED_CSV_FOLDER + BlindConstant.FILE_SEPARATOR + split[0] + "_blinding.zip", md5Sub);
                    files.add(packageFile);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                } catch (ZipException e) {
                    throw new RuntimeException(e);
                }

                File[] fileArray = files.toArray(new File[files.size()]);
                File[] passWordArray = new File[1];
                passWordArray[0] = new File(txtName);

                //测试环境用
                ArrayList<String> tos = new ArrayList(emails);
                //获取当前用户的信息
                Map<String, String> userInfoByMail = edmcdtmsInfoMapper.getUserInfoByMail(userName);
                String phoneNumber = "";
                String signName = "";
                if (!ObjectUtils.isEmpty(userInfoByMail)) {
                    phoneNumber = userInfoByMail.get("COL_SJH");
                    signName = userInfoByMail.get("COL_RY");
                }
                if (!ObjectUtils.isEmpty(emails) && emails.size() > 0) {
                    String emailsStr = String.join(",", emails);
                    //邮件非空校验，之后发送
                    mailSendUtil.batchSendMails(fileArray, tos, "", fileName, projectName, "2", "通过", md5, "", signName, phoneNumber);
                    mailSendUtil.batchSendMails(passWordArray, tos, "", fileName, projectName, "3", "通过", md5, "", signName, phoneNumber);
                    String originalFileId = blindBackMapper.getOriginalFileId(approvedFileId);
                    //邮件发送记录:记录文件id 原始文件id，操作人，文件名，收件人邮箱，发送时间，
                    String id = ULIDGenerator.generateULID();
                    blindBackMapper.insertEmailRecords(id, approvedFileId, originalFileId, userName, projectName, fileName, emailsStr, "2", exDataId, "");
                    result.put("emailId", id);
                    result.put("fileId", approvedFileId);
                    results.add(result);
                    return results;
                }

            } else {
                return results;
            }


        }

        return results;
    }

    @Override
    public boolean getFileIsSendCount(String fileName, String projectName) {
        if (!ObjectUtils.isEmpty(fileName) && !fileName.isEmpty() && !ObjectUtils.isEmpty(projectName) && !projectName.isEmpty()) {
            int fileIsSendCount = blindBackMapper.getFileIsSendCount(fileName, projectName);
            if (fileIsSendCount > 0) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    @Override
    public boolean judgeIsBlinded(String fileId) {
        int count = blindBackMapper.judgeIsBlinded(fileId);
        if (count > 0) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public JSONObject checkLogin(String token, String isLogin) {
        //查询sso表，如果不存在该值，就存一个
        int tokenCount = blindBackMapper.checkTokenIsExist(token);
        if (tokenCount > 0 && isLogin.equals("Y")) {
            JSONObject object = new JSONObject();
            object.put("status", 400);
            return object;
        } else {
            try {
                token = URLDecoder.decode(token, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            String result = mailSendUtil.checkLogin(token);
            JSONObject jsonObject = JSONObject.parseObject(result);
            String status = (String) jsonObject.get("status");
            if (status.equals("200") && isLogin.equals("Y")) {
                //判断这个token是有效的
                blindBackMapper.insertTokenRecord(token);
            }
            return jsonObject;
        }


    }

    @Override
    public List<Map<String, Object>> OperateAndEmailRecords(String id) {
        if (!ObjectUtils.isEmpty(id) || !id.isEmpty()) {
            List<Map<String, Object>> operateAndEmailHistory = blindBackMapper.getOperateAndEmailHistory(id);
            for (int i = 0; i < operateAndEmailHistory.size(); i++) {
                Map<String, String> userInfoByMail = new HashMap<>();
                if (operateAndEmailHistory.get(i).get("user_id") != null) {
                    userInfoByMail = edmcdtmsInfoMapper.getUserInfoByMail(operateAndEmailHistory.get(i).get("user_id").toString());
                    if (!ObjectUtils.isEmpty(userInfoByMail)) {
                        operateAndEmailHistory.get(i).replace("user_id", userInfoByMail.get("COL_RY"));
                    }
                }


            }
            return operateAndEmailHistory;
        }
        return null;
    }

    @Override
    public String getCompareFileUrl(String fileId) {
        String url = "";
        //获取文件id
        if (!ObjectUtils.isEmpty(fileId) || !fileId.isEmpty()) {
            url = blindBackMapper.getCompareFileUrl(fileId);
        }
        return url;
    }

    @Override
    public Map<String, String> getQCFileInfo(String fileName, String projectName) {
        //获取QC文件信息
        try {
            projectName = URLDecoder.decode(projectName, "UTF-8");
            fileName = URLDecoder.decode(fileName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }

        Map<String, String> qcFileInfo = new HashMap<>();
        qcFileInfo = blindBackMapper.getQCFileInfo(fileName, projectName);
        if (!ObjectUtils.isEmpty(qcFileInfo) && !qcFileInfo.isEmpty()) {
            return qcFileInfo;
        }
        return qcFileInfo;
    }

    @Override
    public Map<String, String> getDemandAndName(String historyId) {
        if (!historyId.isEmpty()) {
            return blindBackMapper.getDemandAndName(historyId);
        }
        return null;
    }

    /**
     * 获取编盲后文件
     *
     * @param fileId
     * @return
     */

    @Override
    public String getBlindedFile(String fileId, HttpServletResponse response) {
        if (!fileId.isEmpty()) {
            String fileName = blindBackMapper.getBlindedFile(fileId);
            File file = new File(fileName);
            if (!file.exists()) {
                try {
                    response.sendError(HttpServletResponse.SC_NOT_FOUND);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                return "fail";
            }

            fileName = file.getName();
            long fileSize = file.length();
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            response.setContentLength((int) fileSize);
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

            try (FileInputStream in = new FileInputStream(file)) {
                byte[] buffer = new byte[4096];
                int bytesRead = -1;
                while ((bytesRead = in.read(buffer)) != -1) {
                    response.getOutputStream().write(buffer, 0, bytesRead);
                }
            } catch (FileNotFoundException e) {
                throw new RuntimeException(e);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

            return "success";
        }
        return null;
    }

    @Override
    public String getMemberInfoByFileId(String fileId) {
        if (!ObjectUtils.isEmpty(fileId) && !fileId.isEmpty()) {
            return blindBackMapper.getMemberInfoByFileId(fileId);
        }
        return "";
    }

    @Override
    public String getMemberWithMailByFileId(String fileId) {
        if (!ObjectUtils.isEmpty(fileId) && !fileId.isEmpty()) {
            return blindBackMapper.getBlindMemberWithMail(fileId);
        }
        return "";
    }

    @Override
    public String setDataSetPass(String password, String userName,String studyId,String taskId) {
        if(!taskId.isEmpty()){
            String id = ULIDGenerator.generateULID();
            blindBackMapper.insertDataSetPassword(id, taskId, password, userName, userName, studyId);
            List<Map<String, String>> dataSetPassword = blindBackMapper.getDataSetPassword();
            //how to set the above info into a json file
            Gson gson = new Gson();
            String json = gson.toJson(dataSetPassword);
            try {
                // 保证创建一个新文件
                File file = new File(SASOnlieConstant.SAS_DATA_LOCAL_FOLDER+"tst_rave_pass.json");
                if (!file.getParentFile().exists()) {
                    // 如果父目录不存在，创建父目录
                    file.getParentFile().mkdirs();
                }
                if (file.exists()) { // 如果已存在,删除旧文件
                    file.delete();
                }
                file.createNewFile();
                // 将格式化后的字符串写入文件
                Writer write = new OutputStreamWriter(new FileOutputStream(file), "UTF-8");
                write.write(json);
                write.flush();
                write.close();
                String md5 = decode64Util.getMd5(file);
                //上传调用sas的参数到minio
                minioUtil.uploadObject(file, md5, studyId);
            } catch (Exception e) {
                log.info("json paramter file upload minio failed,please check and the exception time is :" + System.currentTimeMillis());
                e.printStackTrace();
            }
            return "success";
        } else {
            return "fail";
        }

    }




    //获取edm uap的文件路径
    public String getCSVFilePath(String projectName, String fileName, String token) {
        String filePath = "";
        try {
            filePath = MailSendUtil.downloadFile(URLEncoder.encode(projectName, "utf-8"), fileName, token);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }

        return filePath;
    }


    public String insertBlindRecords(String jsonData, String blindDemand, String blindMember, String userName, String projectName, String fileName, String role, String exDataId, String dataType, String blindOperateName, String blindMemberWithMail) {
        int result = 0;
        //新增编盲记录
        String id = ULIDGenerator.generateULID();
        //非空校验
        if (!jsonData.isEmpty() && !userName.isEmpty() && !projectName.isEmpty() && !fileName.isEmpty() && !role.isEmpty() && !exDataId.isEmpty() && !dataType.isEmpty() && !blindOperateName.isEmpty()) {
            result = blindBackMapper.insertBlindRecords(id, jsonData, userName, projectName, fileName, role, exDataId, dataType, blindOperateName, blindDemand, blindMember, blindMemberWithMail);
        }
        return id + "_" + result;
    }



    @Override
    public JSONObject getLoginInfo(String taskId, String projectId) {
        //1.call https://meduap-tst.hengrui.com:8085/remoteButtonTask/getInfo
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String param = formInfo.get("param");
        JSONObject jsonObject = JSONObject.parseObject(param);
        CDTMSAPI.updateWorkFlowStatus(taskId, projectId, "crf_zt","01");
        return jsonObject;
    }

    @Override
    public String getDataSetPass(String studyId) {
        if(!ObjectUtils.isEmpty(studyId)&&!studyId.isEmpty()){
            return  blindBackMapper.getDataSetPass(studyId);
        }else{
            return null;
        }
    }

    @Autowired
    CleanToolsService cleanToolsService;

    @Autowired
    SASCheckContentService sasCheckContentService;

    @Autowired
    RTSMService rtsmService;


    // 定义目标目录
    private static final String TARGET_DIRECTORY = "C:\\MyFile\\testFile";
    @Override
    public String processUploadedFile(MultipartFile file, String userName, String studyId,String taskId,String server) {

        // 检查文件是否为空
        if (file.isEmpty()) {
            throw new IllegalArgumentException("Uploaded file is empty.");
        }

        // 确保目标目录存在，如果不存在则创建
        Path targetDirPath = Paths.get(TARGET_DIRECTORY);
        if (!Files.exists(targetDirPath)) {
            try {
                Files.createDirectories(targetDirPath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        // 构建目标文件路径
        Path targetFilePath = targetDirPath.resolve(file.getOriginalFilename());
        // 保存文件到指定目录
        byte[] bytes = new byte[2048];
        try {
            bytes = file.getBytes();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        try {
            Files.write(targetFilePath, bytes);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        String fileLocalPath=targetFilePath.toString();
        String fileMinioPath="raw/"+studyId+"_sas.zip";
        String id = ULIDGenerator.generateULID();
        blindBackMapper.insertRaveDataUploadRecord(id, userName,studyId, file.getOriginalFilename(),fileLocalPath,fileMinioPath);
        File file1=new File(fileLocalPath);
        //获取上传到minio raw下 并打上tag
        String md5 = decode64Util.getMd5(file1);
        minioUtil.uploadRaveDataSet(file1,md5,studyId,"uat","raw");

        //查询表单信息，判断调用的节点逻辑
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, SASOnlieConstant.REMOTE_SERVER_PROJECTID);
        String param = formInfo.get("param");
        cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(param);
        String transferReason = formInfoData.get("checkarea").toString();
        Map<String, String> results=new HashMap<>();
        if(!transferReason.isEmpty()){
            if(transferReason.equals("影像学数据传输")){
                String result=  sasCheckContentService.getIRCData(taskId, SASOnlieConstant.REMOTE_SERVER_PROJECTID);
            }else if(transferReason.equals("LAB-AE核查工具")){
                 results=cleanToolsService.submitToLabAESAS(taskId, SASOnlieConstant.REMOTE_SERVER_PROJECTID);
            }else if(transferReason.equals("AE-EX-EOT工具")){
                 results = cleanToolsService.submitToAEXOTSAS(taskId, SASOnlieConstant.REMOTE_SERVER_PROJECTID);
            }else if(transferReason.equals("随机化一致性检查")){
                results = rtsmService.submitToRTSMSASBack(taskId, SASOnlieConstant.REMOTE_SERVER_PROJECTID);
            }else if(transferReason.equals("RECIST1.1工具")){
                results = cleanToolsService.submitToRecistSAS(taskId, SASOnlieConstant.REMOTE_SERVER_PROJECTID);
            }
        }
        return null;
    }

}
