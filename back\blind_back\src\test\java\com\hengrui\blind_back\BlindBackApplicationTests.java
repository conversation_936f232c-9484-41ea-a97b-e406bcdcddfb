package com.hengrui.blind_back;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.hengrui.blind_back.blind.config.MinioConfig;
import com.hengrui.blind_back.blind.constant.BlindConstant;
import com.hengrui.blind_back.blind.mapper.BlindBackMapper;
import com.hengrui.blind_back.blind.service.BlindFunctionService;
import com.hengrui.blind_back.blind.utils.*;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.constant.WhoCN;
import com.hengrui.blind_back.ecrf_transfer.entity.ECRFEnity;
import com.hengrui.blind_back.ecrf_unlock.constant.ECRFConstant;
import com.hengrui.blind_back.ecrf_unlock.service.ECRFUnLockService;
import com.hengrui.blind_back.ecrf_unlock.utils.EcrfSASTrigger;
import com.hengrui.blind_back.ecrf_unlock.utils.FileUtil;
import com.hengrui.blind_back.entity.CallPythonEntity;
import com.hengrui.blind_back.onlyOffice.mapper.OnlyOfficeFileMapper;
import com.hengrui.blind_back.parse_excel_toDB.entity.DBDefineEntity;
import com.hengrui.blind_back.parse_excel_toDB.mapper.ParseExcelToDBMapper;
import com.hengrui.blind_back.protocol_process_chart.service.impl.ProtocolChatServiceImpl;
import com.hengrui.blind_back.rtsm.service.RTSMService;
import com.hengrui.blind_back.rtsm.service.impl.RTSMServiceImpl;
import com.hengrui.blind_back.utils.*;
import com.itextpdf.text.DocumentException;
import io.minio.Result;
import io.minio.messages.Item;
import io.minio.messages.VersioningConfiguration;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.exception.ZipException;
import org.apache.commons.io.FileUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.ObjectUtils;

import javax.mail.MessagingException;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.hengrui.blind_back.rtsm.service.impl.RTSMServiceImpl.extractPathAfterKeyword;


@SpringBootTest
@Slf4j
class BlindBackApplicationTests {


    @Autowired
    ParseCSVToDB parseCSVToDB;

    @Autowired
    MinioUtil minioUtil;

    @Autowired
    EcrfSASTrigger sasTrigger;

    @Autowired
    SubmitSAS submitSAS;

    @Autowired
    EDCServerFile edcServerFile;


    @Autowired
    BlindFunctionService blindFunctionService;


    @Autowired
    ECRFUnLockService ecrfUnLockService;


    @Autowired
    FileUtil fileUtil;
    
    
    @Autowired
    com.hengrui.blind_back.utils.FileUtils fileUtils;

    @Autowired
    ParseExcelToDBMapper parseExcelToDBMapper;


    @Test
    void contextLoads() {
    }


    @Test
    void testParseCsv() {
//		String csvFile = "C:\\Users\\<USER>\\Downloads\\INS068-302_M_既往病史.csv";
        String csvFile = "C:\\MyFile\\external_data\\hrg2101-101_5-carboxy-pirfenidone_hq-biosci_20230222_plasma_final.csv";
        parseCSVToDB.parseCSVToDB(csvFile);

    }


    @Test
    void testFileDownload() {
        String urlStr = "https://cdtms.hengrui.com/%E7%BA%BF%E4%B8%8A%E7%AD%BE%E5%90%8D%E5%8A%9F%E8%83%BD%E6%93%8D%E4%BD%9C%E6%8C%87%E5%8D%97_20231101.pdf";
        URL url = null;
        try {
            url = new URL(urlStr);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
        String fileName = null;
        try {
            fileName = URLDecoder.decode(urlStr, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        String[] split = fileName.split("/");
        fileName = split[split.length - 1];
        String tempFileName = "C:\\MyFile\\external_data\\" + fileName;
        File temp = new File(tempFileName);
        try {
            FileUtils.copyURLToFile(url, temp);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


    }


    //生成全局唯一ID
    @Test
    public void tstUUID() {
        for (int i = 0; i < 50; i++) {
            String s = ULIDGenerator.generateULID();
            System.out.println(s);
        }

    }

    //EDM 文件下载
    @Test
    public void testDownloadEDM() {
        try {
            MailSendUtil.downloadFile("EDM_002_ZMM", "受试者日志128764I000.xlsx", "296BD8F48B21428888133D1B69A86E29");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testLogin() {
        String s = MailSendUtil.checkLogin("296BD8F48B21428888133D1B69A86E29");
        System.out.println(s);
    }


    @Test
    public void testSplit() {
        String mm = "12321321.json".split(".json")[0];
        System.out.println(mm);
    }

    @Test
    public void testDownLoad() {
        try {

            String filePath = MailSendUtil.downloadFile(URLEncoder.encode("HRS8807-I-101", "utf-8"), "hrs8807_i_101_hrs8807_plasma_20230207_dft_rev1-hf", "4F449E04C58048A0A82BF53462AAE90B");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }


    //测试编盲后文件压缩加密
    @Test
    public void testEncrypt() {
        try {
            MailSendUtil.packageFolderWithPassword("C:\\MyFile\\external_data\\edm_uap\\dtattach\\8b9d754ddfef6328\\hrs8807_i_101_hrs8807_plasma_20230207_dft_rev1-hf_coding.csv", "C:\\MyFile\\external_data\\edm_uap\\dtattach\\123.zip", "123");
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (ZipException e) {
            throw new RuntimeException(e);
        }
    }

    //测试邮件发送加密文件
    @Test
    public void testSendMail() {
        String email = "<EMAIL>";
        List<String> emails = new ArrayList<>();
        emails.add(email);
        blindFunctionService.sendFinalData(emails, "hrs8807_i_101_hrs8807_plasma_20230207_dft_rev1-hf-3有效2csv.csv", "HRS8807-I-101", "<EMAIL>", "2865102852");

    }


    @Test
    public void testDecrypt() {

    }

    @Test
    public void downloadFile() {
        minioUtil.downloadECRFObject("HR070803-301_LAB_AE.csv", "HR070803-301_LAB_AE.csv");
    }


    @Test
    public void getQueryReportStatic() {
        //下载统计数文件
        minioUtil.downloadObjectWithPath("datamanagement", "output/HRS-4357-101_metrix_return.xlsx", "C:\\Users\\<USER>\\Downloads");
        //解析统计数文件
        String result = "";
        try {
            // Specify the path to your Excel file
            String excelFilePath = "C:\\Users\\<USER>\\Downloads\\HRS-4357-101_metrix_return.xlsx";
            FileInputStream inputStream = new FileInputStream(new File(excelFilePath));

            // Create Workbook instance from excel file
            Workbook workbook = new XSSFWorkbook(inputStream);

            // Get first filePath
            Sheet filePath = workbook.getSheetAt(0);

            // Read first 5 rows
            for (int rowIndex = 0; rowIndex < 5; rowIndex++) {
                Row row = filePath.getRow(rowIndex);
                if (row != null) {
                    // Iterate through each cell in the row
                    for (Cell cell : row) {
                        result += cell.getStringCellValue() + "\t";
                        //   System.out.print(cell.getStringCellValue() + "\t");

                    }

                }

            }
            System.out.println(result);
            // Close workbook and stream
            workbook.close();
            inputStream.close();

        } catch (IOException e) {
            e.printStackTrace();
        }
        // Method 1: Using regex to find all numbers
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(result);

        List<Integer> numbers = new ArrayList<>();
        while (matcher.find() && numbers.size() < 5) {
            numbers.add(Integer.parseInt(matcher.group()));
        }

        // Print results
        System.out.println("Extracted numbers: " + numbers);
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 创建日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String currentDateStr = currentDate.format(formatter);
        String resultStr = "附件是截至" + currentDateStr + "数据导出的进展报告。摘要如下：\n" +
                "① 访视缺失：共" + numbers.get(0) + "个，\n" +
                "② 页面缺失：共" + numbers.get(1) + "条，\n" +
                "③ 质疑：" + numbers.get(2) + "条质疑未回复。待关闭质疑" + numbers.get(3) + "条。\n" +
                "④ 待SDV: 待核查变量共" + numbers.get(4) + "个，\n" +
                "以上内容烦请转发相关CRA督促尽快处理。";
        System.out.println(resultStr);
    }

   /* @Test
    void testSASCalling() {
        String sasCodePath = BlindConstant.SAS_CODE_PATH;
        Map<String, Object> sasLogPath = new HashMap<>();
        try {
            sasLogPath = sasTrigger.runCreateSAS("C:\\Users\\<USER>\\Desktop\\m_unlock_check.txt",
                    "SHR-1314-204", "minios3/ecrfunlock/SHR-1314-204.json");
        } catch (ConnectionFactoryException e) {
            throw new RuntimeException(e);
        }
    }*/

    @Test
    public void testVersion() {
        VersioningConfiguration raw = minioUtil.getVersion("raw");
        System.out.println(raw);
    }

    @Test
    public void listObjects() {
        Iterable<Result<Item>> results = minioUtil.listObjects();
        System.out.println(results);
    }


    @Test
    public void testGetObject() {
        minioUtil.downloadGetObject("dmreview", "HR070803-301_LAB_AE.xlsx");
    }

    @Test
    public void testSubmitECRF() {
        try {
            Gson gson = new Gson();
            String json = gson.toJson("{\"studyId\":\"SHR-1314-204\",\"submit_date\":\"2024-02-19\",\"cutdate\":\"2024-01-09\",\"submit_psw\":\"shr-1314\",\"new_psw\":\"Hr123456\"}");
            String encoded = URLEncoder.encode(json, StandardCharsets.UTF_8.toString());
            ecrfUnLockService.submitToSASECRF(encoded, "SHR-1314-204", "周辉", "2024-02-19", "");
            System.out.println(encoded);
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    @Test
    public void testTime() {
        Map<String, String> tagInfo = minioUtil.getObjectTags("datamanagement", "output/HRS-4357-101_进展报告.xlsx");
        String date = tagInfo.get("date");
        System.out.println(date);
    }


    //download file from CDTMS,return local file path
    @Test
    public void testdownloadFile() {
        try {
            System.out.println(FileUtil.downloadFileromCDTMS("3303211009", "cdtmsen_val", "files", "zip"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    //get study id for create sas original file and upload it to minio
    @Test
    public void testGetInfo() {
        String studyInfo = FileUtil.getStudyIdByTaskId("3303211010", "");
        System.out.println(studyInfo);
    }

    @Test
    public void testDownLoadFile() throws IOException {
        String resultPath = fileUtil.uploadLocalFileToMinio("3303211011", "cdtmsen_val", "files", ".zip");
    }

    @Test
    public void testGetFormId() {
        String formId = fileUtil.getFormIdByTaskId("3303211013");
        log.info("formId is :" + formId);
    }

    @Test
    public void testUploadFile() {
        String newFileName = FileUtil.getDownloadFileName("C:\\Users\\<USER>\\Desktop\\SHR-1314-204_eCRF_unlock_check.xlsx");
        String result = FileUtil.uploadSASOutputFile("3303211013", "E6B8F64ADC06421DB6BEE5399349B4E1", "", "C:\\Users\\<USER>\\Desktop\\SHR-1314-204_eCRF_unlock_check.xlsx", "edm_uap", "cdtms-tst.hengrui.com:82/", newFileName, "dataType");
        log.info(result);
    }


    @Test
    public void testDownload() {
        Map<String, String> map = new HashMap<>();
        map.put("env", "pro");
        map.put("key1", "HRTAU");
        map.put("key2", "HR-TPO-ITP-III-PED_sas.zip");
        map.put("key3", "2024/04/24/19/00");
        Path downloadPath = Paths.get(ECRFConstant.SAS_BLIND_LOG_PATH);
        String result = minioUtil.downloadFileWithTags("raw", "HR-TPO-ITP-III-PED_sas.zip", downloadPath, "2024-04-10");
        log.info(result);
    }


//    @Test
//    public void testUATSASCall(){
//        submitSAS.submitToSAS("%7B%22role%22%3A%22TEAM%22%2C%22pro%22%3A%22SHR-2004-202%22%2C%22crfv%22%3A%22V1.1%22%2C%22crfdat%22%3A%222024-02-04%22%2C%22uatv%22%3A%22V2.1%22%2C%22uatstd%22%3A%222024-01-30%22%2C%22uatend%22%3A%222024-02-04%22%7D",
//                "SHR-2004-102",
//                "%E5%91%A8%E8%BE%89",
//                "uat",
//                "3294822403",
//                "edm_uap",
//                "cdtms-tst.hengrui.com:82/");
//    }

    //"source /home/<USER>/airflow282/bin/activate && " +
    //                    "python /home/<USER>/edc_to_minio/edc_to_minio.py " +
    //                    "--data_type='data_set' --data_format='SAS' --env='UAT' --file_list='SHR-2106-101'";


    @Test
    public void testCallPython() {
        CallPythonEntity entity = new CallPythonEntity();
//        entity.setPythonEnv("/home/<USER>/airflow282/bin/activate &&");
        entity.setDataType("data_set");
        entity.setEnv("UAT");
        entity.setStudyId("SHR-2106-101");
       // edcServerFile.getEDCServerFileByPy(entity, "");
    }

    @Test
    public void getFormInfoByTaskId() {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId("1A76BAA3D42E45DEA71386A98D08D821", "cdtmsen_val");
        String studyId = formInfo.get("studyId");
        System.out.println(studyId);

    }

/*    @Test
    public void testSasTrigger() {
        //String codePath, String studyId, String jsonPath
        //codePath: /home/<USER>/8087/sas_code/test.txt studyId: CDTMS手册优化测试项目jsonPath: minios3/UAT/json/CDTMS手册优化测试项目_UAT_67dfffff77ebf312.json
        try {
            Map<String, Object> objectMap = sasTrigger.runCreateSAS("C:\\\\Users\\\\<USER>\\\\Desktop\\\\test.txt",
                    "CDTMS手册优化测试项目", "minios3/UAT/json/CDTMS手册优化测试项目_UAT_c1cffe79f9eab910.json");
        } catch (ConnectionFactoryException e) {
            throw new RuntimeException(e);
        }
    }*/


    @Test
    public void testGetCDTMSToken() {
        String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "studyinfo");
        System.out.println("I just get the CDTMS Token:" + token);
    }


    @Test
    public void getToken() {
        String token = EDCAPI.getToken("<EMAIL>", "Zh123456", "EDC-TEST-WTJ-01", "pro");
        System.out.println(token);
    }

/*    @Test
    public void getECRFList() {
        JSONObject result = EDCAPI.getSubjectsInfo("EDC-TEST-WTJ-01", "pro", "SV", false);
        System.out.println(result);
    }*/

    @Test
    public void testMinioCPCommand() {
        Map<String, String> ENVInfo = new HashMap<>();
        ENVInfo.put("env", "");
        System.out.println("".equals(ENVInfo.get("env").toString()));
    }

    @Test
    public void testEntitySet() {
        CallPythonEntity entity = new CallPythonEntity();
        entity.setDataType("data_set");
        entity.setEnv("UAT");
        entity.setFileNameSuffix("12312313");
        String original_name = "HRS-5635-101_UAT_cff27cff5dfe4db6_a.zip";
        if (ObjectUtils.isEmpty(entity.getFileNameSuffix())) {
            String[] split = original_name.split(".");
            original_name = split[0] + entity.getFileNameSuffix() + "." + split[1];
            System.out.println(original_name);
        }
    }


    @Test
    public void testGetFileNameSplit() {
        String param="{\"randFilename\":[\"SHR-1210-III-316_Cohort1_RandList_TestVersion_V1.0.csv\"],\"projectCode\":\"SHR-1210-III-316\",\"medicFilename\":[],\"fileType\":\"1\",\"medType\":\"\"}";
        EsignAPI.RTSMPostRequest(SASOnlieConstant.RTSMGEN_API_PREFIX+"tbProject/updateStatus" , param);
    }

    @Test
    public void testDataList() {
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, "cdtmsen_val", SASOnlieConstant.SECRET_KEY, "studyinfo");
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "schedule", "obj.sortorder=0", "", "");
        String temp = JSON.parseArray(dataListInfo).get(0).toString();
        String id = JSON.parseObject(temp).get("id").toString();
        String getFileInfo = CDTMSAPI.getDataListInfo("5B859C9B485C4228AF8762DD119B9318", "xshtbbxx", "obj.studyid='" + id + "' and obj.version_zt='2'", "edit", "");
        String fileInfo = JSON.parseArray(getFileInfo).get(0).toString();
        String revised_doc = JSON.parseObject(fileInfo).get("revised_doc").toString();
        String input = revised_doc;
        String regex = "\\*([A-Z0-9]+\\.docx)\\|";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            String extracted = matcher.group(1);
            System.out.println(extracted); // Output: 2024072E67CFF7833A43D3AFA3ED3B31EC1134.docx
        } else {
            System.out.println("No match found");
        }
    }


    @Test
    public void testReadExcelData() {

        String fileName = "C:\\Work\\随机化生成平台\\file\\随机化与研究药物分配申请表模板_随机+管药_队列研究_镜像替换_20241119 - 副本.xlsx";
        String studyId = "EDC_002";
        try (ExcelReader excelReader = EasyExcel.read(fileName).build()) {
            List<DBDefineEntity> dbDefineDataList = EasyExcel.read(fileName).head(DBDefineEntity.class).sheet(0).doReadSync();
            //4.set uuid and studyId into dbDefineDataList
            for (DBDefineEntity dbDefineEntity : dbDefineDataList) {
                String uuid = ULIDGenerator.generateULID();
                dbDefineEntity.setUuId(uuid);
                dbDefineEntity.setStudyid(studyId);
                log.info("------------------------------I just read this file : {}", JSON.toJSONString(dbDefineEntity));
            }
            parseExcelToDBMapper.insertDatatoECRFDB(dbDefineDataList);
            log.info("----------------------------------the array size is : {}", dbDefineDataList.size());
        }

    }


    @Test
    public void tetsDownloadFileByUserSync() throws IOException {
        CDTMSAPI.downloadDataByUserSync("xshtbbxx", "5B859C9B485C4228AF8762DD119B9318", "2024072E67CFF7833A43D3AFA3ED3B31EC1134.docx", "C:\\Users\\<USER>\\Desktop\\HRS-7535-102-protocol.docx");
    }

    @Test
    public void testUploadFiletoSFTP() {
        SFTPFile.uploadFileToSFTP("C:\\Users\\<USER>\\Downloads\\tst.png",
                "/Projects/CDTMS手册优化测试项目/1/",
                "clinical-ftp.hengrui.com",
                22357, SASOnlieConstant.RTSM_API_USER,
                SASOnlieConstant.RTSM_API_PASS, "tst.png");
    }

    @Test
    public void testApacheSFTPUplaod() {
        FTPUploadExample.VFSUpload();
    }


    @Test
    public void download() {
        SFTPFile.downloadFileFromSFTP("/Projects/CDTMS手册优化测试项目/0/test.png", "C:\\Users\\<USER>\\Downloads\\tst.png", "clinical-ftp.hengrui.com", 22357, "<EMAIL>", SASOnlieConstant.RTSM_API_PASS);
    }


    @Test
    public void testGetSpecificDataFromData() {
        String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "studyinfo");
        //1.2 get studyId
        String studyId = "HRS-5635-101";
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "manual_rev_prog", "obj.studyid=(select obj2.id from Xsht as obj2 where obj2.studyid='" + studyId + "') and obj.version_zt=4 and obj.checkarea=" + "03", "", URLEncoder.encode("createtime desc"));
        JSONArray jsonArray = JSONArray.parseArray(dataListInfo);
        String tmp = jsonArray.get(0).toString();
        JSONObject jsonObject = JSONObject.parseObject(tmp);
        String regime = jsonObject.get("dose_level").toString();
        String drugname = jsonObject.get("drug_name").toString();
        String factor = jsonObject.get("factor").toString();
        System.out.println(regime + " " + drugname + " " + factor);
    }

    @Autowired
    BlindBackMapper blindBackMapper;

    @Test
    public void testInsertPassintoDB() {
        String token = "12321321312312";
        String password = "test213123as";
        String userName = "hengrui.com";
        String role = "TDM";
        String projectName = "CDTMS-TEST";
        String id = ULIDGenerator.generateULID();
        blindBackMapper.insertDataSetPassword(id, token, password, userName, role, projectName);
    }


    @Test
    public void testDateTrans() {
        String dateString = "20240807";

        // Parse the input string
        LocalDate date = LocalDate.parse(dateString, DateTimeFormatter.BASIC_ISO_DATE);

        // Format the date to yyyy-MM-dd
        String formattedDate = date.format(DateTimeFormatter.ISO_LOCAL_DATE);

        System.out.println(formattedDate);
    }

    @Test
    public void getTransferReason() {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId("3936092175", "cdtmsen_val");
        String param = formInfo.get("param");
        cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(param);
        String transferReason = formInfoData.get("checkarea").toString();
        System.out.println(transferReason);
    }

    @Test
    public void downloadFileFromCDTMS() {
        //1.get CDTMS file from research case
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId("3615260674", "cdtmsen_val");
        //1.1 get token
        String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "studyinfo");
        String param = formInfo.get("param");
        cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(param);
        String studyId = formInfoData.get("studyid").toString();
        String id = formInfoData.get("id").toString();
        //1.2 get studyId
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "study_rand_consistency", "obj.id='" + id + "'", "edit", "");
        System.out.println(dataListInfo);
        String temp = JSON.parseArray(dataListInfo).get(0).toString();
        String input = JSON.parseObject(temp).get("rand_dataset").toString();
        if (!input.isEmpty()) {
            String[] split = input.split("\\*");
            String partOne = split[0];
            String[] s = partOne.split("_");
        }

        CDTMSAPI.queryFileDownload(input, token, "xlsx", "");
    }

    @Test
    public void testReadExcel() {
        String fileName = "C:\\Users\\<USER>\\Desktop\\SHR-1701-II-209-death_ae_disc_list.xlsx";
        try (ExcelReader excelReader = EasyExcel.read(fileName).build()) {
            List<ECRFEnity> dbDefineDataList = EasyExcel.read(fileName).head(ECRFEnity.class).sheet(0).doReadSync();
            // Extract only the ecrfNum values
            List<String> ecrfNumList = dbDefineDataList.stream().map(ECRFEnity::getEcrfNum).collect(Collectors.toList());
            try {
                fileUtils.findAndZipMatchingFiles("C:\\MyFile\\testFile", ecrfNumList, "C:\\MyFile\\testFile\\output.zip");
            } catch (IOException e) {
                e.printStackTrace();
            }
            BlindBackApplicationTests.log.info(ecrfNumList.toString());
        }
    }


    @Test
    public void unzipFileintoFolder() throws IOException {
        fileUtils.unzip("C:\\MyFile\\testFile\\HR091506-302_PRO_受试者病例报告表_20241210_133003_无稽查历史.zip", "C:\\Work\\ecrf_list");
    }

    @Test
    public void testFileSIze() {
        boolean zipFileEmpty = fileUtils.isZipFileEmpty("C:\\Users\\<USER>\\Downloads\\HRS-4357-101_ECRF_list.zip");
        System.out.println(zipFileEmpty);
    }

    @Autowired
    private Decode64Util decode64Util;

    @Test
    public void testUploadJsonFile() {
        List<Map<String, String>> dataSetPassword = blindBackMapper.getDataSetPassword();
        //how to set the above info into a json file
        Gson gson = new Gson();
        String json = gson.toJson(dataSetPassword);

        try {
            // 保证创建一个新文件
            File file = new File("C:\\Users\\<USER>\\Desktop\\desktopFolder\\file\\tst_rave_pass.json");
            if (!file.getParentFile().exists()) {
                // 如果父目录不存在，创建父目录
                file.getParentFile().mkdirs();
            }
            if (file.exists()) { // 如果已存在,删除旧文件
                file.delete();
            }
            file.createNewFile();
            // 将格式化后的字符串写入文件
            Writer write = new OutputStreamWriter(new FileOutputStream(file), "UTF-8");
            write.write(json);
            write.flush();
            write.close();
            String md5 = decode64Util.getMd5(file);
            //上传调用sas的参数到minio
            minioUtil.uploadObject(file, md5, "test-123456");
        } catch (Exception e) {
            log.info("json paramter file upload minio failed,please check and the exception time is :" + System.currentTimeMillis());
            e.printStackTrace();
        }
    }

    @Test
    public void tstMd5(){
        File file = new File("C:\\Users\\<USER>\\Downloads\\SHR-1703-201_药物安全数据一致性签字页_2025-03-27.pdf");
        String md5 = decode64Util.getMd5(file);
        System.out.println(md5);
    }


    @Test
    public void testDownLaodDoc() {
        String downloadUri = "https://oos-tst.hengrui.com/cache/files/data/Khirz6zTPdfd8_7328/output.docx/output.docx?md5=PtpzbkXqtj6zz2DB6MlFwQ&expires=1725258899&WOPISrc=Khirz6zTPdfd8&filename=output.docx";
        // Set up SSL context
        SSLContext sslContext = null;
        try {
            sslContext = SSLContext.getInstance("TLS");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        TrustManager[] tm = {new MyX509TrustManager()};
        try {
            sslContext.init(null, tm, new java.security.SecureRandom());
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        }
        SSLSocketFactory ssf = sslContext.getSocketFactory();

        // Open connection
        URL url = null;
        try {
            url = new URL(downloadUri);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
        HttpsURLConnection con = null;
        try {
            con = (HttpsURLConnection) url.openConnection();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        con.setSSLSocketFactory(ssf);
        try {
            con.setRequestMethod("GET");
        } catch (ProtocolException e) {
            throw new RuntimeException(e);
        }

        // Set hostname verifier if necessary
        con.setHostnameVerifier(new NullHostNameVerifier());

        // Define the local path where the file will be saved
        String localFilePath = "C:\\MyFile\\testFile\\" + "test.docx";

        // Read the input stream and write to the specified local file path
        try (InputStream in = new BufferedInputStream(con.getInputStream()); FileOutputStream fos = new FileOutputStream(localFilePath); BufferedOutputStream bout = new BufferedOutputStream(fos, 1024)) {
            byte[] data = new byte[1024];
            int count;
            while ((count = in.read(data, 0, 1024)) != -1) {
                bout.write(data, 0, count);
            }
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    //获取项目ID
    @Test
    public void getStudyId() {
        String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "studyinfo");
        //1.2 get studyId
        String studyId = "HRS-5635-101";
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "xsht", "obj.zt='20' or obj.zt='15' or obj.zt='10'  or obj.zt='30' or obj.zt='35' ", "", URLEncoder.encode("createtime desc"));
        JSONArray jsonArray = JSONArray.parseArray(dataListInfo);
        for (int i = 0; i < jsonArray.size(); i++) {
            String tmp = jsonArray.get(i).toString();
            JSONObject jsonObject = JSONObject.parseObject(tmp);
            String studyid = jsonObject.get("studyid").toString();
            String zt = jsonObject.get("zt").toString();
            String study_phase = jsonObject.get("study_phase").toString();
            System.out.println(studyid + " " + zt + " " + study_phase);
        }
    }

    @Test
    public void getFormIdByToken() {
        String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "crf_handover");
        String formId = CDTMSAPI.getFormIdByToken("https://meduap-tst.hengrui.com:8085/", token);
        // String dataListInfo = CDTMSAPI.getDataListInfo(token, "xsht", "obj.studyid='HRS-4357-101'", "", "");
        String studyId = "HRS-4357-101";
        JSONObject json = new JSONObject();
        json.put("studyid", "2463039488");
        json.put("receiver", "test");
        json.put("notes", "test");
        json.put("zq", 7);
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        String id = JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
        String recordId = CDTMSAPI.usersyndataSave(token, "crf_handover", formId, "", "", json.toString());
        JSONObject saveRestult = JSONObject.parseObject(recordId);
        String id1 = saveRestult.get("id").toString();
        String param = CDTMSAPI.getDataListInfo(token, "crf_handover", "obj.studyid='" + id + "'" + "and obj.id='" + id1 + "'", "edit", "");
        System.out.println(param);
    }


    @Test
    public void testUploadFileViaUserSyn() {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId("3976101888", "cdtmsen_val");
        String data = formInfo.get("param");
        String dataId = "";
        if (!data.isEmpty()) {
            JSONObject formInfoData = JSONObject.parseObject(data);
            dataId = formInfoData.get("id").toString();
        }
        String formId = CDTMSAPI.getFormIdByTaskId("3976101888", "cdtmsen_val");
        String result = fileUtil.uploadFileByUsersyn(dataId, formId, "whodrugcoding", "C:\\MyFile\\testFile\\med\\FZPL-III-302_W.zip", "coding", "meduap-tst.hengrui.com:8085/", "SHR-1316-III-303_W.zip", "zip");
        log.info(result);
    }

    @Test
    public void getFormInfo() {
        //String param = CDTMSAPI.getDataListInfo("3832B863368E49429704807A69D748F4", "crf_handover",  "obj.studyid='" + "HRS-4357-101"+ "'"+"and obj.id='" + "3570827267" + "'", "edit", "");
        Map<String, String> crfHandover = CDTMSAPI.getFormInfoByTaskId_back("3570827271", "crf_handover");
        System.out.println(crfHandover);

    }

    @Test
    public void testJsonStr() {
        boolean validJsonA = CDTMSAPI.isValidJson("1232131");
        boolean validJsonB = CDTMSAPI.isValidJson("{\"timestamp\":\"2024-09-09T05:15:13.697+00:00\",\"status\":500,\"error\":\"Internal Server Error\",\"message\":\"\",\"path\":\"/sas_online/testSchedule\"}");
        System.out.println(validJsonA);
        System.out.println(validJsonB);
    }


    @Test
    public void insertData() {
        int i = blindBackMapper.addScheduleRecord("123456", "123456", "123456", "123456", "123456", "123456", "{\n" + "    \"studyid\": \"HRS-4357-101\",\n" + "    \"tableId\": \"crf_handover\",\n" + "    \"formData\": {\n" + "        \"notes\": \"调度测试3\",\n" + "        \"receiver\": \"调度测试3\",\n" + "        \"zq\": 7,\n" + "        \"transfer_reason\":\"2\",\n" + "        \"tdm\":\"System\"\n" + "    }\n" + "}", "");
        System.out.println(i);
    }


    @Test
    public void getData() {
        List<Map<String, String>> record = blindBackMapper.getScheduleReviewRecord();
        for (Map<String, String> stringStringMap : record) {
            System.out.println(stringStringMap);
        }
    }


    @Test
    public void testScheduleReview() {
        //创建定期审核的记录
        String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "crf_handover");
        String formId = CDTMSAPI.getFormIdByToken("https://meduap-tst.hengrui.com:8085/", token);
        String studyId = "HRS-5635-101";
        //根据表单接口，查询对应的项目的studyId 整数值
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
        JSONObject json = new JSONObject();
        json.put("reviewer_name", "System");
        json.put("study_id", studyInt);
        json.put("zq", 6);
        //创建新审核记录
        String params = CDTMSAPI.usersyndataSave(token, "study_regular_review", formId, "", "", json.toString());
        String recordId = JSON.parseObject(params).get("id").toString();
        String result = CDTMSAPI.getDataListInfo(token, "study_regular_review", "obj.study_id='" + studyInt + "'" + "and obj.id='" + recordId + "'", "edit", "");
        //查询uuid
        String uuid = JSONArray.parseArray(result).getJSONObject(0).get("uuid").toString();
    }


    @Test
    public void getRTSMUserInfo() {
        String studyId = "SHR-4597-101";
        String rtsmRole = "RandSpecialist";
        Map<String, String> rtsmRandInfo = CDTMSAPI.getRTSMRandInfo(studyId, rtsmRole, "吴洁");
        log.info(rtsmRandInfo.toString());
    }

    @Test
    public void getAccountInfo() {
        String studyId = "SHR-4597-101";
        String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "crf_handover");
        String formId = CDTMSAPI.getFormIdByToken("https://meduap-tst.hengrui.com:8085/", token);
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
        String pmResult = CDTMSAPI.getDataListInfo(token, "cra", "obj.studyid='" + studyInt + "'" + "and obj.limitnum=2 and obj.syzt=1 ", "edit", "");
        //获取项目经理姓名、邮箱
        JSONArray pms = JSON.parseArray(pmResult);
        List<String> pmEmails = new ArrayList<>();
        List<String> pmNames = new ArrayList<>();
        for (int i = 0; i < pms.size(); i++) {
            pmEmails.add(JSON.parseObject(JSON.parseArray(pmResult).get(i).toString()).get("cra_email").toString());
            pmNames.add(JSON.parseObject(JSON.parseArray(pmResult).get(i).toString()).get("cra_name").toString());
        }

        String[] pmEmailsStr = pmEmails.toArray(new String[0]);
        String pmEmail = String.join(", ", pmEmailsStr);
        String[] pmNamesStr = pmNames.toArray(new String[0]);
        String pmName = String.join(", ", pmNamesStr);


        System.out.println(pmEmail);
        System.out.println(pmName);


        //获取数据中心人员-DM
        String DMIdresult = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'" + "and obj.limitnum='RandSpecialist' and  obj.active='1'  ", "edit", "");
        //获取DM姓名、邮箱
        List<String> dmUserIds = new ArrayList<>();
        List<String> dmEmails = new ArrayList<>();
        List<String> dmNames = new ArrayList<>();
        JSONArray dms = JSON.parseArray(DMIdresult);
        for (int i = 0; i < dms.size(); i++) {
            dmUserIds.add(JSON.parseObject(JSON.parseArray(DMIdresult).get(i).toString()).get("member").toString());
        }

        for (String userid : dmUserIds) {
            String DMResult = CDTMSAPI.getDataListInfo(token, "ryjbzl", "obj.id='" + userid + "'", "edit", "");

            dmNames.add(JSON.parseObject(JSON.parseArray(DMResult).get(0).toString()).get("xm").toString());
            dmEmails.add(JSON.parseObject(JSON.parseArray(DMResult).get(0).toString()).get("email").toString());
        }
        String[] dmEmailsStr = dmEmails.toArray(new String[0]);
        String dmEmail = String.join(", ", dmEmailsStr);
        String[] dmNamesStr = dmNames.toArray(new String[0]);
        String dmName = String.join(", ", dmNamesStr);
        log.info(dmEmail);
        log.info(dmName);


        //获取数据中心人员-RandSpecialist
        String RSIdresult = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'" + "and obj.limitnum='RandSpecialist' and  obj.active='1' and obj.blinding='1' ", "edit", "");
        //获取RandSpecialist姓名、邮箱
        List<String> rsUserIds = new ArrayList<>();
        List<String> rsEmails = new ArrayList<>();
        List<String> rsNames = new ArrayList<>();
        JSONArray rss = JSON.parseArray(RSIdresult);
        for (int i = 0; i < rss.size(); i++) {
            rsUserIds.add(JSON.parseObject(JSON.parseArray(RSIdresult).get(i).toString()).get("member").toString());
        }

        for (String userid : rsUserIds) {
            String RSResult = CDTMSAPI.getDataListInfo(token, "ryjbzl", "obj.id='" + userid + "'", "edit", "");
            rsNames.add(JSON.parseObject(JSON.parseArray(RSResult).get(0).toString()).get("xm").toString());
            rsEmails.add(JSON.parseObject(JSON.parseArray(RSResult).get(0).toString()).get("email").toString());
        }
        String[] rsEmailsStr = rsEmails.toArray(new String[0]);
        String rsEmail = String.join(", ", rsEmailsStr);
        String[] rsNamesStr = rsNames.toArray(new String[0]);
        String rsName = String.join(", ", rsNamesStr);
        log.info(rsEmail);
        log.info(rsName);

        //获取数据中心人员-TDM
        String TDMIdresult = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'" + "and obj.limitnum='TDM' and  obj.active='1' and obj.blinding='1' ", "edit", "");
        //获取RandSpecialist姓名、邮箱
        List<String> tdmUserIds = new ArrayList<>();
        List<String> tdmEmails = new ArrayList<>();
        List<String> tdmNames = new ArrayList<>();
        JSONArray tdms = JSON.parseArray(TDMIdresult);
        for (int i = 0; i < tdms.size(); i++) {
            tdmUserIds.add(JSON.parseObject(JSON.parseArray(TDMIdresult).get(i).toString()).get("member").toString());
        }

        for (String userid : tdmUserIds) {
            String TDMResult = CDTMSAPI.getDataListInfo(token, "ryjbzl", "obj.id='" + userid + "'", "edit", "");
            tdmNames.add(JSON.parseObject(JSON.parseArray(TDMResult).get(0).toString()).get("xm").toString());
            tdmEmails.add(JSON.parseObject(JSON.parseArray(TDMResult).get(0).toString()).get("email").toString());
        }
        String[] tdmEmailsStr = tdmEmails.toArray(new String[0]);
        String tdmEmail = String.join(", ", tdmEmailsStr);
        String[] tdmNamesStr = tdmNames.toArray(new String[0]);
        String tdmName = String.join(", ", tdmNamesStr);
        log.info(tdmEmail);
        log.info(tdmName);

    }


    @Autowired
    RTSMService rtsmService;

    @Test
    public void testSendEmail() {
        String studyId = "HRS-5635-101";
        Map<String, String> stringStringMap = rtsmService.sendReviewEmail("{\n" + "    \"title\": \"test\",\n" + "    \"content\": \"test content\",\n" + "    \"receiver\": \"<EMAIL>\"\n" + "}", "HRS-5635-101", "asdsadsadasdas");
        System.out.println(stringStringMap);
    }



    @Test
    public void testAddAATAbles() {
        // 构造一个示例JSON字符串
        String jsonExample = "{\n" +
                "  \"fileType\": 1,\n" +
                "  \"accountInfo\": [\n" +
                "    {\n" +
                "      \"accountName\": \"张三\",\n" +
                "      \"account\": \"<EMAIL>\",\n" +
                "      \"role\": \"统计\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"accountName\": \"李四\",\n" +
                "      \"account\": \"<EMAIL>\",\n" +
                "      \"role\": \"随机一\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"accountName\": \"王五\",\n" +
                "      \"account\": \"<EMAIL>\",\n" +
                "      \"role\": \"随机负责人\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"randFileNames\": [\"随机表文件1.xlsx\", \"随机表文件2.xlsx\"],\n" +
                "  \"medFileNames\": [\"药物编号表1.xlsx\", \"药物编号表2.xlsx\"]\n" +
                "}";
         String result = rtsmService.addAuditAndApprovalTable("tets", "周辉", jsonExample);
         System.out.println(result.toString());

    }


    @Test
    public void tstDocxConvertToPdf(){
        String inputFile = "C:\\MyFile\\testFile\\tets_随机分配表与药物编号表审核表-RandQC审核V0.3.docx";
        String outputFile = "C:\\MyFile\\testFile\\tets_随机分配表与药物编号表审核表-RandQC审核V0.3.pdf";
        try {
            fileUtils.convertDocxToPdf(inputFile, outputFile);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @Test
    public void tstGetRandCompareFile(){
        String rtsmUploadFileDrugName = fileUtil.getDownloadFilesName("4049731606", "rtms_drugdata");
        System.out.println(rtsmUploadFileDrugName);
    }

    @Autowired
    MinioConfig minioConfig;

    @Test
    public void testObjExist() {
        String dataSetName = "test.xlsx";
        //1.先查询minio raw下面有么有同名文件，如果没有，调用python获取sas数据集
        minioConfig.setBucketName("regularreview");
        boolean objectExist = minioUtil.isObjectExist(dataSetName, minioConfig);
        System.out.println(objectExist);
    }

    


    @Test
    public void testWrite() {
        String inputFile = "C:\\MyFile\\testFile\\DM-FM-026 随机分配表与药物编号表审核表模板-统计师审核V1.docx";
        String outputFile = "C:\\MyFile\\testFile\\output.docx";
        Map<String, String> params = new HashMap<>();
        params.put("作者", "张三");
        params.put("随专", "李四");
        params.put("随负", "王五");
        params.put("Author", "zhangsan");
        params.put("RQ", "lisi");
        params.put("RL", "wangwu");
        params.put("随专", "李四");
        params.put("随负", "王五");
        params.put("RandFileNames", "1： RSP61418_RandomizationList_TestVersion_ 1组V1.0_2024_01_10_16_05\n" +
                "2： RSP61418_RandomizationList_TestVersion_ 2组V1.0_2024_01_10_16_05");
        params.put("MedFileNames", "1： RSP61418_KitList_TestVersion_药物号V2.0_拓展_2024_05_27_11_35");
        params.put("caseNum", "123456");
        params.put("applyDate", "2025-03-25");
        params.put("rtsmApplyVersion", "123456");
        try {
            fileUtils.replaceVariables(inputFile, outputFile, params);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (InvalidFormatException e) {
            throw new RuntimeException(e);
        }
    }

    //模板文件内容复制粘贴
    @Test
    public void copyAndPaste() {
        String excelFile = "C:\\Work\\随机化生成平台\\file\\随机化与研究药物分配申请表模板_随机+管药_队列研究_镜像替换_20241119模板 - 副本.xlsx";
        int filePathIndex = 1;
        int startRow = 7;
        int endRow = 14;
        int copyTimes = 3;
        fileUtils.copyAndPasteContent(excelFile, filePathIndex, startRow, endRow, copyTimes);
        //计算药物供应管理的起始行和结束行，基于上面的18+(time*8)
        int medStartRow = 18 + (copyTimes * 8);
        int medEndRow = 18 + (copyTimes * 8) + 4;
        copyTimes = 4;
        fileUtils.copyAndPasteContent(excelFile, filePathIndex, medStartRow, medEndRow, copyTimes);
    }


    @Autowired
    OnlyOfficeFileMapper onlyOfficeFileMapper;

    //模板填充
    @Test
    public void testGetStudyInfo() {
        //存储表单录入的内容
        JSONObject formInfo = new JSONObject();
        formInfo.put("account", "test");
        formInfo.put("rmdm", "1");
        formInfo.put("studyDesign", "2");
        formInfo.put("applyType", "1");
        formInfo.put("randMethod", "0");
        formInfo.put("subjectReplace", "1");
        formInfo.put("secondRand", "0");
        formInfo.toString();
        String batchNum = "53d268fed64f45f3";
        onlyOfficeFileMapper.updateFormInfo(batchNum, "HRS-5635-101", formInfo.toString(), "");
        Map<String, String> studyInfo = CDTMSAPI.getStudyInfo("HRS-5635-101");
        //2.通过studyId查询该项目的统计师邮箱
        Map<String, String> rtsmAccountInfo = CDTMSAPI.getRTSMAccountEmail("HRS-5635-101");
       // Map<String, String> rtsmAccountManagerInfo = CDTMSAPI.getRTSMAccountManagerEmail("HRS-5635-101");
        String accountName = rtsmAccountInfo.get("name");
        //String accountManagerName = rtsmAccountManagerInfo.get("name");
        String fileName = "C:\\Work\\随机化生成平台\\file\\随机化与研究药物分配申请表模板_随机+管药_队列研究_镜像替换_20241119填充.xlsx";
        String templateFileName = "C:\\Work\\随机化生成平台\\file\\随机化与研究药物分配申请表模板_随机+管药_队列研究_镜像替换_20241119模板.xlsx";
        Map<String, Object> map = MapUtils.newHashMap();
        map.put("title", studyInfo.get("title").toString());
        map.put("caseNum", studyInfo.get("caseNum").toString());
        map.put("caseVersionNum", studyInfo.get("caseVersionNum").toString());
        map.put("caseVersionDate", studyInfo.get("caseVersionDate").toString().substring(0, 10));
        map.put("accountName", accountName);
        map.put("versionNum", "V1.0");
        //map.put("accountManagerName", accountManagerName);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // Parse current time to date
        LocalDate currentDate = LocalDate.now();
        String currentDateStr = currentDate.format(formatter);
        map.put("currentDate", currentDateStr);
        //获取表单信息
        String formResult = onlyOfficeFileMapper.getFormInfo(batchNum);
        //formResult 转换成json对象
        JSONObject jsonObject = JSONObject.parseObject(formResult);
        String applyType = jsonObject.get("applyType").toString();
        if (applyType.equals("1")) {
            map.put("applyType", "初始");
        } else if (applyType.equals("2")) {
            map.put("applyType", "扩展");
        } else if (applyType.equals("3")) {
            map.put("applyType", "修订/替换");
        }
        EasyExcel.write(fileName).withTemplate(templateFileName).sheet(0).doFill(map);
    }

    @Test
    public void testAPI() {
        String result = CDTMSAPI.callScheduleReview("http://10.10.14.177:5000/", "review_uuid__E6BEA67D55C74431B47A2002A6849E9C", "质疑审核");
        System.out.println(result);
    }

    @Test
    public void testCronCustom() {
        String dateString = "2023-09-20";

        // Create a DateTimeFormatter
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // Parse the string to LocalDate
        LocalDate date = LocalDate.parse(dateString, formatter);

        System.out.println("Parsed date: " + date);
        int dayOfWeek = date.getDayOfWeek().getValue();
        String cronExpression = String.format("%d %d ? * %d", 0, 9, dayOfWeek);
        System.out.println("Parsed cronExpression: " + cronExpression);
    }


    @Test
    public void testGetLanguage() {
        String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "studyinfo");
        //1.2 get studyId
        String studyId = "HRS9531-301";
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "xsht", "obj.studyid='" + studyId + "'", "", "");
        JSONArray jsonArray = JSONArray.parseArray(dataListInfo);
        String tmp = jsonArray.get(0).toString();
        JSONObject jsonObject = JSONObject.parseObject(tmp);
        String used_language = jsonObject.get("used_language").toString();
        System.out.println(used_language + " ");
    }

    @Test
    public void testDate() {
        LocalDate currentDate = LocalDate.now();

        // Define the desired format
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // Format the date as a string
        String dateString = currentDate.format(formatter);

        // Print the result
        System.out.println("Current date: " + dateString);
    }

    @Test
    public void testGetDocTableData() {
        try {
            String filePath = "C:\\Work\\随机化生成平台\\SOP-CDSC-030 随机化与研究药物分配方案的管理\\SOP-CDSC-030F4_随机化工作计划.docx";
            String savePath = "C:\\Work\\随机化生成平台\\SOP-CDSC-030 随机化与研究药物分配方案的管理\\SOP-CDSC-030F4_随机化工作计划_new.docx";
            FileInputStream in = new FileInputStream(filePath);
            FileOutputStream out = new FileOutputStream(savePath);
            XWPFDocument doc = new XWPFDocument(in);
            List<Map<String, String>> data = rtsmService.getStudyBatchRecords("TEST");
            List<XWPFTable> tables = doc.getTables();
            XWPFTable table = tables.get(9);
            //设置第一行
            List<XWPFTableRow> rows = table.getRows();

            for (int i = 0; i < rows.size() - 1; i++) {
                rows.get(i + 1).getCell(0).setText(data.get(i).get("user_name"));
                rows.get(i + 1).getCell(1).setText(data.get(i).get("file_key"));
                rows.get(i + 1).getCell(2).setText(data.get(i).get("status"));
            }
            if (data.size() > rows.size()) {
                for (int i = data.size() - rows.size() - 2; i < data.size(); i++) {
                    table.createRow();//这是新增的一行
                    rows.get(i + 1).getCell(0).setText(data.get(i).get("user_name"));
                    rows.get(i + 1).getCell(1).setText(data.get(i).get("file_key"));
                    rows.get(i + 1).getCell(2).setText(data.get(i).get("status"));
                }
            }
            doc.write(out);
            out.flush();
            out.close();
            System.out.println(tables);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testReg() {
        String input = "INS068-303_随机化计划_2024-10-12.docx*202410E799E461759C4ADA86BB1D417AB43840.docx|";
        String regex = "\\*([A-Z0-9]+\\.docx)\\|";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        String ufn = "";
        if (matcher.find()) {
            ufn = matcher.group(0);
            System.out.println(ufn);
            int endIndex = input.indexOf('*');

            // 提取子字符串
            String result = input.substring(0, endIndex);
            System.out.println(result);
        } else {
            System.out.println("No match found");
        }
    }


    @Test
    public void getDateOfMonth() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 获取当月最后一天的日期
        LocalDate lastDayOfMonth = currentDate.withDayOfMonth(currentDate.lengthOfMonth());
        //获取当月第一天的日期
        LocalDate firstDayOfMonth = currentDate.withDayOfMonth(1);

        // 创建日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 将日期转换为字符串
        String lastDayOfMonthString = lastDayOfMonth.format(formatter);
        //将日期转换为字符串
        String firstDayOfMonthString = firstDayOfMonth.format(formatter);
        System.out.println("当月最后一天: " + lastDayOfMonthString);
        System.out.println("当月第一天: " + firstDayOfMonthString);
    }


    @Test
    public void getfilePath() {
        String fullPath = "group1/M00/02/93/CgoMLWWc91SAc-7NAAOekNOpi9g93.xlsx";
        String[] parts = fullPath.split("/", 3);
        String substring = parts.length > 2 ? "/" + parts[2] : "";

        String testName = "/output/asdasd_2024-10-12.xlsx";
        testName.split("/");
        System.out.println(testName.split("/").length);

    }


    @Test
    public void downloadFiles() {
        //   Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId("3615948806", "cdtmsen_val");
        //    String param = formInfo.get("param");
        //    cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(param);
        //  String studyId = formInfoData.get("studyid").toString();
        //    String tableId = formInfo.get("tableId").toString();
        //获取模板文件
        String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "studyinfo");
        //      String filePath = SASOnlieConstant.SAS_BLIND_LOG_PATH + studyId + ".xlsx";
        String dataListInfo = CDTMSAPI.getDataListInfoWithPage(token, "Xsht", "obj.active=1", "edit", "", 1000);
        log.info(dataListInfo);
        //  String dataListInfo = CDTMSAPI.getDataListInfo(token, "xsht", "obj.studyid='" + studyId + "'", "", "");
     /*   String temp = JSON.parseArray(dataListInfo).get(0).toString();
        String id = JSON.parseObject(temp).get("id").toString();
        String getFileInfo = CDTMSAPI.getDataListInfo(token, "study_rand_consistency", "obj.studyid='" + id + "'", "edit", "obj.lastmodifytime desc");
        String fileInfo = JSON.parseArray(getFileInfo).get(0).toString();
        String input = JSON.parseObject(fileInfo).get(tableId).toString();
        String regex = "\\*([A-Z0-9]+\\.xlsx)\\|";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        String ufn = "";
        if (matcher.find()) {
            ufn = matcher.group(1);
            log.info(ufn);
        } else {
            log.info("No match found");
        }
        log.info("-------------------------------------------------------found the file name is {}---------------------------", ufn);
        if (!ufn.isEmpty()) {
            try {
                CDTMSAPI.downloadDataByUserSync(tableId, token, ufn, filePath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }*/

    }


    @Autowired
    MailUtil mailUtil;

    @Test
    public void getSendMail() throws MessagingException, IOException, ZipException {
        String filepath1 = mailUtil.saveSendMailFile("", "马时", "");
        String filepath2 = mailUtil.saveSendMailFile("开发环境", "", "");
        List<File> fileList = new ArrayList<>();
        if (!filepath1.isEmpty()) {
            fileList.add(new File(filepath1));
        }
        if (!filepath2.isEmpty()) {
            fileList.add(new File(filepath2));
        }
        FileOutputStream fos2 = new FileOutputStream(new File(SASOnlieConstant.RTSM_FILE_PATH + "test.zip"));

        fileUtils.toZip(fileList, fos2);

    }


    @Test
    public void getRTSMInfo() {
        String studyCode = "SHR-8068-201-GC-TEST";
        String filePath = RTSMAPI.downloadRTSMFile(studyCode, "随机信息揭盲报告");
        File file = new File(filePath);
        if (file.exists()) {
            System.out.println("下载成功啦!");
        }

    }


    @Test
    public void testPostResponse() {
        String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "ruta");
        String formId = CDTMSAPI.getFormIdByToken("https://meduap-tst.hengrui.com:8085/", token);
        String ufn = FileUtil.uploadFileByRemoteAPI("**********", formId, "test.zip", "C:\\Work\\随机化生成平台\\file\\test.zip", "cdtmsen_val", SASOnlieConstant.REMOTE_SERVER_API_PREFIX, "test.zip", "zip");
        System.out.println(ufn);
    }

    @Test
    public void testformateDates() {
        String result = FileUtil.formatDate("2023/09/26/00/00");
        System.out.println(result);
    }

    @Test
    public void testData() {
        Map<String, String> accountInfo = new HashMap<>();
        String studyId = "HRS-4357-101";
        String version = "";
        String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "crf_handover");
        String formId = CDTMSAPI.getFormIdByToken("https://meduap-tst.hengrui.com:8085/", token);
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            //String result = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'", "edit", "obj.statr_date desc");
            String result = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'" + "and obj.limitnum='TDM' and  obj.active='1' ", "", "");
            String resultA = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'" + "and obj.limitnum='DM' and  obj.active='1' ", "", "");
            String results = CDTMSAPI.getInfo(SASOnlieConstant.REMOTE_SERVER_API, "3825336358", "cdtmsen_val", "edit");
            JSONObject loginInfo = JSON.parseObject(results).getJSONObject("user");
            String userName = loginInfo.get("username").toString();
            JSONArray objects = JSONArray.parseArray(result);
            String role = "";
            for (int i = 0; i < objects.size(); i++) {
                if (objects.getJSONObject(i).get("member").toString().equals(userName)) {
                    role = "TDM";
                    break;
                }
            }

            JSONArray objectsA = JSONArray.parseArray(resultA);
            for (int i = 0; i < objectsA.size(); i++) {
                if (objectsA.getJSONObject(i).get("member").toString().equals(userName)) {
                    role = "DM";
                    break;
                }
            }
            System.out.println("查询到的人员项目角色是:" + role);

        }

    }

    @Test
    public void getRidOfNasty() {
        String time = FileUtil.transferEDCFIleName("HRS-4357-101_PRO_进展报告汇总_20241115_094235.xlsx");
        System.out.println(time);
    }


    @Test
    public void testGetEDCToken() {
        cn.hutool.json.JSONObject pro = EDCAPI.getSubjectsInfo("HRS9531-201", "pro", "SV", true);
        System.out.println(pro.toString());
    }


    @Test
    public void testExternalData() {
        String studyId = "HRS-4357-101";
        String version = "";
        String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "study_dvs");
        String formId = CDTMSAPI.getFormIdByToken("https://meduap-tst.hengrui.com:8085/", token);
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "study_dvs", "'obj.studyid='" + studyInt + "'", "", "");
            JSONArray objects = JSON.parseArray(result);
            if (objects.size() > 0) {
                System.out.println(objects.get(0));
            }
        }

    }


    @Test
    public void testGetSubjectInfo() {
        String formId = CDTMSAPI.getFormIdByToken("https://meduap-tst.hengrui.com:8085/", "1FD14CD8564C49AD9358AB9E5313DDB0");
        System.out.println(formId);
    }


    @Test
    public void testGetEcrfInfo() {
        String testFilePath = "C:\\Users\\<USER>\\Downloads\\test.xlsx";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, "cdtmsen_val", SASOnlieConstant.SECRET_KEY, "studyinfo");
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "ecrf_build", "obj.studyid=(select obj2.id from Xsht as obj2 where obj2.studyid='" + "HRS-5635-101" + "') and obj.version_zt=2 and obj.note_ecrf is not null", "edit", URLEncoder.encode("createtime desc"));
        JSONArray jsonArray = JSONArray.parseArray(dataListInfo);
        String tmp = jsonArray.get(0).toString();
        String input = JSON.parseObject(tmp).get("note_ecrf").toString();
        String regex = "\\*([A-Z0-9]+\\.xlsx)\\|";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        String ufn = "";
        if (matcher.find()) {
            ufn = matcher.group(1);
            log.info(ufn);
        } else {
            log.info("No match found");
        }
        log.info("-------------------------------------------------------found the file name is {}---------------------------", ufn);
        if (!ufn.isEmpty()) {
            try {
                CDTMSAPI.downloadDataByUserSync("ecrf_build", token, ufn, testFilePath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }


    @Test
    public void getRandomCode() {
        String randomString = fileUtils.getRandomString(6);
        log.info(randomString);
    }

    @Test
    public void testEsignAPI() {
        long date = fileUtils.getExpirationTime();
        try {
            //上传文件
            Map<String, String> result = EsignAPI.uploadFile("C:\\Users\\<USER>\\Downloads\\签字测试文件.pdf", SASOnlieConstant.ESIGN_TST_FILE + "/upload");
            log.info("获取到的文件id为{}，文件名称为{}", result.get("fileId"), result.get("filename"));
            //创建根据id查文件
            cn.hutool.json.JSONObject fileInfo = EsignAPI.getRequest("https://clinical-esign-val.hengrui.com/api/files/" + result.get("fileId"));
            log.info("获取到的文件返回值{}", fileInfo.get("data"));
            //创建任务
            JSONObject taskParam = new JSONObject();
            taskParam.put("file_id", result.get("fileId"));
            taskParam.put("expiration_date", fileUtils.getExpirationTime());
            taskParam.put("callback_url", "https://sas-online-tst.hengrui.com/sas_online/ESignCallBack");
            taskParam.put("file_code", "123456");
            taskParam.put("file_author", "zh");
            taskParam.put("created_by", "zh");
            taskParam.put("file_status", "finish");
            taskParam.put("sign_type", 0);
            String taskInfo = EsignAPI.postRequest("https://clinical-esign-val.hengrui.com/api/tasks", taskParam.toString());
            //拿到任务ID
            JSONObject taskInfoObj = JSONObject.parseObject(taskInfo);
            String taskData = JSONObject.parseObject(taskInfo).get("data").toString();
            JSONObject taskDataObj = JSONObject.parseObject(taskData);
            String taskId = taskDataObj.get("task_id").toString();
            //多个签署人
            JSONObject signerParam1 = new JSONObject();
            signerParam1.put("name_en", "zh");
            signerParam1.put("name_zh", "周辉");
            signerParam1.put("email", Base64.getEncoder().encodeToString("<EMAIL>".getBytes()));
            signerParam1.put("verification_code", "123456");
            signerParam1.put("sign_reason", "我是作者");
            signerParam1.put("is_author", 1);


            JSONObject signerParam2 = new JSONObject();
            signerParam2.put("name_en", "ls");
            signerParam2.put("name_zh", "李四");
            signerParam2.put("email", Base64.getEncoder().encodeToString("<EMAIL>".getBytes()));
            signerParam2.put("verification_code", "123456");
            signerParam2.put("sign_reason", "我是统计师");
            signerParam2.put("is_author", 0);


            //添加签署人-作者
            String signerInfo1 = EsignAPI.postRequest("https://clinical-esign-val.hengrui.com/api/tasks/" + taskId + "/users", signerParam1.toString());
            //添加签署人-统计师
            String signerInfo2 = EsignAPI.postRequest("https://clinical-esign-val.hengrui.com/api/tasks/" + taskId + "/users", signerParam2.toString());
            JSONObject signer1 = JSONObject.parseObject(signerInfo1);
            JSONObject signer2 = JSONObject.parseObject(signerInfo2);
            int userid1 = (int) JSONObject.parseObject(signer1.get("data").toString()).get("id");
            int userid2 = (int) JSONObject.parseObject(signer2.get("data").toString()).get("id");
            JSONObject jsonObject1 = new JSONObject();

             jsonObject1 = EsignAPI.setDefaultSignerPosition(taskId, userid1, "120.1", "220.1", "140.1", "140.2", 1, jsonObject1);
            // 创建items数组
//            JSONArray itemsArray1 = new JSONArray();
            JSONArray itemsArray2 = new JSONArray();
//            // 创建第一个item对象
//            JSONObject item1 = new JSONObject();
//            item1.put("page", 1);
//            item1.put("position_x", "120.1");
//            item1.put("position_y", "220.1");
//            item1.put("date_position_x", "140.1");
//            item1.put("date_position_y", "140.2");

            //创建第二个item对象
            JSONObject item2 = new JSONObject();
            item2.put("page", 2);
            item2.put("position_x", "220.1");
            item2.put("position_y", "220.1");
            item2.put("date_position_x", "240.1");
            item2.put("date_position_y", "240.2");
            itemsArray2.add(item2);
            // 将item对象添加到items数组
//            itemsArray1.add(item1);
            // 将items数组添加到JSON对象
//            jsonObject1.put("items", itemsArray1);
            //更新签署人坐标-作者
//            String updateResult = EsignAPI.putRequest("https://clinical-esign-val.hengrui.com/api/tasks/" + taskId + "/users/" + userid1, jsonObject1.toString());
            jsonObject1.put("items", itemsArray2);
            //更新签署人坐标-统计师
            String accountResult = EsignAPI.putRequest("https://clinical-esign-val.hengrui.com/api/tasks/" + taskId + "/users/" + userid2, jsonObject1.toString());


            log.info(accountResult);
            //任务下发
            JSONObject taskDisParam = new JSONObject();
            taskDisParam.put("type", 1);
            taskDisParam.put("remark", "测试");
            String taskDis = EsignAPI.putRequest("https://clinical-esign-val.hengrui.com/api/tasks/" + taskId, taskDisParam.toString());
            log.info(taskDis);
            //签署 地址 https:///clinical-esign-val.hengrui.com/?task_id=任务id&email=base64加密邮箱&verification_code=文件码
            log.info("作者的签署地址是：" + "https://clinical-esign-val.hengrui.com/?task_id=" + taskId + "&email=" + Base64.getEncoder().encodeToString("<EMAIL>".getBytes()) + "&verification_code=123456");
            log.info("统计师的签署地址是：" + "https://clinical-esign-val.hengrui.com/?task_id=" + taskId + "&email=" + Base64.getEncoder().encodeToString("<EMAIL>".getBytes()) + "&verification_code=123456");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @Test
    public void testRenameFile() {
        String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "study_partner_contacts");
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "study_partner_contacts", "obj.edm_tr_file!=null and transfer_way='2' ", "edit", "obj.date desc");

        String regex = "\\*([A-Z0-9]+\\.docx)\\|";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher("SHR8735-116_外部数据管理培训记录_Jiangsu Hengrui(CRO)_TEST_2024-11-12.docx*202411DDA888AFD2E643078A86A7A0E74C3E2B.docx|");
        String ufn = "";
        if (matcher.find()) {
            ufn = matcher.group(1);
            log.info(ufn);
        } else {
            log.info("No match found");
        }
        System.out.println(ufn);

        String s = fileUtils.formatDate("2023/9/26/00/00");


        String s1 = fileUtils.extractFileName("外部数据管理培训记录_chiikawa_usagi_2024-12-03.pdf*202412FC34D581A2A04476904FE352D6BE722C.pdf|");
        System.out.println(s1);

    }


    @Test
    public void callAPI() {
        String  positionStr="1,120.1,220.1,140.1,140.2|2,119.1,219.1,141.1,141.2";

        List<Map<String,String>> result = new ArrayList<>();

            if (StringUtils.isEmpty(positionStr)) {
                System.out.println(result);
            }
            // 按"|"分割多个坐标点
            String[] positions = positionStr.split("\\|");

            for (String position : positions) {
                // 按","分割单个坐标点的各个参数
                String[] params = position.split(",");
                if (params.length >= 5) {
                    try {
                        // 获取页码
                        int pageIndex = Integer.parseInt(params[0]);
                        // 创建坐标点Map
                        Map<String,String> coordinates = new HashMap<>();
                        coordinates.put("page", params[0]);
                        coordinates.put("position_x", params[1]);
                        coordinates.put("position_y", params[2]);
                        coordinates.put("date_position_x", params[3]);
                        coordinates.put("date_position_y", params[4]);
                        result.add(coordinates);
                    } catch (NumberFormatException e) {
                        System.out.println("解析签字坐标失败"+e);
                    }
                }
            }
        System.out.println(result);
    }
    @Test
    public void getSignerInfo(){
        String receiver="<EMAIL>";
        String id="6352c6d2-0e2b-43aa-afbd-4215bad8fa49";
        String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "study_dvs");
        String esignInstance= CDTMSAPI.getDataListInfo(token, "esign_instance",  " obj.sign_flow_id='" + id +  "'" , "", "");
        String result = CDTMSAPI.getDataListInfo(token, "esign_signer",  " obj.user_code='" + receiver + "' and obj.status=5 or obj.status=0", "edit", "obj.lastmodifytime desc");
        System.out.println(result);
    }

    @Test
    public void testGerDVSFormData() {
        Map<String, String> accountInfo = new HashMap<>();
        String studyId = "HRS9531-301";
        String token = CDTMSAPI.getToken("https://cdtms-pilot.hengrui.com/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "");
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "study_dvs", "obj.studyid='" + studyInt + "'" + "and obj.dvs_doc is not null", "edit", "obj.lastmodifytime desc");
            JSONArray objects = JSONArray.parseArray(result);
            com.alibaba.fastjson.JSONObject previousVersionObject = fileUtils.findPreviousVersionObject(objects, "V2.1");
            String lastVersionfilePath = "C:\\Work\\chatgpt\\test" + BlindConstant.FILE_SEPARATOR + studyId + "_" + previousVersionObject.get("bbh").toString() + ".xlsx";
            //下载上一个版本的文件到本地
            String dvsDoc = previousVersionObject.get("dvs_doc").toString();
            String regex = "\\*([A-Z0-9]+\\.xlsx)\\|";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(dvsDoc);
            String ufn = "";
            if (matcher.find()) {
                ufn = matcher.group(1);
                log.info(ufn);
            } else {
                log.info("No match found");
            }
            log.info("-------------------------------------------------------found the file name is {}---------------------------", ufn);
            if (!ufn.isEmpty()) {
                try {
                    CDTMSAPI.downloadDataByUserSync("study_dvs", token, ufn, lastVersionfilePath);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }


    @Test
    public void testSMO() {
        String value = "";
        String record = "{访视阶段序号=17, 受试者状态=已终止, 访视缺失距今天数=472, 研究中心名称=复旦大学附属肿瘤医院, 项目代码=HR-BLTN-III-EBC, 预期随访日期=2022/8/7, 研究中心编号=1, 访视阶段=随访期V2-4, 受试者代码=1008, close=2022/9/4, open=2022/7/4, visitnum=17}";
        String[] pairs = record.toString().split(", ");
        for (String pair : pairs) {
            String[] keyValue = pair.split("=");
            if (keyValue.length == 2 && keyValue[0].equals("研究中心名称")) {
                value = keyValue[1];
                break;
            }
        }
        System.out.println(value);
    }

    @Test
    public void testGetTitle() {
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "xsht", "obj.studyid='" + "SHR-A2102-201" + "'", "", "");
        JSONArray objects = JSONObject.parseArray(dataListInfo);
        if (objects.size() > 0) {
            JSONObject object = JSONObject.parseObject(objects.get(0).toString());
            if (object.get("used_language").toString().equals("中文")) {
                log.info("CH");
            } else if (object.get("used_language").toString().equals("英文")) {
                log.info("EN");
            } else {
                log.info("");
            }
        } else {
            log.info("notFound");
        }


    }


    @Test
    public void testGetEDCVersion() {
        String studyId = "HRS-4357-101";
        String edcVersion = "";
        String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "study_dvs");
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "edc_name", "obj.studyid='" + studyInt + "'", "edit", "");
            JSONArray objects = JSONArray.parseArray(result);
            if (objects.size() > 0) {
                //获取edc版本
                JSONObject object = JSON.parseObject(objects.get(0).toString());
                edcVersion = object.get("edc_version").toString();
                if (edcVersion.equals("V4.1.0")) {

                } else if (edcVersion.equals("V4.1.8")) {

                } else if (edcVersion.equals("V4.1.20")) {

                }

            }

        }
    }


    @Test
    public void TestGetFileName() {
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "data_submission");
        String suffix = "zip";
        String input = "HR091506-302_PRO_受试者病例报告表_20241210_133003_无稽查历史.zip*2024125BBF6D23FF7C4C33909D73F79EECF4C8.zip|";


        String regex = "\\*([A-Z0-9]+\\." + suffix + ")\\|";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        String ufn = "";
        if (matcher.find()) {
            ufn = matcher.group(1);
            log.info(ufn);
        } else {
            log.info("No match found");
        }

        //   File file = new File("C:\\MyFile\\testFile\\test.xlsx");
        try {
            CDTMSAPI.downloadDataByUserSync("data_submission", token, ufn, "C:\\MyFile\\testFile\\HR091506-302_PRO_受试者病例报告表_20241210_133003_无稽查历史.zip");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    @Test
    public void testDownLsssoad() {
        CDTMSAPI.queryFileDownload("HR091506-302_PRO_受试者病例报告表_20241210_133003_无稽查历史.zip*202412212FB52BA91A4CE1BCDF1B0A55591BC3.zip|", "9664AFD42A16499D85055BDA3536D72C", "zip", "data_submission");
        ;
    }


    @Test
    public void TestEDCApiSet() {
        String brokenString = "茅陋艗猫炉聛莽聽聛猫戮鈥溍モ€βッε撯€懊悸�";

// 尝试不同的编码转换
        String utf8ToGbk = null;
        try {
            utf8ToGbk = new String(brokenString.getBytes("UTF-8"), "GBK");
            String gbkToUtf8 = new String(brokenString.getBytes("GBK"), "UTF-8");
            String iso88591ToUtf8 = new String(brokenString.getBytes("ISO-8859-1"), "UTF-8");
            String utf8ToIso88591 = new String(brokenString.getBytes("UTF-8"), "ISO-8859-1");
            System.out.println("UTF-8 -> GBK: " + utf8ToGbk);
            System.out.println("GBK -> UTF-8: " + gbkToUtf8);
            System.out.println("ISO-8859-1 -> UTF-8: " + iso88591ToUtf8);
            System.out.println("UTF-8 -> ISO-8859-1: " + utf8ToIso88591);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }



    }


    @Test
    public void testControlRawHide() {
        String excelFille = "C:\\Work\\随机化生成平台\\file\\随机化与研究药物分配申请表模板_随机+管药_队列研究_镜像替换_20241217模板 - 副本.xlsx";
        // fileUtils.controlRowVisibility(excelFille,6,46);

        fileUtils.copyAndPasteContent(excelFille, 1, 7, 54, 1);

    }


    @Test
    public void tstWrite(){
        // ... 现有代码 ...
        String url = "http://10.25.51.5/webutil/js/pdfjs/web/viewer.show.do?file=%2Ffiledbroot%2Fcdtmsen_val%2Fdtattach%2F202503%2Fesign_file%2F4%2F6%2F202503463D4B28F4D945B794FC8B1D2BC887F5.pdf&title=tetsSign.pdf&unloadstr=";

// 方法1：使用 substring + indexOf
        String subUrl = url.substring(url.indexOf("/webutil"));
        System.out.println(subUrl);
    }


    @Test
    public void testGetRowNumByText() {
       // String param = "{    \"account\": \"<EMAIL>\",    \"applyType\": 1,    \"applyType2\": \"\",    \"firstRandGroup\": 0,    \"randMethod\": 2,    \"levelNum\": 2,    \"medDesignNum\": 3,    \"queueNum\": 3,    \"rmdm\": 3,    \"secondRand\": 1,    \"secondRandDesignType\": 3,    \"secondRandLevel\": 1,    \"secondRandLevelNum\": 2,    \"studyDesign\": 2,    \"subjectReplace\": 1, \"isMedLevelNum\": 1}";
        String param ="{\n" +
                "  \"applyType\": 1,\n" +
                "  \"applyType2\": \"\",\n" +
                "  \"firstRandGroup\": 0,\n" +
                "  \"isMedLevelNum\": 1,\n" +
                "  \"isRegularDesign\": \"\",\n" +
                "  \"levelNum\": 2,\n" +
                "  \"medDesignNum\": 2,\n" +
                "  \"queueNum\": 2,\n" +
                "  \"randMethod\": 1,\n" +
                "  \"rmdm\": 3,\n" +
                "  \"secondRand\": 0,\n" +
                "  \"secondRandDesignType\": 0,\n" +
                "  \"secondRandLevel\": 0,\n" +
                "  \"secondRandLevelNum\": 0,\n" +
                "  \"studyDesign\": 2,\n" +
                "  \"studyDesign2\": \"\",\n" +
                "  \"subjectReplace\": 2\n" +
                "}";
        String rmfilePath = "C:\\Work\\随机化生成平台\\随机化与研究药物分配申请表_20250226\\随机化与研究药物分配申请表_20250107\\test\\随机化与研究药物分配申请表-模板.xlsx";
        String medfilePath = "C:\\Work\\随机化生成平台\\随机化与研究药物分配申请表_20250226\\随机化与研究药物分配申请表_20250107\\test\\随机化与研究药物分配申请表-药物分配管理-模板.xlsx";
        String randfilePath = "C:\\Work\\随机化生成平台\\随机化与研究药物分配申请表_20250226\\随机化与研究药物分配申请表_20250107\\test\\随机化与研究药物分配申请表-随机分配管理-模板.xlsx";
        String filePath = "";
        JSONObject object = JSONObject.parseObject(param);
        int rmdm = (int) object.get("rmdm");
        if (rmdm == 1) {
            //选择随机分配管理模板
            filePath = randfilePath;
        } else if (rmdm == 2) {
            //选择药物分配管理模板
            filePath = medfilePath;
        } else if (rmdm == 3) {
            //选择随机+药物管理模板
            filePath = rmfilePath;
        }


        //先删除
        List<Integer> rows4 = new ArrayList<>();
        List<Integer> rows2 = new ArrayList<>();
        List<Integer> rows5 = new ArrayList<>();
        List<Integer> rows6 = new ArrayList<>();

        if ((int) object.get("randMethod") == 0) {
            //有分层因素
            //删除分层参数
            rows2 = fileUtils.findRowsByText(filePath, "分层参数", 1);
            if (rows2.size() > 0) {
                int rowNum = rows2.get(0);
                log.info("Found '分层参数' in first column rows: " + rows2.get(0));
                log.info("删除了分层参数");
                //随机分配管理-子方案队列-分层参数删除
                fileUtils.controlRowVisibility(filePath, rowNum, rowNum + 2);
            }

        }


        if ((int) object.get("randMethod") == 1) {
            //有分层因素
            //删除分层参数
            rows6 = fileUtils.findRowsByText(filePath, "哨兵组别比例", 1);
            if (rows6.size() > 0) {
                int rowNum = rows6.get(0);
                log.info("Found '哨兵组别比例' in first column rows: " + rows6.get(0));
                log.info("删除了哨兵组别比例");
                //随机分配管理-子方案队列-分层参数删除
                fileUtils.controlRowVisibility(filePath, rowNum, rowNum );
            }

        }

        //随机方法选择----区组随机
        if ((int) object.get("randMethod") == 2) {
            //有分层因素
            //删除分层参数
            rows6 = fileUtils.findRowsByText(filePath, "哨兵组别比例", 1);
            if (rows6.size() > 0) {
                int rowNum = rows6.get(0);
                log.info("Found '哨兵组别比例' in first column rows: " + rows6.get(0));
                log.info("删除了哨兵组别比例");
                //随机分配管理-子方案队列-分层参数删除
                fileUtils.controlRowVisibility(filePath, rowNum, rowNum );
            }

            //有分层因素
            //删除分层参数
            rows2 = fileUtils.findRowsByText(filePath, "分层参数", 1);
            if (rows2.size() > 0) {
                int rowNum = rows2.get(0);
                log.info("Found '分层参数' in first column rows: " + rows2.get(0));
                log.info("删除了分层参数");
                //随机分配管理-子方案队列-分层参数删除
                com.hengrui.blind_back.utils.FileUtils.controlRowVisibility(filePath, rowNum, rowNum + 2);
            }
        }


        if ((int) object.get("subjectReplace") == 1) {
            //倒序替换
            List<Integer> rows3 = fileUtils.findRowsByText(filePath, "镜像替换间隔", 1);
            if (rows3.size() > 0) {
                int rowNum3 = rows3.get(0);
                fileUtils.controlRowVisibility(filePath, rowNum3, rowNum3);
                log.info("删除了镜像替换间隔");
            }
        } else if ((int) object.get("subjectReplace") == 2) {
            //镜像替换
            fileUtils.clearCellsByText(filePath, "顺序区组数量(如有)");
            log.info("删除了顺序区组数量(如有)");
        } else if ((int) object.get("subjectReplace") == 3) {
            //不替换
            List<Integer> rows3 = fileUtils.findRowsByText(filePath, "镜像替换间隔", 1);
            if (rows3.size() > 0) {
                int rowNum3 = rows3.get(0);
                fileUtils.controlRowVisibility(filePath, rowNum3, rowNum3);
            }
            fileUtils.clearCellsByText(filePath, "顺序区组数量(如有)");
            log.info("删除了镜像替换间隔和顺序区组数量(如有)");
        }


        //二次随机是否
        if ((int) object.get("secondRand") == 0) {
            //删除二次随机
            rows2 = fileUtils.findRowsByText(filePath, "二次随机号设计1", 1);
            if (rows2.size() > 0) {
                int rowNum = rows2.get(0);
                log.info("Found '二次随机号设计1' in first column rows: " + rows2.get(0));
                //二次随机删除
                fileUtils.controlRowVisibility(filePath, rowNum, rowNum + 17);
                log.info("删除了二次随机模块");
            }

        }

        //二次随机分层因素-是否
        if ((int) object.get("secondRandLevel") == 0 && (int) object.get("secondRand") == 1) {
            //删除分层参数
            rows2 = fileUtils.findRowsByText(filePath, "二次随机分层参数", null);
            if (rows2.size() > 0) {
                int rowNum = rows2.get(0);
                log.info("Found '二次随机分层参数' in first column rows: " + rows2.get(0));
                log.info("删除了分层参数");
                //二次随机分层因素-分层参数删除
                fileUtils.controlRowVisibility(filePath, rowNum, rowNum + 2);
            }

        }

        //是否按一次随机组别分层：否
        if ((int) object.get("firstRandGroup") == 0 && (int) object.get("secondRand") == 1) {
            rows2 = fileUtils.findRowsByText(filePath, "二次随机号设计", 1);
            if (rows2.size() > 0) {
                int rowNum = rows2.get(0);
                log.info("Found '二次随机号设计' in first column rows: " + rows2.get(0));
                fileUtils.controlRowVisibility(filePath, rowNum, rowNum + 6);
            }
        } else if ((int) object.get("firstRandGroup") == 1 && (int) object.get("secondRand") == 1 && (int) object.get("secondRandLevel") == 0) {
            //是否按一次随机组别分层：是
            rows2 = fileUtils.findRowsByText(filePath, "二次随机号设计1", 1);
            if (rows2.size() > 0) {
                int rowNum = rows2.get(0);
                log.info("Found '二次随机号设计1' in first column rows: " + rows2.get(0));
                //随机分配管理-子方案队列-分层参数删除
                fileUtils.controlRowVisibility(filePath, rowNum, rowNum + 7);
            }

        } else if ((int) object.get("firstRandGroup") == 1 && (int) object.get("secondRand") == 1 && (int) object.get("secondRandLevel") == 1) {
            //是否按一次随机组别分层：是
            rows2 = fileUtils.findRowsByText(filePath, "二次随机号设计1", 1);
            if (rows2.size() > 0) {
                int rowNum = rows2.get(0);
                log.info("Found '二次随机号设计1' in first column rows: " + rows2.get(0));
                //随机分配管理-子方案队列-分层参数删除
                fileUtils.controlRowVisibility(filePath, rowNum, rowNum + 10);
            }
        }


        //将数据验证加上-随即方法
        List<Integer> rmRow = new ArrayList<>();
        List<Integer> qzRow = new ArrayList<>();
        List<Integer> rrRow = new ArrayList<>();
        List<Integer> rfRow = new ArrayList<>();
        List<Integer> medRow = new ArrayList<>();
        //子方案队列
        rows4 = fileUtils.findRowsByText(filePath, "子方案/队列1", 1);
        if (rows4.size() > 0) {
            int rowNum4 = rows4.get(0);
            int levelCopy = (int) object.get("levelNum") - 1;//分层参数的复制的次数
            int twiceRandCopy = (int) object.get("queueNum") - 1;//子方案队列复制的次数
            //子方案队列-分层参数 没被删除
            if ((int) object.get("randMethod") == 1) {
                if ((int) object.get("subjectReplace") == 1 || (int) object.get("subjectReplace") == 3) {
                    fileUtils.copyAndPasteContent(filePath, 1, rowNum4 + 9, rowNum4 + 9, levelCopy);
                    fileUtils.copyAndPasteContent(filePath, 1, rowNum4, (rowNum4 + 9) + levelCopy, twiceRandCopy);
                } else {
                    fileUtils.copyAndPasteContent(filePath, 1, rowNum4 + 10, rowNum4 + 10, levelCopy);
                    fileUtils.copyAndPasteContent(filePath, 1, rowNum4, (rowNum4 + 10) + levelCopy, twiceRandCopy);
                }

            } else if ((int) object.get("randMethod") == 0) {
                if ((int) object.get("subjectReplace") == 1 || (int) object.get("subjectReplace") == 3) {
                    //子方案队列-分层参数 被删除
                    fileUtils.copyAndPasteContent(filePath, 1, rowNum4, rowNum4 + 7, twiceRandCopy);
                } else {
                    //子方案队列-分层参数 被删除
                    fileUtils.copyAndPasteContent(filePath, 1, rowNum4, rowNum4 + 8, twiceRandCopy);
                }

            }else if((int) object.get("randMethod") == 2){
                if ((int) object.get("subjectReplace") == 1 || (int) object.get("subjectReplace") == 3) {
                    //子方案队列-分层参数 被删除
                    fileUtils.copyAndPasteContent(filePath, 1, rowNum4, rowNum4 + 6, twiceRandCopy);
                } else {
                    //子方案队列-分层参数 被删除
                    fileUtils.copyAndPasteContent(filePath, 1, rowNum4, rowNum4 + 7, twiceRandCopy);
                }
            }
        }

        rows5 = fileUtils.findRowsByText(filePath, "二次随机号设计1", 1);
        if (rows5.size() > 0) {
            int rowNum5 = rows5.get(0);
            int levelCopy = (int) object.get("secondRandLevelNum") - 1;//二次分层参数的复制的次数
            int twiceRandCopy = (int) object.get("secondRandDesignType") - 1;//二次随机号设计类型
            //二次随机分层参数 没被删除
            if ((int) object.get("secondRandLevel") == 1 && (int) object.get("firstRandGroup") == 0 && (int) object.get("secondRand") == 1) {
                fileUtils.copyAndPasteContent(filePath, 1, rowNum5 + 10, rowNum5 + 10, levelCopy);
                fileUtils.copyAndPasteContent(filePath, 1, rowNum5, (rowNum5 + 10) + levelCopy, twiceRandCopy);
            } else if ((int) object.get("secondRandLevel") == 0 && (int) object.get("firstRandGroup") == 0 && (int) object.get("secondRand") == 1) {
                //二次随机分层参数 被删除
                fileUtils.copyAndPasteContent(filePath, 1, rowNum5, rowNum5 + 7, twiceRandCopy);
            }
        }


        //药物供应管理
        rows5 = fileUtils.findRowsByText(filePath, "药物号设计1", 1);
        if (rows5.size() > 0 && ((int) object.get("rmdm") == 2 || (int) object.get("rmdm") == 3)) {
            int rowNum5 = rows5.get(0);
            int levelCopy = (int) object.get("medDesignNum") - 1;
            fileUtils.copyAndPasteContent(filePath, 1, rowNum5, rowNum5 + 6, levelCopy);
           int isMedLevelNum=(int) object.get("isMedLevelNum") ;
            if(isMedLevelNum==0){
                fileUtils.clearCellsByText(filePath, "内层药物号后缀和药物名称");
            }

            log.info("内层药物号后缀和药物名称");
        }


        //处理数据验证下拉框
        //获取随机方法的所在行
        rmRow = fileUtils.findRowsByText(filePath, "随机方法", 1);
        //获取区组类型的所在行
        qzRow = fileUtils.findRowsByText(filePath, "区组类型", 4);
        //获取随机号规则的所在行
        rrRow = fileUtils.findRowsByText(filePath, "随机号规则", 7);
        //获取随机号格式的所在行
        rfRow = fileUtils.findRowsByText(filePath, "随机号格式", 1);
        //获取药物号规则的所在行
        medRow = fileUtils.findRowsByText(filePath, "药物号规则", 1);
        for (int row : rmRow) {
            fileUtils.copyValidationAndContent(filePath, "Z1", "D" + (row + 1));
        }
        for (int row : qzRow) {
            fileUtils.copyValidationAndContent(filePath, "Z2", "G" + (row + 1));
        }
        for (int row : rrRow) {
            fileUtils.copyValidationAndContent(filePath, "Z3", "J" + (row + 1));
        }
        for (int row : rfRow) {
            fileUtils.copyValidationAndContent(filePath, "Z4", "D" + (row + 1));
        }
        for (int row : medRow) {
            fileUtils.copyValidationAndContent(filePath, "Z5", "D" + (row + 1));
        }

        //取消边框
        fileUtils.cancelBoundary(filePath, "Z1", 1);
        fileUtils.cancelBoundary(filePath, "Z2", 1);
        fileUtils.cancelBoundary(filePath, "Z3", 1);
        fileUtils.cancelBoundary(filePath, "Z4", 1);
        fileUtils.cancelBoundary(filePath, "Z5", 1);


    }

    @Autowired
    CDTMSAPI cdtmsapi;

    @Test
    public void uploadRaveData() {
        cdtmsapi.uploadSAS("HRS-5635-101");
    }


    @Test
    public void testCancelBund() {
        String filePath = "C:\\Work\\随机化生成平台\\file\\随机化与研究药物分配申请表 - 副本.xlsx";
        fileUtils.cancelBoundary(filePath, "Z1", 1);
        fileUtils.cancelBoundary(filePath, "Z2", 1);
        fileUtils.cancelBoundary(filePath, "Z3", 1);
        fileUtils.cancelBoundary(filePath, "Z4", 1);
        fileUtils.cancelBoundary(filePath, "Z5", 1);
    }


    @Test
    public void testReadCsv() {
        String inputFile = "C:\\MyFile\\testFile\\med\\FZPL-III-302_W_系统性抗肿瘤治疗史.csv"; // CSV 文件路径
        // 读取 CSV 文件
        List<WhoCN> dataList = new ArrayList<>();
        EasyExcel.read(inputFile, WhoCN.class, new PageReadListener<WhoCN>(dataList::addAll))
                .sheet()
                .doRead();


        // 筛选 Coding Method 列为 "同义词库(global)" 的数据
        List<WhoCN> filteredData = dataList.stream()
                .filter(data -> "同义词库(study)".equals(data.getCodingMethod()))
                .collect(Collectors.toList());

        EasyExcel.write(inputFile, WhoCN.class)
                .sheet("Filtered Data")
                .doWrite(filteredData);
    }


    @Test
    public void testZipFile() {
        String outputZipFile = "C:\\MyFile\\testFile\\med\\" + "FZPL-III-302_W.zip";
        List<String> files = new ArrayList<>();
        files.add("C:\\MyFile\\testFile\\med\\FZPL-III-302_W_系统性抗肿瘤治疗史.csv");
        files.add("C:\\MyFile\\testFile\\med\\FZPL-III-302_W_后续系统性抗肿瘤治疗.csv");
        files.add("C:\\MyFile\\testFile\\med\\FZPL-III-302_W_既往及合并用药.csv");
        // 打包 CSV 文件到 ZIP
        try {
            fileUtils.packCsvFilesToZip(files, outputZipFile);
            System.out.println("CSV 文件已成功打包到 " + outputZipFile);
        } catch (IOException e) {
            System.err.println("打包失败: " + e.getMessage());
        }

    }


    @Test
    public void testConvertPDF() {

        com.hengrui.blind_back.utils.MailUtil.convertToPDF();
        System.out.println("PDF file created successfully.");

    }


    @Test
    public void testExtractTextFromBrackets() {
        String customcode = "                                        <asdasdsa:asdasdasd,dassdadasdas:dassdadasd>                          123123123kjskaxnaskjdhjkhjaxans";
        customcode = "<regime_name=SUBPR|子方案,drug_name=,factor=>";
        String parmStr = fileUtils.extractInsideBrackets(customcode);
        String sascodeStr = fileUtils.extractOutsideBrackets(customcode);
        log.info(parmStr);
        log.info(sascodeStr);
    }


    @Test
    public void testAddRecords() {
        //获取token
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "coding");
        //获取所有studyid
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + "HRS-5635-101" + "'", "edit", "");
        //  String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
        JSONArray objects = JSON.parseArray(studyIdNum);
        // for(int i=0;i<objects.size();i++){
        String studyInt = objects.getJSONObject(0).get("id").toString();
        String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
        //创建新记录
        JSONObject param = new JSONObject();
        param.put("studyid", studyInt);
        param.put("zq", 6);
        String saveRecord = CDTMSAPI.usersyndataSave(token, "coding", formId, "", "", param.toString());

    }

    @Test
    public void tst() {
        Map<String, String> studyInfo = CDTMSAPI.getStudyInfo("HRS-5635-101");

        log.info(studyInfo.toString());


    }

    @Test
    public void getEsiginAccountPass(){
        String token = CDTMSAPI.getToken("https://meduap-tst.hengrui.com:8085/", "cdtmsen_val", "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "esign_account");
        String accountInfo=CDTMSAPI.getDataListInfo(token, "esign_account", "obj.email='" + "<EMAIL>" + "'", "", "");
        JSONArray objects = JSONObject.parseArray(accountInfo);
        String userPass = objects.getJSONObject(0).get("password").toString();
        log.info(userPass);
    }

    @Test
    public void getRTSMinfo(){
        Map<String, String> rtsmAccountInfo = CDTMSAPI.getRTSMAccountEmail("HRS-9813-101");
        log.info(rtsmAccountInfo.toString());
    }


    @Test
    public void testRTSMEsignAccountSave(){
         ResponseResult<?> responseResult = rtsmService.setSignPass("<EMAIL>","123456");
         log.info(responseResult.toString());
    }


    @Test
    public void testInsertApplyInfo(){
        // 创建单个账户对象
        JSONObject accountObj = new JSONObject();
        accountObj.put("accountName", "zh");
        accountObj.put("account", "<EMAIL>");
        // 将对象添加到JSON数组
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(accountObj);
        // 获取标准JSON字符串
        String result = jsonArray.toString();
        //String accountInfo = jsonObject.get("accountInfo").toString();
        JSONArray accountList= JSONArray.parseArray(result);
        for(int i=0;i<accountList.size();i++){
           String accountName = JSONObject.parseObject( accountList.get(i).toString()).get("accountName").toString();
           String  accountEmail = JSONObject.parseObject(accountList.get(i).toString()).get("account").toString();
            String reviewerId = ULIDGenerator.generateULID();
            String uuid = ULIDGenerator.generateULID();
            onlyOfficeFileMapper.insertApplyReviewers(reviewerId,uuid,accountName,accountEmail);
        }
    }

    @Test
    public void tstGetSASCheck(){
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "manual_rev_prog", "obj.studyid=(select obj2.id from Xsht as obj2 where obj2.studyid='" + "HRS-4357-101" + "') and obj.version_zt=4 and obj.checkarea=70 ", "", URLEncoder.encode("createtime desc"));
        log.info(dataListInfo);
    }

    @Test
    public void getMedCodingHis(){
        String studyId = "HRS9531-203";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "study_coding_plan");
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "study_coding_plan", "obj.status='10' and obj.study_id='" + studyInt + "'", "edit", "");
            log.info(result);
            String codingPlanInfo = CDTMSAPI.getDataListInfo(token, "coding_name", "obj.studyid='" + studyInt + "'", "edit", "");
            log.info(codingPlanInfo);
            String subTable = CDTMSAPI.getDataListInfo(token, "study_coding_ver_update", "obj.study_id='" + studyInt + "'", "edit", "");
          log.info(subTable);

        }

    }


    @Test
    public void tstEsignGetRequest(){
        String email="<EMAIL>";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "esign_account");
        String accountInfo = CDTMSAPI.getDataListInfo(token, "esign_account", "obj.email='" + email + "'", "", "");
        JSONArray objects = JSONObject.parseArray(accountInfo);
    }


    @Test
    public void tstExcelGetCellValue(){
        String filePath="C:\\Work\\随机化生成平台\\随机化与研究药物分配申请表_20250107\\随机化与研究药物分配申请表_设计说明_20250107.xlsx";
        List<Integer> rows5 = new ArrayList<>();
        rows5 = fileUtils.findAccountRowsByText(filePath, "项目统计师", 1);
        if (!rows5.isEmpty()) {
            int rowNum = rows5.get(0);
            // 获取项目统计师右侧单元格的内容
            String statisticianName = fileUtils.getCellContentByRowAndColumn(filePath, "封面", rowNum, 2); // 列索引+1获取右侧单元格
            // 现在statisticianName变量中包含了项目统计师右侧单元格的内容
            log.info("项目统计师: {}", statisticianName);
        }
    }


    @Test
    public void tstEsignTask(){
        String email="<EMAIL>";
        String encode=  Base64.getEncoder().encodeToString(email.getBytes());
        cn.hutool.json.JSONObject  signUsers = EsignAPI.getRequest(SASOnlieConstant.ESIGN_TST_TASK+"/"+"7996e3af-e022-401c-9cf0-78be7d82d8ff"+"/users?per_page=100&page=1");
         log.info(signUsers.toString());
         if(!ObjectUtils.isEmpty(signUsers)){
              JSONArray array = JSON.parseArray(signUsers.get("items").toString());
              int userId=0;
              for(int i=0;i<array.size();i++){
                  if(encode.equals(array.getJSONObject(i).get("email").toString())){
                      userId= (int) array.getJSONObject(i).get("id");
                  }
              }
              log.info(String.valueOf(userId));
              String result = EsignAPI.deleteRequest(SASOnlieConstant.ESIGN_TST_TASK + "/" + "7996e3af-e022-401c-9cf0-78be7d82d8ff" + "/users/" + userId);
              log.info(result);
         }



    }



    @Test
    public void tstDocx2PDF(){
        String result = null;
        try {
                Doc2Pdf.doc2pdf(
                    "C:\\MyFile\\testFile\\DM-FM-025随机分配列表（测试版）与药物编号列表（测试版）审核表-RandQCV1.docx",
                    "C:\\MyFile\\testFile\\DM-FM-025随机分配列表（测试版）与药物编号列表（测试版）审核表-RandQCV1.pdf"
            );
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        System.out.println(result);
    }



    @Test
    public void tstRTSMRemoteUrl(){

        JSONObject parameter = new JSONObject();
        parameter.put("fileType", "ls");
        parameter.put("projectCode", "李四");
        parameter.put("randFilename", "123456");
        parameter.put("medicFilename", "我是统计师");
        parameter.put("medType", 0);


        //添加签署人-作者
        String result = EsignAPI.RTSMPostRequest(SASOnlieConstant.RTSMGEN_API_PREFIX+"tbProject/updateStatus" , parameter.toString());
    }

    @Autowired
    RTSMServiceImpl rtsmServiceImpl;
    @Test
    public void addVersionNum(){
        String result=rtsmServiceImpl.incrementVersion("/home/<USER>/8087/rtsm_file/SHR-2001-101_随机化与研究药物分配申请表V5.0.pdf");
        System.out.println(result);
    }


    @Test
    public void getMedCodingHistory() {
        String studyId = "SHR-1703-201";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "study_coding_plan");
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "coding", "obj.studyid='" + studyInt + "'", "edit", "");
            if(com.alibaba.fastjson.JSONArray.parseArray(result).size()>0){
               String medrug= com.alibaba.fastjson.JSONArray.parseArray(result).getJSONObject(0).get("code_doc").toString();
              String whoDrug=  com.alibaba.fastjson.JSONArray.parseArray(result).getJSONObject(0).get("whodrugcoding").toString();
                log.info(medrug);
                log.info(whoDrug);
                if (medrug != null && !"".equals(medrug)) {
                    //获取ufn
                    String input = medrug;
                    // 获取文件名和文件索引
                    String[] fileEntries = input.split("\\|");
                    for (String entry : fileEntries) {
                        if (entry.isEmpty())
                            continue;
                        String[] parts = entry.split("\\*");
                        if (parts.length == 2) {
                            String fileName = parts[0];
                            String fileIndex = parts[1];
                            log.info("File Name: " + fileName);
                            log.info("File Index: " + fileIndex);
                            // Pattern to extract the Chinese text between underscores
                            String pattern = ".*-\\d{8}-\\d{4}_([^_]+)_\\d{8}_\\d{4}_.*";
                            Pattern regex = Pattern.compile(pattern);
                            Matcher matcher = regex.matcher(fileName);
                            String extractedText = "";
                            if (matcher.matches()) {
                                extractedText = matcher.group(1); // "不良事件"
                                log.info("Extracted text: " + extractedText);
                            } else {
                                log.warn("Could not extract text from filename: " + fileName);
                            }


                            try {
                                CDTMSAPI.downloadDataByUserSync("coding",token,fileIndex,"C:\\Work\\medcoding_file\\"+fileName);
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            }
                        }
                    }
                }
            }
        }
    }




    @Test
    public void queryInfo() {
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "esign_instance");
        String result = CDTMSAPI.getDataListInfo(token, "esign_instance", "obj.file_name='SHR7280-107_数据管理计划 Production V2.0 2025-06-09.pdf' ", "", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(result).size() > 0) {
            log.info(result);
        }
        token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "esign_file");
        result = CDTMSAPI.getDataListInfo(token, "esign_file", "obj.esign_instance_id='4110778372' ", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(result).size() > 0) {
            log.info(result);
        }


         token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "esign_engine");
         result = CDTMSAPI.getDataListInfo(token, "esign_engine", "", "", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(result).size() > 0) {
                log.info(result);
        }


        token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "study_rand_consistency");
        result = CDTMSAPI.getDataListInfo(token, "study_rand_consistency", "", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(result).size() > 0) {
            log.info(result);
        }
    }


    @Test
    public void tstAccountSignSheetToSignPage(){
        String filePath="C:\\Work\\随机化生成平台\\file\\随机化与研究药物分配申请表 - 药物分配管理 - 副本.xlsx";
        String newPath = fileUtils.addSuffixToFileName(filePath, "_signPage");
        fileUtils.extractFirstSheetByRemovingOthers(filePath,newPath);

        String command = "/home/<USER>/xlsx2pdf/venv/bin/python /home/<USER>/xlsx2pdf/xlsx2pdf.py /home/<USER>/8087/onlyOfficeFile/record/ " + newPath;
        CallPython.executeLatestFill(command);
        // 获取最后一个斜杠的位置
        int lastSlashIndex = newPath.lastIndexOf('/');
        // 获取文件名（带扩展名）
        String fileNameWithExtension = newPath.substring(lastSlashIndex + 1);
        // 获取不带扩展名的文件名
        String desiredString = fileNameWithExtension.substring(0, fileNameWithExtension.lastIndexOf('.'));
        String signFilePath = "/home/<USER>/8087/onlyOfficeFile/record/" + desiredString + ".pdf";
        //下载url pdf 合并

    }

    @Test
    public void combineTwoPdfs(){
        String pdf1Path = "C:\\Work\\随机化生成平台\\file\\SHR-4597-101_efb5feffb5ab51a4.pdf";
        String pdf2Path = "C:\\Users\\<USER>\\Documents\\WXWork\\1688857437402592\\Cache\\File\\2025-05\\documentexported(2).pdf";
        String outputPath = "C:\\Work\\随机化生成平台\\file\\SHR-4597-101_签字测试文件_combine.pdf";
        fileUtils.combinePdfFiles(pdf1Path,pdf2Path,outputPath);
    }

    @Test
    public void testDownloadFile(){
        String fileUrl="https://rtsmgenerator-tst.hengrui.com/tbAppParameter/downloadPdf?uuid=19b77f7f7dd724bd";
        String localFilePath="C:\\Work\\随机化生成平台\\file\\TST.pdf";
        try {
            fileUtils.downloadRTSMGenFile(fileUrl, localFilePath);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }


    @Test
    public void getTablesInfo(){
        try {
            // 尝试不同的原始编码格式
            BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream("c:/Users/<USER>/Desktop/json.txt"), "GBK"));
            StringBuilder content = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
            reader.close();

            // 写入文件，使用 UTF-8 编码
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream("c:/Users/<USER>/Desktop/json.txt"), "UTF-8"));
            writer.write(content.toString());
            writer.close();

            System.out.println("文件编码已转换为 UTF-8。");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }



    @Test
    public void testGetSignedFilePath(){
        String paths="2025076C87410C277F4D4B9B3D90D217D937BD.pdf||HRS-1893-202_空白病例报告表(单一册)_V2.1.pdf||||ecrf_v_doc,2025073BEB582D7A914A00BC9E169F065BB67F.pdf||HRS-1893-202_注释病例报告表(单一册)_V2.1.pdf||||annotcrf,2025075DA3581BC9D0433B9DA1CDEC6E0E8A3B.pdf||HRS-1893-202_空白病例报告表(全册)_V2.1.pdf||||fullecrf,2025075F75E4EAAFFA4B54B9F4107B01CA68B7.xlsx||HRS-1893-202_数据库定义报告_V2.1.xlsx||||note_ecrf,202507C207FB6385584F11BF100DC17DA4E8BD.xlsx||HRS-1893-202_eCRF审核记录表_V2.1.xlsx||||shpz";
        String path1 = "2025076C87410C277F4D4B9B3D90D217D937BD.pdf||HRS-1893-202_空白病例报告表(单一册)_V2.1.pdf||||ecrf_v_doc";
         String[] split = path1.split("\\|\\|");

        System.out.println(split.length);

    }


    @Test
    public void testGetSignedFilePath2(){
        String input="HRS-1738-101 临床研究方案 V1.0 clean.docx*2025026B2D50DF72E84456B2879E5118844DE3.docx|HRS-1738-101 临床研究方案 V1.0.docx|HRS-1738-101 临床研究方案 V1.0 clean.docx*202502ssx6B2D50DF72E84456B2879E5118844DE3.pdf|HRS-1738-101 临床研究方案 V1.0 track.docx*2026026B2D50DF72E84456B2879E5118844DE3.docx|HRS-1738-101 临床研究方案 V1.0 ctrack.docx*2023426B2D50DF72E84456B2879E5118844DE3.docx|";
        // Find the index of the clean.docx hash file
        Pattern pattern = Pattern.compile("clean\\.docx\\*([A-Z0-9]+\\.docx)");
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            int startIndex = matcher.start(1);  // Start index of the hash file
            int endIndex = matcher.end(1);      // End index of the hash file

            System.out.println("Hash file starts at index: " + startIndex);
            System.out.println("Hash file ends at index: " + endIndex);
            System.out.println("Hash file: " + input.substring(startIndex, endIndex));
        }
    }


    @Test
    public void testProcessMailFileName(){
        String input="SHR-8068-302 研究方案 V0.1 tracking版 final - QT.docx*202507AAE8C4A9BBBA4153B5527BED8484BC2B.docx|";
        Pattern pattern = Pattern.compile("\\.docx\\*([A-Z0-9]+\\.docx)");
        Matcher matcher = pattern.matcher(input);
        String ufn="";
        if (matcher.find()) {
            int startIndex = matcher.start(1);  // Start index of the hash file
            int endIndex = matcher.end(1);      // End index of the hash file
            ufn = input.substring(startIndex, endIndex);
            log.info(ufn);
        }else{
            log.info("No match found");
        }
    }


    @Test
    public void testProcessFileName(){
        String fileName="HR070803-307_随机化与研究药物分配申请表6.0.pdf";
        String fileName1="HR070803-307_随机化与研究药物分配申请表6.0-2023-12-31.pdf";
        String mailFileName= com.hengrui.blind_back.utils.FileUtils.processFileName(fileName);
        String mailFileName1= com.hengrui.blind_back.utils.FileUtils.processFileName(fileName1);
        System.out.println(mailFileName);
        System.out.println(mailFileName1);
    }


    @Test
    public void testProcessFileName2(){
        String dateStr = "16JUL25";
        DateTimeFormatter inputFormatter = new DateTimeFormatterBuilder()
                .parseCaseInsensitive()
                .appendPattern("ddMMMyy")
                .toFormatter(Locale.ENGLISH);
        LocalDate date = LocalDate.parse(dateStr, inputFormatter);
        String formatted = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        System.out.println(formatted);
        String newProtoolChartName="SHR1234"+"_ProTree_input"+"_"+formatted+".xlsx";
        System.out.println(newProtoolChartName);
    }



    @Test
    public void extractFileDate(){
        String fileName1="shr7280-108_m-20250702-0025_不良事件_20250715_0000_cn_en.zip";
        String fileName2="shr-4597-101_w-20250314-0001_既往及合并用药_20250729_1311_cn_en.zip";
        String fileName3="shr7280-108_m-20250702-0025_不良事件_20250715_0000_cn_en.zip";
        String fileName4="shr-4597-101_w-20250314-0001_既往及合并用药_20250729_1311_cn_en.zip";
        // Extract two datetime from filename
        String fileName =fileName4;
        String firstDateTime = "";
        String secondDateTime = "";

        // Updated pattern to match the actual filename format
        String pattern = ".*-(\\d{8})-(\\d{4})_.*_(\\d{8})_(\\d{4})_.*";
        Pattern regex = Pattern.compile(pattern);
        Matcher matcher = regex.matcher(fileName);

        if (matcher.matches()) {
            // First datetime: YYYYMMDD-HHMM format
            String firstDate = matcher.group(1); // 20250702
            String firstTime = matcher.group(2); // 0025
            firstDateTime = firstDate.substring(0, 4) + "-" +
                    firstDate.substring(4, 6) + "-" +
                    firstDate.substring(6, 8) + " " +
                    firstTime.substring(0, 2) + ":" +
                    firstTime.substring(2, 4);

            // Second datetime: YYYYMMDD_HHMM format
            String secondDate = matcher.group(3); // 20250715
            String secondTime = matcher.group(4); // 0000
            secondDateTime = secondDate.substring(0, 4) + "-" +
                    secondDate.substring(4, 6) + "-" +
                    secondDate.substring(6, 8) + " " +
                    secondTime.substring(0, 2) + ":" +
                    secondTime.substring(2, 4);

            log.info("First datetime: " + firstDateTime);  // 2025-07-02 00:25
            log.info("Second datetime: " + secondDateTime); // 2025-07-15 00:00
        } else {
            log.warn("Filename does not match expected pattern: " + fileName);
        }
    }


    @Test
    public void testExtractFileDate(){
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "esign_instance");
        String result = CDTMSAPI.getDataListInfo(token, "coding", "obj.file_name='SHR7280-107_数据管理计划 Production V2.0 2025-06-09.pdf' ", "", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(result).size() > 0) {
            log.info(result);
        }
    }

}



