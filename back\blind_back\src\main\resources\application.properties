server.port=8087
server.servlet.context-path=/sas_online
#preview-prfix=http://10.10.5.74
#preview-prfix=http://10.10.5.139
#preview-prfix=http://127.0.0.1
#preview-prfix=http://10.10.14.121
preview-prfix=http://************


mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.auto-mapping-behavior=full
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.mapper-locations=classpath*:/mapper/*.xml
logging.level.com.hengrui.blind_back.smo_data.mapper=TRACE
logging.level.org.apache.ibatis=debug



# ?????
spring.datasource.dynamic.primary=slave_3
spring.datasource.dynamic.strict=false
#spring.datasource.dynamic.datasource.master.driver-class-name=com.mysql.jdbc.Driver
#spring.datasource.dynamic.datasource.master.url=************************************************************************************************************ = Asia/Shanghai
##spring.datasource.dynamic.datasource.master.username=root
##spring.datasource.dynamic.datasource.master.password=123456
#spring.datasource.dynamic.datasource.master.username=root
#spring.datasource.dynamic.datasource.master.password=Hr@mysql1024

#spring.datasource.dynamic.datasource.slave_1.driver-class-name=com.mysql.jdbc.Driver
#spring.datasource.dynamic.datasource.slave_1.url=***************************************************************************************************************************
#spring.datasource.dynamic.datasource.slave_1.username=root
#spring.datasource.dynamic.datasource.slave_1.password=123456
#spring.datasource.dynamic.datasource.slave_1.username=root
#spring.datasource.dynamic.datasource.slave_1.password=Hr@mysql1024

#spring.datasource.dynamic.datasource.slave_2.url=jdbc:oracle:thin:@**********:1521:orcl
#spring.datasource.dynamic.datasource.slave_2.username=gousy
#spring.datasource.dynamic.datasource.slave_2.password=hrgsy@cdtms

spring.datasource.dynamic.datasource.slave_1.url=***************************************************************************************************************************
#spring.datasource.dynamic.datasource.slave_1.username=root
#spring.datasource.dynamic.datasource.slave_1.password=123456
spring.datasource.dynamic.datasource.slave_1.username=root
spring.datasource.dynamic.datasource.slave_1.password=Hr@mysql1024
spring.datasource.dynamic.datasource.slave_1.driver-class-name=com.mysql.jdbc.Driver


spring.datasource.dynamic.datasource.slave_2.url=***************************************
spring.datasource.dynamic.datasource.slave_2.username=weix5
spring.datasource.dynamic.datasource.slave_2.password=weix1207


#spring.datasource.dynamic.datasource.slave_2.url=jdbc:oracle:thin:@**********:1521:orcl
#spring.datasource.dynamic.datasource.slave_2.username=gousy
#spring.datasource.dynamic.datasource.slave_2.password=hrgsy@cdtms


spring.datasource.dynamic.datasource.slave_3.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.dynamic.datasource.slave_3.url=******************************************************************************************************************************
#spring.datasource.dynamic.datasource.slave_3.username=root
#spring.datasource.dynamic.datasource.slave_3.password=123456
spring.datasource.dynamic.datasource.slave_3.username=root
spring.datasource.dynamic.datasource.slave_3.password=Hr@mysql1024


spring.datasource.dynamic.datasource.slave_4.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.dynamic.datasource.slave_4.url=*********************************************************************************************************************************
spring.datasource.dynamic.datasource.slave_4.username=weix5
spring.datasource.dynamic.datasource.slave_4.password=weix@edc

spring.datasource.dynamic.datasource.slave_5.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.dynamic.datasource.slave_5.url=**********************************************************************************************************************************
spring.datasource.dynamic.datasource.slave_5.username=weix5
spring.datasource.dynamic.datasource.slave_5.password=weix@edc


spring.datasource.dynamic.datasource.slave6.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.dynamic.datasource.slave6.driver-class-name=com.clickhouse.jdbc.ClickHouseDriver
#spring.datasource.driver-class-name=ru.yandex.clickhouse.ClickHouseDriver
#SMO??????
spring.datasource.dynamic.datasource.slave6.url=******************************************
spring.datasource.dynamic.datasource.slave6.username=default
spring.datasource.dynamic.datasource.slave6.password=10101436
#SMO??????
#spring.datasource.dynamic.datasource.slave6.url=*****************************************
#spring.datasource.dynamic.datasource.slave6.username=hengrui
#spring.datasource.dynamic.datasource.slave6.password=1010542


# Increase file upload limits
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB
spring.servlet.multipart.enabled=true

spring.datasource.platform=mysql
spring.datasource.initialization-mode=always
spring.datasource.separator=;


#xxl.job.admin.addresses=http://***********:8088/xxl-job-admin
xxl.job.admin.addresses=http://************:8088/xxl-job-admin
xxl.job.executor.address:
xxl.job.executor.appname:xxl-job-executor-mileage
xxl.job.executor.ip:
xxl.job.executor.port:9999
xxl.job.executor.logpath:/data/xxl-job/jobhandler
xxl.job.executor.logretentiondays:-1
xxl.job.accessToken:default_token


