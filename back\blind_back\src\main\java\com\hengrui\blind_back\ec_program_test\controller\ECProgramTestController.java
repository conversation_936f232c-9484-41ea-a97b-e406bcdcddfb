package com.hengrui.blind_back.ec_program_test.controller;

import cn.hutool.json.JSONObject;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.MapUtils;
import com.hengrui.blind_back.blind.utils.Decode64Util;
import com.hengrui.blind_back.blind.utils.MinioUtil;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.ec_program_test.service.ECProgramTestService;
import com.hengrui.blind_back.ecrf_unlock.utils.FileUtil;
import com.hengrui.blind_back.question_summary.service.QuestionSumService;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.hengrui.blind_back.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName ECProgramTestController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/26 16:58
 * @Version 1.0
 **/
@RestController
@Slf4j
public class ECProgramTestController {
    @Autowired
    ECProgramTestService ecProgramTestService;
    @Autowired
    QuestionSumService questionSumService;

    @Autowired
    MinioUtil minioUtil;

    @Autowired
    Decode64Util decode64Util;


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getLogicCheckSetting")

    public Map<String, Object> getLogicCheckSetting(String taskId,
                                                  String server,
                                                  String projectId) {
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        ECProgramTestController.log.info("server is :" + server);
        Map<String, String> resultsA = ecProgramTestService.getLogicCheckSetting(taskId,projectId,"ec_doc","a".toString());
        Map<String, String> resultsB = ecProgramTestService.getTestDataSet(taskId,projectId,"test_data_files","a".toString());
        Map<String, String> resultsC = questionSumService.getQuestionsSumReport(taskId, projectId, "test_query_files",  "a".toString());
        //回填ectesting附件模板
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        String used_language = CDTMSAPI.getStudyLanguage(studyId);
        //中文使用中文模板，英文使用英文模板
        String templateName="";
        String ECTestFileName="";
        String ECTFileName="";
        String dataId="";
        if(used_language.equals("CH")){
            ECTestFileName="/home/<USER>/8087/DVS/"+studyId+"_逻辑核查测试报告.xlsx";
            ECTFileName=studyId+"_逻辑核查测试报告.xlsx";
            templateName="/home/<USER>/8087/DVS/DVSTemplate/逻辑核查测试报告-CN.xlsx";
        }else{
            ECTestFileName="/home/<USER>/8087/DVS/"+studyId+"_Edit Check Testing Report.xlsx";
            ECTFileName=studyId+"_Edit Check Testing Report.xlsx";
            templateName="/home/<USER>/8087/DVS/DVSTemplate/逻辑核查测试报告-EN.xlsx";
        }



        String data = formInfo.get("param");
        log.info("获取到的表单信息为:{}", data);
        String bbh="";
        String edit_check_prog_date="";
        if (!data.isEmpty()) {
            JSONObject formInfoData = new JSONObject(data);
            dataId = formInfoData.get("id").toString();
            bbh = formInfoData.get("study_dvs_ver").toString();
            edit_check_prog_date= formInfoData.get("edit_check_prog_date").toString();
        }
        String dm = CDTMSAPI.getDMName(studyId);
        String pdm = CDTMSAPI.getTDMName(studyId);
        String mdm = CDTMSAPI.getMDMName(studyId);
        Map<String, Object> map = MapUtils.newHashMap();
        map.put("caseNum", studyId);
        map.put("DVSVersionNum", bbh);
        map.put("PDM", pdm);
        map.put("MDM", mdm);
        map.put("DVSVersionDate", edit_check_prog_date);
        map.put("DM", dm);
        EasyExcel.write(ECTestFileName).withTemplate(templateName).sheet(0).doFill(map);
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        FileUtil.uploadSASOutputFile(dataId,formId, "ectesting", ECTestFileName, "edit_check_prog", SASOnlieConstant.REMOTE_SERVER_API_PREFIX,ECTFileName,"xlsx");

        File file = new File(ECTestFileName);
        String md5 = decode64Util.getMd5(file);
        minioUtil.uploadNormalFile(file,md5,studyId,"EC","eccheck",ECTFileName);
        //CDTMSAPI.updateWorkFlowStatus(taskId,projectId,"crf_zt","01");
        ECProgramTestController.log.info("获取到的文件名的类型为：" + "a");
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", resultsA);
        return result;
    }



    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getDVS")

    public Map<String, Object> getDVS(String taskId,
                                                    String server,
                                                    String projectId) {
        ECProgramTestController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> resultA = ecProgramTestService.getDVS(taskId,projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", resultA);
        return result;
    }


}
