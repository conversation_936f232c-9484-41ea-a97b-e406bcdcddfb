package com.hengrui.blind_back.parse_excel_toDB.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.hengrui.blind_back.parse_excel_toDB.entity.DBDefineEntity;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;

@Component
@Mapper
@Repository
@DS("slave_3")
public interface ParseExcelToDBMapper {
    int insertDatatoECRFDB(List<DBDefineEntity> dbDefineEntityList);
}
