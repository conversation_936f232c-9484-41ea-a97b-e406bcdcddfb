package com.hengrui.blind_back.blind.utils;

import org.springframework.stereotype.Component;

import java.io.*;

@Component
public class SASIOM {
    public  String readSAS(String path,String jsonPath,String CSVPath,String projectName) throws IOException {
        String replaceStr1 = "&jsonpath.";
        String replaceStr2 = "&rawpath.";
        String replaceStr3 = "&studyname.";

        File file = new File(path);
        InputStreamReader in = new InputStreamReader ( new FileInputStream(file),"GBK");
        BufferedReader bufIn = new BufferedReader(in);

//        NtlmPasswordAuthentication auth = new NtlmPasswordAuthentication(USER_DOMAIN, USER_ACCOUNT, USER_PWS);
//        SmbFile remoteFile = new SmbFile(path, auth);
//        InputStreamReader in = new InputStreamReader ( new SmbFileInputStream(remoteFile),"GBK");
//        BufferedReader bufIn = new BufferedReader(in);

        // 替换
        String line = "";
        StringBuilder stringBuffer = new StringBuilder();
        while ((line = bufIn.readLine()) != null) {
            line = line.replaceAll(replaceStr1, jsonPath).replaceAll(replaceStr2,CSVPath).replaceAll(replaceStr3,projectName);
            stringBuffer.append(line);
        }
        return stringBuffer.toString();
    }
}
