package com.hengrui.blind_back.ecrf_unlock.service.impl;

import com.hengrui.blind_back.blind.constant.BlindConstant;
import com.hengrui.blind_back.blind.entity.CSVTableDataEntity;
import com.hengrui.blind_back.blind.mapper.EDMCDTMSInfoMapper;
import com.hengrui.blind_back.blind.utils.Decode64Util;
import com.hengrui.blind_back.blind.utils.MinioUtil;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.ecrf_unlock.mapper.ECRFUnlockMapper;
import com.hengrui.blind_back.ecrf_unlock.service.ECRFUnLockService;
import com.hengrui.blind_back.ecrf_unlock.utils.EcrfSASTrigger;
import com.hengrui.blind_back.ecrf_unlock.utils.FileUtil;
import com.sas.services.connection.ConnectionFactoryException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.*;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName ECRFUnLockServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/10 13:53
 * @Version 1.0
 **/
@Slf4j
@Service
public class ECRFUnLockServiceImpl implements ECRFUnLockService {

    @Autowired
    MinioUtil minioUtil;

    @Autowired
    FileUtil fileUtil;

    @Autowired

    Decode64Util decode64Util;


    @Autowired
    ECRFUnlockMapper ecrfUnlockMapper;

    @Autowired
    EDMCDTMSInfoMapper edmcdtmsInfoMapper;


    @Autowired
    EcrfSASTrigger sasTrigger;

    @Override
    public CSVTableDataEntity getCurrentData(String fileId) {
        return null;
    }


    /*
     * <AUTHOR>
     * @Description //TODO 提交json参数到SAS服务器
     * @Date 13:39 2024/4/11
     * @Param
     * @param null
     * @return
     * @return null
     **/

    @Override
    public Map<String, String> submitToSASECRF(String param, String studyId, String userName, String submitDate, String taskId) {
        //init the API return result
        Map<String, String> submitResult = new HashMap<>();
        //the specific content in the result
        String operateId = "";
        String fileName = "";
        String sasLogPath = "";
        String currentTime = ULIDGenerator.getCurrentTime();
        //1.将param 存到 json文件，并上传到minio
        try {
            param = URLDecoder.decode(param, "UTF-8");
            taskId = URLDecoder.decode(taskId, "UTF-8");
            studyId = URLDecoder.decode(studyId, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        //先把除了file_id以外的数据存储到历史操作记录中
        String recordsAdd = insertBlindRecords(param, userName, studyId);
        if (!ObjectUtils.isEmpty(recordsAdd) && !recordsAdd.isEmpty()) {
            String[] s = recordsAdd.split("_");
            //在此生成uuid
            operateId = s[0];
            fileName = studyId + "_" + operateId + ".json";
        }
        String jsonMinioPath = "ecrfunlock/" + fileName;
        //json文件存储sas网盘位置
        String paramsPath = BlindConstant.SAS_JSON_PATH + BlindConstant.FILE_SEPARATOR + fileName;
        //将json参数文件上传到sas服务器读取的指定位置
        // 标记文件生成是否成功
        boolean flag = true;
        try {
            // 保证创建一个新文件
            File file = new File(paramsPath);
            if (!file.getParentFile().exists()) {
                // 如果父目录不存在，创建父目录
                file.getParentFile().mkdirs();
            }
            if (file.exists()) { // 如果已存在,删除旧文件
                file.delete();
            }
            file.createNewFile();
            // 将格式化后的字符串写入文件
            Writer write = new OutputStreamWriter(new FileOutputStream(file), "UTF-8");
            write.write(param);
            write.flush();
            write.close();
            String md5 = decode64Util.getMd5(file);
            minioUtil.uploadObject(file, md5, fileName);
        } catch (Exception e) {
            flag = false;
            log.info("json paramter file upload minio failed,please check and the exception time is :" + currentTime);
            //update the error operation record into table
            ecrfUnlockMapper.updateUnlockFailRecords(operateId, "json paramter file upload minio failed", "");
            e.printStackTrace();
        }
        //2.调用sas程序，提供json文件路径
        if (flag == true) {
            String sasCodePath = BlindConstant.SAS_CODE_PATH;
            Map<String, Object> sasCallResult = new HashMap<>();
            submitResult.put("studyId",studyId);
            submitResult.put("jsonMinioPath", SASOnlieConstant.PREFIX_PRO_MINIO+"/" + jsonMinioPath);
            sasCallResult = sasTrigger.runCreateSAS(sasCodePath,submitResult);
            sasLogPath = sasCallResult.get("sasLogPath").toString();
            //record this success sas call operation
            ecrfUnlockMapper.updateUnlockSuccessRecords(operateId, SASOnlieConstant.PREFIX_PRO_MINIO+"/" + jsonMinioPath, paramsPath, sasCallResult.get("logPath").toString());
        }
        String outputName = studyId + "_eCRF_unlock_check.xlsx";
        //3.download the output file after sas call which is from minio
        boolean isSuccess = minioUtil.downloadGetObject("dmreview", outputName);
        if (isSuccess) {
            String formId = FileUtil.getFormIdByTaskId(taskId);

            String newFileName = FileUtil.getDownloadFileName(BlindConstant.SAS_BLIND_LOG_PATH + System.getProperty("file.separator") + outputName);
            String result = FileUtil.uploadSASOutputFile(taskId, formId, "",
                    BlindConstant.SAS_BLIND_LOG_PATH + System.getProperty("file.separator") + outputName,
                    SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.REMOTE_SERVER_API_PREFIX,newFileName,"ecrf_unlock");
            if (!result.equals("fail")) {
                submitResult.put("sasCallResult", "success");
                submitResult.put("sasLogPath", sasLogPath);
            } else {
                submitResult.put("sasCallResult", "fail");
                submitResult.put("sasLogPath", sasLogPath);
            }
        } else {
            //4.返回调用结果
            submitResult.put("sasCallResult", "fail");
            submitResult.put("sasLogPath", sasLogPath);
        }
        return submitResult;
    }

    public String insertBlindRecords(String param, String userName, String studyId) {
        int result = 0;
        //新增编盲记录
        String id = ULIDGenerator.generateULID();
        //非空校验
        if (!param.isEmpty() && !userName.isEmpty() && !studyId.isEmpty()) {
            String fileName = studyId + "_" + id + ".json";
            result = ecrfUnlockMapper.insertUnlockRecords(id, param, userName, fileName);
        }
        return id + "_" + result;
    }


    @Override
    public String uploadUnLockData(String taskId, String projectId, String fid) {
        //1.download file from cdtms and make it to be the local file
        String resultPath = "";
        try {
            resultPath = fileUtil.uploadLocalFileToMinio(taskId, projectId, fid,".zip");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        //2.upload this file to minio
        return resultPath;
    }

    @Override
    public String getEDCName(String taskId,String projectId) {
        //先获取studyId
        String studyId = FileUtil.getStudyIdByTaskId(taskId,projectId);
        if (!studyId.isEmpty()) {
            //返回该EDCName 的类型
            return edmcdtmsInfoMapper.getEDCName(studyId);
        } else {
            return "unknown";
        }
    }

}
