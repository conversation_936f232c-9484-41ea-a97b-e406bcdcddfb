package com.hengrui.blind_back.rtsm.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Component
@Mapper
@Repository
@DS("slave_5")
public interface RTSMFileNameMapper {


    List<String> getMedicalFileName(String fileTableName, String medTableName);

    List<String>  getRandFileName(String fileTableName,String randTableName);

    Map<String,String> getRandBlindFilePath(String studyId);

    Map<String,String> getMedBlindFilePath(String studyId);

    Map<String,String> getMedTrailFilePath(String studyId);

    Map<String,String> getSubjectTrailPath(String studyId);

    Map<String,String> getWayBillTrailPath(String studyId);
}
