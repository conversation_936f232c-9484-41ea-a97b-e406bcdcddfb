package com.hengrui.blind_back.test_file.service.impl;

import com.hengrui.blind_back.blind.constant.BlindConstant;
import com.hengrui.blind_back.blind.utils.MinioUtil;
import com.hengrui.blind_back.test_file.constant.TestFileConstant;
import com.hengrui.blind_back.test_file.service.TestFileService;
import com.hengrui.blind_back.test_file.utils.ContentTypeUtil;
import com.hengrui.blind_back.test_file.utils.SASForDtaTrigger;
import com.sas.iom.SASIOMDefs.GenericError;
import com.sas.services.connection.ConnectionFactoryException;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;

@Service
public class TestFileServiceImpl implements TestFileService {

    @Autowired
    SASForDtaTrigger sasForDtaTrigger;

    @Autowired
    MinioUtil minioUtil;

    @Autowired
    ContentTypeUtil contentTypeUtil;

    @Override
    public String testFileExecute(String fileUrl, String StudyName) {
        //1.根据参参数下载dta文件
        URL url = null;
        try {
            url = new URL(fileUrl);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
        String fileName = null;
        try {
            fileName = URLDecoder.decode(fileUrl, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        String[] split = fileName.split("/");
        fileName = split[split.length - 1];
        String tempFileName = TestFileConstant.File_Path + fileName;
        File temp = new File(tempFileName);
        try {
            FileUtils.copyURLToFile(url, temp);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        //处理文件路径，将文件分隔符转为linux的，并移除根目录
        tempFileName = tempFileName.replaceAll("\\\\", "/");
        tempFileName = tempFileName.substring(tempFileName.indexOf(":") + 2);
        //将下载好的文件直接存储到网盘对应的位置
        String dtaFilePath = "SHR-1210-II-218_ADA_V2.0_20230915_clean-测试用-中文.docx";
        dtaFilePath = dtaFilePath.substring(0, dtaFilePath.indexOf(".docx"));
        //2.trigger sas program,pass the sas code file path and dta file path
        try {
            sasForDtaTrigger.runCreateSAS(TestFileConstant.SAS_Code_Path, dtaFilePath, StudyName);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (GenericError e) {
            throw new RuntimeException(e);
        } catch (ConnectionFactoryException e) {
            throw new RuntimeException(e);
        }
        //3.到minio上下载运行后的结果文件,第一个参数是minio上对应的文件路径，第二个参数是下载到本地的文件路径
        minioUtil.downloadObject(fileName, BlindConstant.EDM_UAP_File_Path + File.separator + fileName);
        //5.返回下载文件的地址
        return null;
    }

    @Override
    public void downloadFile(String filename, HttpServletResponse response) {
        //文件路径
        String filepath = "C:\\MyFile\\external_data\\dta_file\\";
        try {
            File file = new File(filepath + filename);
            FileInputStream inputStream = new FileInputStream(file);
            ServletOutputStream outputStream = response.getOutputStream();

            //响应文件格式
            response.setContentType(contentTypeUtil.getContentType(contentTypeUtil.getSuffix(filename)));
            response.setContentLengthLong(file.length());

            int len = 0;
            byte[] bytes = new byte[1024];
            while ((len = inputStream.read(bytes)) != -1) {
                //读取输出流
                outputStream.write(bytes, 0, len);
            }
            outputStream.flush(); //刷新
            outputStream.close();
            inputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
