package com.hengrui.blind_back.blind.constant;

public class BlindConstant {

        public static String[] EMAIL_LIST = {"<EMAIL>", "<EMAIL>"};
        public static final String MAIL_FROM = "<EMAIL>";
        public static final String MAIL_PASSWORD = "Zh654321";
        public static final String MAIL_ACCOUNT ="<EMAIL>" ;
        public static final String MAIL_TITLE_PARTONE = "通知【";
        public static final String EDM_MAIL_TITLE_PARTTWO = "】遮盲数据已生成 ";

        public static final String EDMQC_MAIL_TITLE_PARTTWO = "】遮盲数据已完成QC ";

        public static final String MEMBER_MAIL_TITLE_PARTTWO = "】项目遮盲数据已生成 ";

        // 指定邮件的内容
        public static String EDM_EMAIL_CONTENT_PART_ONE = "Dear Qcer：<br />" ;
        public static String EDM_EMAIL_CONTENT_PART_TWO =
                "项目遮盲数据已生成，请及时对数据进行QC并反馈。谢谢！<br />" +
                "文件名：" ;
        public static String EDMQC_EMAIL_CONTENT_PART_ONE = "Dear EDM：<br />"+"您提交的";

        public static String EDMQC_EMAIL_CONTENT_PART_TWO = "项目遮盲数据已完成QC，QC记录如下：<br />"+"文件名：";


        public static String EDMQC_EMAIL_CONTENT_PART_THREE ="<br />"+"QC结果： ";


        public static String MEMBER_EMAIL_CONTENT_PART_ONE = "Dear Colleague：<br />"+"附件为";

        public static String MEMBER_EMAIL_CONTENT_PART_TWO = "项目遮盲后数据，请及时查收。同时请验证遮盲文件MD5码是否一致，如有问题，请及时反馈。<br />"+"文件名：";
        public static String PASSWORD_EMAIL_CONTENT_PART_TWO = "项目遮盲后数据解压密码，请及时查收。如有问题，请及时反馈。<br />"+"文件名：";
        public static String MEMBER_EMAIL_CONTENT_PART_THREE ="<br />"+"MD5码：";

        public static String SIGN_PART_CONTENT ="<br />"+"临床数据科学中心"+"<br />"+"江苏恒瑞医药股份有限公司"+"<br />"+"电话：";





//        public static  String  BLINDED_CSV_FOLDER="C:\\MyFile\\external_data\\edm_uap\\dtattach";

//        public static  String  BLINDED_COMPARE_FOLDER="C:\\MyFile\\external_data\\compare_file";

        public static  String  BLINDED_COMPARE_FOLDER="/home/<USER>/ex_check_xlsx";



        public static  String  BLINDED_CSV_FOLDER="/home/<USER>/ex_blinded_csv";

//        public static  String  EDM_UAP_File_Path="/home/<USER>/edm_uap_file";

        public static  String  EDM_UAP_File_Path="/home/<USER>/ex_original_csv";



//        public static  String  EDM_UAP_File_Path="C:\\MyFile\\external_data\\edm_uap\\dtattach";
//
//        public static  String  Params_Path="C:\\MyFile\\external_data\\";

        public static  String  Params_Path="/home/<USER>/ex_md5_txt/";



        //邮件发送端口
        public static String MAIL_HOST = "mail.hengrui.com";

        public static int MAIL_PORT = 587;
        //权限校验
        public static boolean MAIL_AUTH = true;

        //本地sas代码路径
//        public static String SAS_CODE_PATH="C:\\MyFile\\external_data\\sas_code\\m_edm_blind_output.txt";
           public static String SAS_CODE_PATH="/home/<USER>/ex_sascode/m_edm_blind_output.txt";

      //  public static String SAS_CODE_PATH="C:\\Users\\<USER>\\Desktop\\pgmout_uat.txt";

        //遮盲文件参数根目录
//        public static String SAS_ORA_CSV_PATH="W:\\Projects\\dev\\GRP_CDSC_liz119\\URS_EDM\\doc\\EDM\\csv_raw";

        public static String SAS_ORA_CSV_PATH="/home/<USER>/ ";

       // public static String SAS_JSON_PATH="C:\\Work\\ecrf-unlock\\json";


//        public static String SAS_JSON_PATH="W:\\Projects\\dev\\GRP_CDSC_liz119\\URS_EDM\\doc\\EDM\\json_blind";

     public static String SAS_JSON_PATH="/home/<USER>/ex_json_file";
//
//        public static String SAS_BLIND_LOG_PATH="/home/<USER>/blind_sas_log/";
//        public static String SAS_BLIND_LOG_PATH="W:\\Projects\\dev\\GRP_CDSC_liz119\\URS_EDM\\blind_sas_log\\";
//        public static String SAS_BLIND_LOG_PATH="C:\\Users\\<USER>\\Desktop";
        public static String SAS_BLIND_LOG_PATH="/home/<USER>/8087/sas_call_log";

        public static String FILE_SEPARATOR = System.getProperty("file.separator");

//        public static String EDM_DOWNLOAD_URL="https://cdtms-tst.hengrui.com:82/edmfile.download.do?studycode=";
        public static String EDM_DOWNLOAD_URL="https://clinical.hengruipharma.com:1818/edmfile.download.do?studycode=";
        //文件预览地址栏前缀
//        public static String COMPARE_URL_PREFIX="http://localhost:8087/external_data/file/";
//        public static String COMPARE_URL_PREFIX="https://externalBlind-tst.hengrui.com/external_data/file/";

        public static String COMPARE_URL_PREFIX="https://externalBlind.hengrui.com/external_data/file/";

        //文件预览地址栏前缀
        public static String FILE_URL_PREFIX="https://externalblindcheck.hengrui.com/onlinePreview?url=";
//        public static String FILE_URL_PREFIX="https://externalblindcheck-tst.hengrui.com/onlinePreview?url=";

//        public static String FILE_URL_PREFIX="http://127.0.0.1:8012/onlinePreview?url=";

        public static String ECRF_DOWNLOAD_URL="https://cdtms.hengrui.com/usersyn/download?tableid=";




        public static String UAP_API_PRFIX="https://meduap-tst.hengrui.com:8082/";


//        public static String SAS_SERVER_URL="sas-hrsh-node1.hengrui.com";

        public static String SAS_SERVER_URL="sas-hrsh.hengrui.com";
        public static String ECRF_UNLOCK_DOWNLOAD_URL="https://cdtms.hengrui.com:443/remoteButtonTask/download?taskId=";

        public static  String  ECRF_UAP_File_Path="C:\\Work\\ecrf-unlock\\dataset\\";

        public static  String  TABLE_ID="ecrf_build1";

//        public static  String  TABLE_ID="cdtmsen_val";


}


