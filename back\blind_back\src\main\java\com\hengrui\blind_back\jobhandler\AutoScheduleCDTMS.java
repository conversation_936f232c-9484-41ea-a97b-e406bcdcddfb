/*
package com.hengrui.blind_back.jobhandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hengrui.blind_back.blind.mapper.BlindBackMapper;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

*/
/**
 * @ClassName AutoScheduleCDTMS
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/10 9:10
 * @Version 1.0
 **//*


@Component
@Slf4j
public class AutoScheduleCDTMS {

    @Autowired
    BlindBackMapper blindBackMapper;
    
    

    @XxlJob("scheduleReview")
    @Transactional
    public void scheduleCDTMSHandler() throws Exception {
        AutoScheduleCDTMS.log.info("--------------------------------调度任务已经启动-----------------");
        //获取调度业务表中的表名和对应的表单数据
        List<Map<String, String>> records = blindBackMapper.getScheduleReviewRecord();
        for (Map<String, String> stringStringMap : records) {
            //获取调度频率
            String frequence=stringStringMap.get("remark");
            //遍历有关定期审核节点的调度设置
            JSONObject param = JSON.parseObject(stringStringMap.get("param"));
            String type = param.get("type").toString();
            //创建定期审核的记录
            String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "crf_handover");
            String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
            String studyId =stringStringMap.get("studyId");
            //根据表单接口，查询对应的项目的studyId 整数值
            String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            long finalStudyId =  Long.parseLong(studyInt);
            AutoScheduleCDTMS.log.info("--------------------------------定期审核的studyId是:"+finalStudyId);
            param.put("study_id", studyInt);
            if(type.equals("2")||type.equals("3")){
                //存储数据截至和开始日期
                LocalDate currentDate = LocalDate.now();
                LocalDate oneWeekAgo = currentDate.minusWeeks(1); // 获取一周前的日期
                LocalDate twoWeekAgo = currentDate.minusWeeks(2); // 获取两周前的日期
                LocalDate oneMonthAgo = currentDate.minusMonths(1); // 获取一月前的日期
                LocalDate threeMonthAgo = currentDate.minusMonths(3); // 获取三月前的日期
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                String formattedDate = currentDate.format(formatter);
                String formattedOneWeekAgo = oneWeekAgo.format(formatter); //一周前
                String formattedTwoWeekAgo = twoWeekAgo.format(formatter); // 两周前
                String formattedOneMonthAgo = oneMonthAgo.format(formatter); // 一月前
                String formattedThreeMonthAgo = threeMonthAgo.format(formatter); //三月前
                param.put("data_deadline_date", formattedDate);
                param.put("data_deadline_date2", formattedDate);
                //根据调度频率，计算开始日期
                switch (frequence) {
                    case "qw":
                        param.put("data_start_date", formattedOneWeekAgo);
                        break;
                    case "q2w":
                        param.put("data_start_date", formattedTwoWeekAgo);
                        break;
                    case "qm":
                        param.put("data_start_date", formattedOneMonthAgo);
                        break;
                    case "q3m":
                        param.put("data_start_date", formattedThreeMonthAgo);
                        break;
                    case "PRN":
                        break;
                    default:
                        break;
                }

            }
            //创建新审核记录
            String saveRecord = CDTMSAPI.usersyndataSave(token, "study_regular_review", formId, "", "", param.toString());
            long irecordId= Long.parseLong(JSONObject.parseObject(saveRecord).get("id").toString());
            AutoScheduleCDTMS.log.info("--------------------------------定期审核的记录id是:"+irecordId);
            String result = CDTMSAPI.getDataListInfo(token, "study_regular_review", "obj.study_id='" + finalStudyId + "'" + "and obj.id='" + irecordId + "'", "edit", "");
            //查询uuid
            String uuid = JSONArray.parseArray(result).getJSONObject(0).get("uuid").toString();
            AutoScheduleCDTMS.log.info("--------------------------------定期审核的记录id是:"+uuid);
            //调用定期审核python接口
            CDTMSAPI.callScheduleReview(SASOnlieConstant.EDC_API,"uuid_"+uuid,"账户审核");

        }


    }


    @XxlJob("queryReview")
    @Transactional
    public void queryReviewHandler() throws Exception {
        AutoScheduleCDTMS.log.info("--------------------------------调度任务已经启动-----------------");
        //获取调度业务表中的表名和对应的表单数据
        List<Map<String, String>> records = blindBackMapper.getQueryReviewRecord();
        for (Map<String, String> stringStringMap : records) {
            //获取调度频率
            String frequence=stringStringMap.get("remark");
            //遍历有关定期审核节点的调度设置
            JSONObject param = JSON.parseObject(stringStringMap.get("param"));
            String type = param.get("type").toString();
            //创建定期审核的记录
            String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "crf_handover");
            String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
            String studyId =stringStringMap.get("studyId");
            //根据表单接口，查询对应的项目的studyId 整数值
            String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            long finalStudyId =  Long.parseLong(studyInt);
            AutoScheduleCDTMS.log.info("--------------------------------定期审核的studyId是:"+finalStudyId);
            param.put("study_id", studyInt);
            if(type.equals("2")||type.equals("3")){
                //存储数据截至和开始日期
                LocalDate currentDate = LocalDate.now();
                LocalDate oneWeekAgo = currentDate.minusWeeks(1); // 获取一周前的日期
                LocalDate twoWeekAgo = currentDate.minusWeeks(2); // 获取两周前的日期
                LocalDate oneMonthAgo = currentDate.minusMonths(1); // 获取一月前的日期
                LocalDate threeMonthAgo = currentDate.minusMonths(3); // 获取三月前的日期
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                String formattedDate = currentDate.format(formatter);
                String formattedOneWeekAgo = oneWeekAgo.format(formatter); //一周前
                String formattedTwoWeekAgo = twoWeekAgo.format(formatter); // 两周前
                String formattedOneMonthAgo = oneMonthAgo.format(formatter); // 一月前
                String formattedThreeMonthAgo = threeMonthAgo.format(formatter); //三月前
                param.put("data_deadline_date", formattedDate);
                param.put("data_deadline_date2", formattedDate);
                //根据调度频率，计算开始日期
                switch (frequence) {
                    case "qw":
                        param.put("data_start_date", formattedOneWeekAgo);
                        break;
                    case "q2w":
                        param.put("data_start_date", formattedTwoWeekAgo);
                        break;
                    case "qm":
                        param.put("data_start_date", formattedOneMonthAgo);
                        break;
                    case "q3m":
                        param.put("data_start_date", formattedThreeMonthAgo);
                        break;
                    case "PRN":
                        break;
                    default:
                        break;
                }

            }
            //创建新审核记录
            String saveRecord = CDTMSAPI.usersyndataSave(token, "study_regular_review", formId, "", "", param.toString());
            long irecordId= Long.parseLong(JSONObject.parseObject(saveRecord).get("id").toString());
            AutoScheduleCDTMS.log.info("--------------------------------定期审核的记录id是:"+irecordId);
            String result = CDTMSAPI.getDataListInfo(token, "study_regular_review", "obj.study_id='" + finalStudyId + "'" + "and obj.id='" + irecordId + "'", "edit", "");
            //查询uuid
            String uuid = JSONArray.parseArray(result).getJSONObject(0).get("uuid").toString();
            AutoScheduleCDTMS.log.info("--------------------------------定期审核的记录id是:"+uuid);
            //调用定期审核python接口
            CDTMSAPI.callScheduleReview(SASOnlieConstant.EDC_API,"review_uuid_"+uuid,"质疑审核");

        }


    }


    @XxlJob("trailReview")
    @Transactional
    public void trailReviewCDTMSHandler() throws Exception {
        AutoScheduleCDTMS.log.info("--------------------------------调度任务已经启动-----------------");
        //获取调度业务表中的表名和对应的表单数据
        List<Map<String, String>> records = blindBackMapper.getTrailReviewRecord();
        for (Map<String, String> stringStringMap : records) {
            //获取调度频率
            String frequence=stringStringMap.get("remark");
            //遍历有关定期审核节点的调度设置
            JSONObject param = JSON.parseObject(stringStringMap.get("param"));
            String type = param.get("type").toString();
            //创建定期审核的记录
            String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "crf_handover");
            String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
            String studyId =stringStringMap.get("studyId");
            //根据表单接口，查询对应的项目的studyId 整数值
            String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            long finalStudyId =  Long.parseLong(studyInt);
            AutoScheduleCDTMS.log.info("--------------------------------定期审核的studyId是:"+finalStudyId);
            param.put("study_id", studyInt);
            if(type.equals("2")||type.equals("3")){
                //存储数据截至和开始日期
                LocalDate currentDate = LocalDate.now();
                LocalDate oneWeekAgo = currentDate.minusWeeks(1); // 获取一周前的日期
                LocalDate twoWeekAgo = currentDate.minusWeeks(2); // 获取两周前的日期
                LocalDate oneMonthAgo = currentDate.minusMonths(1); // 获取一月前的日期
                LocalDate threeMonthAgo = currentDate.minusMonths(3); // 获取三月前的日期
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                String formattedDate = currentDate.format(formatter);
                String formattedOneWeekAgo = oneWeekAgo.format(formatter); //一周前
                String formattedTwoWeekAgo = twoWeekAgo.format(formatter); // 两周前
                String formattedOneMonthAgo = oneMonthAgo.format(formatter); // 一月前
                String formattedThreeMonthAgo = threeMonthAgo.format(formatter); //三月前
                param.put("data_deadline_date", formattedDate);
                param.put("data_deadline_date2", formattedDate);
                //根据调度频率，计算开始日期
                switch (frequence) {
                    case "qw":
                        param.put("data_start_date", formattedOneWeekAgo);
                        break;
                    case "q2w":
                        param.put("data_start_date", formattedTwoWeekAgo);
                        break;
                    case "qm":
                        param.put("data_start_date", formattedOneMonthAgo);
                        break;
                    case "q3m":
                        param.put("data_start_date", formattedThreeMonthAgo);
                        break;
                    case "PRN":
                        break;
                    default:
                        break;
                }

            }
            //创建新审核记录
            String saveRecord = CDTMSAPI.usersyndataSave(token, "study_regular_review", formId, "", "", param.toString());
            long irecordId= Long.parseLong(JSONObject.parseObject(saveRecord).get("id").toString());
            AutoScheduleCDTMS.log.info("--------------------------------定期审核的记录id是:"+irecordId);
            String result = CDTMSAPI.getDataListInfo(token, "study_regular_review", "obj.study_id='" + finalStudyId + "'" + "and obj.id='" + irecordId + "'", "edit", "");
            //查询uuid
            String uuid = JSONArray.parseArray(result).getJSONObject(0).get("uuid").toString();
            AutoScheduleCDTMS.log.info("--------------------------------定期审核的记录id是:"+uuid);
            //调用定期审核python接口
            CDTMSAPI.callScheduleReview(SASOnlieConstant.EDC_API,"'review_uuid_"+uuid,"稽查轨迹");

        }


    }

}
*/
