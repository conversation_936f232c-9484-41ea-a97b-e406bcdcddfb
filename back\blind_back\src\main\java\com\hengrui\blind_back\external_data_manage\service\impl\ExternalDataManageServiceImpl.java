package com.hengrui.blind_back.external_data_manage.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.ecrf_unlock.utils.FileUtil;
import com.hengrui.blind_back.external_data_manage.service.ExternalDataManageService;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.hengrui.blind_back.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName ExternalDataManageServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/13 11:13
 * @Version 1.0
 **/
@Service
@Slf4j
public class ExternalDataManageServiceImpl  implements ExternalDataManageService {

    @Autowired
    FileUtils fileUtils;

    @Override
    public Map<String, Object> getEDMTrainRecord(String taskId, String projectId) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String tableId = "";
        String recordId = "";
        if (!ObjectUtils.isEmpty(formInfo.get("recordId"))) {
            recordId = formInfo.get("recordId");
            tableId = formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String params = formInfo.get("param");
        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(params);
        String uuid = jsonObject.get("uuid").toString();
        String studyId =  jsonObject.get("study_id").toString();
        String dataId = jsonObject.get("id").toString();
        String email=jsonObject.get("email").toString();
        Map<String,String> accountInfo=new HashMap<>();
        String version="";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "study_partner_contacts");

        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if(com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size()>0){
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "study_partner_contacts", "obj.email='"+email+"' and obj.edm_tr_file!=null and transfer_way='2'  ", "edit", "obj.date desc");
            JSONArray objects = JSON.parseArray(result);
            if(objects.size()>0){
                for(int i=0;i<objects.size();i++){
                    //调用模板提取接口
                    JSONObject object = JSONObject.parseObject(objects.get(i).toString());
                    String edmTrFile = fileUtils.extractFileName(object.get("edm_tr_file").toString());
                    if(!ObjectUtils.isEmpty(edmTrFile)&&!edmTrFile.isEmpty()&&edmTrFile.contains(".pdf")){
                        try {
                            String regex = "\\*([A-Z0-9]+\\.pdf)\\|";
                            Pattern pattern = Pattern.compile(regex);
                            Matcher matcher = pattern.matcher(object.get("edm_tr_file").toString());
                            String ufn = "";
                            if (matcher.find()) {
                                ufn = matcher.group(1);
                                log.info(ufn);
                                //下载这个文件
                                CDTMSAPI.downloadDataByUserSync("study_partner_contacts",token,ufn, SASOnlieConstant.SAS_DATA_LOCAL_FOLDER+edmTrFile);
                                //上传到这个表单的附件字段
                                formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
                                FileUtil.uploadSASOutputFile(taskId, formId, "edm_tr_file", SASOnlieConstant.SAS_DATA_LOCAL_FOLDER+edmTrFile, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.REMOTE_SERVER_API_PREFIX, edmTrFile, "pdf");
                            } else {
                                log.info("No match found");
                            }

                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                        break;
                    }

                }




            }
        }
        return null;
    }
}
