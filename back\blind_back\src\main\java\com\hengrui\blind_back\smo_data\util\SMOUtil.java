package com.hengrui.blind_back.smo_data.util;

import com.hengrui.blind_back.blind.mapper.EDMCDTMSInfoMapper;
import com.hengrui.blind_back.smo_data.entity.SearchEntity;
import com.hengrui.blind_back.smo_data.mapper.SMODataMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @ClassName SMOUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/6 15:49
 * @Version 1.0
 **/
@Slf4j
@Component
public class SMOUtil {

    @Autowired
    SMODataMapper smoDataMapper;

    @Autowired
    EDMCDTMSInfoMapper edmcdtmsInfoMapper;


    public File exportCSV(String filePath, SearchEntity entity) {
       List<String> siteNum=  entity.getSiteNum();
       String tableType= entity.getType();
       String studyId="";
       Boolean isHrtau=true;
       if(!ObjectUtils.isEmpty(entity.getFileName())&&!entity.getFileName().isEmpty()){
           studyId=entity.getFileName().split("_")[0];
       }

        //查询是否是rave项目
        String edcName = edmcdtmsInfoMapper.getEDCName(studyId);
        if(!ObjectUtils.isEmpty(edcName)&&!edcName.isEmpty()){
            if (edcName.equals("TAU")) {
              isHrtau=true;
            } else if (edcName.equals("Rave")) {
              isHrtau=false;
            }
        }


        //表头列名
        Map<String, String> fieldHeaderMap = new LinkedHashMap<>();
        Collection<String> columnNames = new ArrayList<>();
        try {
            List<Map<String, String>> labTableColumns = smoDataMapper.getLabTableHeader(filePath);
            List<Map<String, String>> tableColumns = smoDataMapper.getLabTableNum(filePath);
            if(tableColumns.size()>1){
                //表头列名排序
                List<Map<String, String>> columnNamesSort = dynamicOrder(labTableColumns);
                log.info("查到的表头信息为:{}", columnNamesSort.toString());
                for (Map<String, String> column : columnNamesSort) {
                    for (String name : column.values()) {
                        columnNames.add(name.replace("<feff>",""));
                        fieldHeaderMap.put(name.replace("<feff>",""), name.replace("<feff>",""));
                    }
                }
                //表头列名
                String[] headerArr = new String[fieldHeaderMap.size()];
                fieldHeaderMap.values().toArray(headerArr);
                String[] keyArr = new String[fieldHeaderMap.size()];
                fieldHeaderMap.keySet().toArray(keyArr);//字段名称
                log.info("------------------------------------------------转换特殊字符后的表头信息为:{}-------------------------", headerArr.toString());
                log.info("------------------------------------------------转换特殊字符后的字段名称为:{}-------------------------", keyArr.toString());
                List<Map<String, String>> result = new ArrayList<>();
                if(tableType.equals("CH")){
                    result = smoDataMapper.getTableContent(filePath, siteNum);
                }else if(tableType.equals("EN")&&isHrtau){
                    result = smoDataMapper.getENHRTableContent(filePath, siteNum);
                    log.info("-----------------------------------------------HRTAU英文项目查询文件路径为:{}-------------------------", filePath);
                    log.info("-----------------------------------------------HRTAU英文项目查询数据量为:{}-------------------------", result.size());
                }else if(tableType.equals("EN")&&!isHrtau){
                    result = smoDataMapper.getENTableContent(filePath, siteNum);
                }

                if(result.size()==0){
                    return null;
                }


                log.info("获取到的表格信息为:{}", result.toString());

                for (String key : keyArr) {
                    log.info("-------------------------------------------获取到的表列信息为:{}----------------------------------",key);
                }
                //存储到CSV
                try {
                    File file = File.createTempFile("export", ".csv");
                    BufferedWriter writer = new BufferedWriter(
                            new OutputStreamWriter(new FileOutputStream(file), StandardCharsets.UTF_8));
                    CSVFormat csvFormat = CSVFormat.EXCEL.withHeader(headerArr);
                    CSVPrinter printer = csvFormat.print(writer);

                    //写入数据
                    // 在获取字段值时，移除 BOM 字符
                    for (Map<String, String> record : result) {
                        List<String> values = new ArrayList<>();
                        for (String key : keyArr) {
                            // 移除 BOM 字符
                            String cleanKey = key.replace("\uFEFF", "");
                            log.info("----------------------------------------处理后的字段名是:{}------------------------------------------", cleanKey);

                            Object value = record.get(cleanKey);
                            if (!ObjectUtils.isEmpty(value)) {
                                if (value instanceof Long) {
                                    Long longValue = (Long) value;
                                    String stringValue = longValue.toString();
                                    values.add(stringValue.replace("", "")); // 使用 stringValue
                                } else if (value instanceof String) {
                                    String stringValue = (String) value;
                                    values.add(stringValue.replace("", ""));
                                }
                            } else {
                                log.info("----------------------------------------这个字段的值是空，需要单独处理:{}------------------------------------------", cleanKey);
                                String siteName = "";
                                String[] pairs = record.toString().split(", ");
                                for (String pair : pairs) {
                                    String[] keyValue = pair.split("=");
                                    if (keyValue.length == 2 && keyValue[0].replace("\uFEFF", "").equals(cleanKey)) {
                                        siteName = keyValue[1];
                                        break;
                                    } else if (keyValue.length == 2 && (
                                            (keyValue[0].replace("\uFEFF", "").equals("研究中心名称") && cleanKey.contains("中心名称")) ||
                                                    (keyValue[0].replace("\uFEFF", "").equals("表") && cleanKey.contains("表")) ||
                                                    (keyValue[0].replace("\uFEFF", "").equals("国家") && cleanKey.contains("家")) ||
                                                    (keyValue[0].replace("\uFEFF", "").equals("SiteGroupName") && cleanKey.contains("GroupName"))
                                    )) {
                                        log.info("表头是:{}", cleanKey);
                                        log.info("拿到的数据为:{}", record);
                                        log.info("拿到的表头数据为:{}", keyValue[0]);
                                        siteName = keyValue[1].replace("}", "").replace("{", "");
                                        break;
                                    }
                                }
                                values.add(siteName.replace("", ""));
                            }
                        }
                        printer.printRecord(values);
                    }
                    printer.flush();
                    printer.close();
                    return file;
                } catch (Exception e) {
                    e.printStackTrace();
                    return null;
                }
            }else {
                return null;
            }
        }catch (Exception e){
            return null;
        }


    }


    public List<Map<String, String>> dynamicOrder(List<Map<String, String>> jsonData) {
        List<Map<String, String>> orderedData = new ArrayList<>();

        List<String> keys = new ArrayList<>(jsonData.get(0).keySet());
        Collections.sort(keys, (a, b) -> {
            int keyA = Integer.parseInt(a.replace("c", ""));
            int keyB = Integer.parseInt(b.replace("c", ""));
            return keyA - keyB;
        });

        for (Map<String, String> map : jsonData) {
            Map<String, String> orderedMap = new LinkedHashMap<>();
            for (String key : keys) {
                orderedMap.put(key, map.get(key).replace("\"", ""));
            }
            orderedData.add(orderedMap);
        }

        return orderedData;
    }
}
