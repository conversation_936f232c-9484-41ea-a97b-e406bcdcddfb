package com.hengrui.blind_back.utils;

import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;

/**
 * @ClassName SFTPFile
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/8/1 13:43
 * @Version 1.0
 **/
@Slf4j
@Component
public class SFTPFile {
    public static void uploadFileToSFTP(String localFilePath, String remoteFilePath, String sftpHost, int sftpPort, String sftpUser, String sftpPassword,String newFileName) {
        Session session = null;
        ChannelSftp channelSftp = null;

        try {
            JSch jsch = new JSch();
            session = jsch.getSession(sftpUser, sftpHost, sftpPort);
            session.setPassword(sftpPassword);

            // Enable JSch logging
            JSch.setLogger(new MyLogger());
            // Avoid asking for key confirmation
            java.util.Properties config = new java.util.Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);
            // Add host key verifier that accepts all keys
            session.setHostKeyRepository(new HostKeyRepository() {
                @Override
                public int check(String host, byte[] key) {
                    return HostKeyRepository.OK;
                }

                @Override
                public void add(HostKey hostkey, UserInfo ui) {}

                @Override
                public void remove(String host, String type) {}

                @Override
                public void remove(String host, String type, byte[] key) {}

                @Override
                public String getKnownHostsRepositoryID() {
                    return null;
                }

                @Override
                public HostKey[] getHostKey() {
                    return new HostKey[0];
                }

                @Override
                public HostKey[] getHostKey(String host, String type) {
                    return new HostKey[0];
                }
            });



            SFTPFile.log.info("Connecting...");
            session.connect(60000);  // 30 second timeout
            SFTPFile.log.info("Connected!");
            channelSftp = (ChannelSftp) session.openChannel("sftp");
            channelSftp.connect(60000);
            SFTPFile.log.info("SFTP Channel opened.");

            File localFile = new File(localFilePath);
            String remoteFile = remoteFilePath + newFileName;
            SFTPFile.log.info("上传的本地文件是:"+localFile +" ,远程的SFTP地址是: "+ remoteFilePath);
            channelSftp.put(new FileInputStream(localFile), remoteFile);
            SFTPFile.log.info("File uploaded successfully to " + remoteFilePath);

        } catch (JSchException | SftpException | java.io.FileNotFoundException e) {
            e.printStackTrace();
        } finally {
            if (channelSftp != null) {
                channelSftp.disconnect();
            }
            if (session != null) {
                session.disconnect();
            }
        }
    }



    public static void downloadFileFromSFTP(String remoteFilePath, String localFilePath, String sftpHost, int sftpPort, String sftpUser, String sftpPassword) {
        Session session = null;
        ChannelSftp channelSftp = null;

        try {
            JSch jsch = new JSch();
            session = jsch.getSession(sftpUser, sftpHost, sftpPort);
            session.setPassword(sftpPassword);

            // Enable JSch logging
            JSch.setLogger(new MyLogger());
            // Avoid asking for key confirmation
            java.util.Properties config = new java.util.Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);
            // Add host key verifier that accepts all keys
            session.setHostKeyRepository(new HostKeyRepository() {
                @Override
                public int check(String host, byte[] key) {
                    return HostKeyRepository.OK;
                }

                @Override
                public void add(HostKey hostkey, UserInfo ui) {}

                @Override
                public void remove(String host, String type) {}

                @Override
                public void remove(String host, String type, byte[] key) {}

                @Override
                public String getKnownHostsRepositoryID() {
                    return null;
                }

                @Override
                public HostKey[] getHostKey() {
                    return new HostKey[0];
                }

                @Override
                public HostKey[] getHostKey(String host, String type) {
                    return new HostKey[0];
                }
            });

            SFTPFile.log.info("Connecting...");
            session.connect(60000);  // 60 second timeout
            SFTPFile.log.info("Connected!");
            channelSftp = (ChannelSftp) session.openChannel("sftp");
            channelSftp.connect(60000);
            SFTPFile.log.info("SFTP Channel opened.");

            File localFile = new File(localFilePath);
            channelSftp.get(remoteFilePath, new FileOutputStream(localFile));
            SFTPFile.log.info("File downloaded successfully to " + localFilePath);

        } catch (JSchException | SftpException | java.io.FileNotFoundException e) {
            e.printStackTrace();
        } finally {
            if (channelSftp != null) {
                channelSftp.disconnect();
            }
            if (session != null) {
                session.disconnect();
            }
        }
    }

}
