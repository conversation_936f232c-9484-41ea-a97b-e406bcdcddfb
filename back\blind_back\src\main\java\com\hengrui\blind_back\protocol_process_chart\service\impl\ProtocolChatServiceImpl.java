package com.hengrui.blind_back.protocol_process_chart.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hengrui.blind_back.blind.utils.Decode64Util;
import com.hengrui.blind_back.blind.utils.MinioUtil;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.protocol_process_chart.service.ProtocolChatService;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.hengrui.blind_back.utils.SubmitSAS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName ProtocolChatServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/12 14:01
 * @Version 1.0
 **/
@Service
@Slf4j
public class ProtocolChatServiceImpl  implements ProtocolChatService {
    @Autowired
    MinioUtil minioUtil;

    @Autowired
    Decode64Util decode64Util;


    @Autowired
    SubmitSAS submitSAS;


    @Override
    public Map<String, String> submitToPPCSAS(String taskId, String projectId,String server) {
        JSONObject param = new JSONObject();
        param.put("server", server);
        param.put("projectId", projectId);
        //1.get CDTMS file from research case
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId="";
        String tableId="";
        if(!ObjectUtils.isEmpty(formInfo.get("recordId"))){
            recordId= formInfo.get("recordId");
            tableId=formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id",recordId);
            object.put("formData",formData);
            projectId=object.toJSONString();
        }else{
            tableId=projectId;
            recordId=taskId;
        }
        //1.1 get token
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");
        //1.2 get studyId
        String studyId = formInfo.get("studyId");
        String filePath= SASOnlieConstant.SAS_DATA_LOCAL_FOLDER + studyId + "-protocol.docx";
        String dataListInfo = CDTMSAPI.getDataListInfo(token,"xsht","obj.studyid='"+studyId+"'","","");
        String temp = JSON.parseArray(dataListInfo).get(0).toString();
        String id = JSON.parseObject(temp).get("id").toString();
        String  getFileInfo = CDTMSAPI.getDataListInfo(token,"xshtbbxx","obj.studyid='"+id+"'","edit","obj.revised_date desc");
        String fileInfo = JSON.parseArray(getFileInfo).get(0).toString();
        String input = JSON.parseObject(fileInfo).get("revised_doc").toString();
        Pattern pattern = Pattern.compile("\\.docx\\*([A-Z0-9]+\\.docx)");
        Matcher matcher = pattern.matcher(input);
        String ufn="";
        if (matcher.find()) {
            int startIndex = matcher.start(1);  // Start index of the hash file
            int endIndex = matcher.end(1);      // End index of the hash file
            ufn = input.substring(startIndex, endIndex);
            ProtocolChatServiceImpl.log.info(ufn);
        }else{
            ProtocolChatServiceImpl.log.info("No match found");
        }

        ProtocolChatServiceImpl.log.info("-------------------------------------------------------found the file name is {}---------------------------",ufn);
        if(!ufn.isEmpty()){
            try {
                CDTMSAPI.downloadDataByUserSync("xshtbbxx",token,ufn,filePath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        //2.upload the research file into minio specific bucket
        if(!filePath.isEmpty()){
            File file = new File(filePath);
            String md5 = decode64Util.getMd5(file);
            //上传调用sas的参数到minio
            minioUtil.uploadObject(file, md5, studyId);
        }

        String uuid = ULIDGenerator.generateULID();
        formInfo.put("uuid",uuid);
        String paramFileName = formInfo.get("studyId").toString() + "_PPC_" + uuid + ".json";


        String paramStr = param.toString();

        //4.1 submit to sas via json params and define the json path on the minio storage platform
        formInfo.put("jsonMinioPath",   SASOnlieConstant.PREFIX_TST_MINIO+"/protocolchart/json/" + paramFileName);
        formInfo.put("sasCodePath", SASOnlieConstant.SAS_PROTOCOL_CHART_CODE_PATH);
        formInfo.put("paramFileName", paramFileName);
        //5. define the output report name on minio
        String outputName = "output/"+formInfo.get("studyId").toString() + SASOnlieConstant.PPC_SUFFIX;
        formInfo.put("outputName", outputName);
        formInfo.put("bucket", "protocolchart");
        formInfo.put("param", paramStr);
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);

        Map<String,String> ENVInfo=new HashMap<>();
        ENVInfo.put("ENV", "UAT");
        ENVInfo.put("studyId",studyId);
        ENVInfo.put("taskId",recordId);
        ENVInfo.put("formId",formId);
        ENVInfo.put("data_type", "protocol_chart");
        ENVInfo.put("data_format", "SAS");
        ENVInfo.put("projectId",tableId);
        ENVInfo.put("uuid",uuid);
        ENVInfo.put("requestPrefix",SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        ENVInfo.put("isLatest", "");
        ENVInfo.put("fileSuffix", "a".toString());
        List<Map<String,String>> uploadFilesFromEDC = new ArrayList<>();
        Map<String,String> sasOutputFile=new HashMap<>();
        List<Map<String,String>> sasOutputFilesInfo = new ArrayList<>();
        sasOutputFile.put("fid","crf_v_doc");
        sasOutputFile.put("outputName",studyId + "_ProTree_input.xlsx");
        sasOutputFilesInfo.add(sasOutputFile);
        //3.then call sas program , and  upload the output  file to the specific form of CDTMS
        return submitSAS.submitToSAS( ENVInfo, uploadFilesFromEDC,formInfo,sasOutputFilesInfo);
    }
}
