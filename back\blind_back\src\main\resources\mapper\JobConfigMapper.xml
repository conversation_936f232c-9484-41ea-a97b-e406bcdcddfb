<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengrui.blind_back.jobconfig.mapper.JobConfigMapper">
   <update id="configSchedule" parameterType="string" >
       UPDATE xxl_job_info
       <set>
           <if test="cron !=null and cron!=''">schedule_conf = #{cron}</if>
       </set>
       WHERE job_desc=#{nodeName}
   </update>

    <select id="getJobId" parameterType="string" resultType="int">
        SELECT
            id
        FROM
            xxl_job_info
        WHERE
            job_desc like  concat('%',#{nodeName},'%');
    </select>
</mapper>
