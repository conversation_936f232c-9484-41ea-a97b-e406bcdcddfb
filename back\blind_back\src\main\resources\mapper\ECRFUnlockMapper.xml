<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengrui.blind_back.ecrf_unlock.mapper.ECRFUnlockMapper">


    <insert id="insertUnlockRecords" parameterType="string">
        insert into sas_call_operation_records
        (id,json_context,user_name,file_name,create_time,update_time)values(#{id},#{param},#{userName},#{fileName},current_timestamp,current_timestamp)
    </insert>

    <update id="updateUnlockFailRecords" parameterType="string">
          update sas_call_operation_records
            set
                fail_reason=#{failReason},
                sas_log_path=#{SASLogPath},
                is_success='N',
                update_time=current_timestamp
            where id = #{id}
    </update>

    <update id="updateUnlockSuccessRecords" parameterType="string">
        update sas_call_operation_records
        set
            file_local_path=#{localPath},
            file_minio_path=#{minioPath},
            sas_log_path=#{SASlogPath},
            is_success='Y',
            update_time=current_timestamp
        where id = #{id}
    </update>
</mapper>