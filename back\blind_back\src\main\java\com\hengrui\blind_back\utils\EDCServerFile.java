package com.hengrui.blind_back.utils;

import com.alibaba.excel.util.StringUtils;
import com.hengrui.blind_back.blind.constant.BlindConstant;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.entity.CallPythonEntity;
import com.hengrui.blind_back.entity.ServerConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName EDCServerFile
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/30 15:22
 * @Version 1.0
 **/
@Slf4j
@Component
public class EDCServerFile {
    public Map<String,String> getEDCServerFileByPy(CallPythonEntity entity, String pyFolder) {
        Map<String,String> result = new HashMap<>();
        String original_name="";
        String dataIsTwoDays="";
        String  command;
        try {

            if(!entity.getIsLatest().equals("default")){
                command = "python3.9 " + entity.getPyPath()
                        + " --data_type='" + entity.getDataType() + "'"
                        + " --env='" + entity.getEnv() + "'"
                        + " --file_list='" + entity.getStudyId() + "'"
                        + " --pro_test='" + SASOnlieConstant.PY_PRO_TEST + "'"
                        + " --uuid='" + entity.getUuid() + "'"
                        + " --data_format='" + entity.getDataFormat() + "'"
                        + " --second='" + entity.getIsLatest() + "'";
            }else{
                // Execute command (replace with your actual command)
                command = "python3.9 " + entity.getPyPath()
                        + " --data_type='" + entity.getDataType() + "'"
                        + " --env='" + entity.getEnv() + "'"
                        + " --file_list='" + entity.getStudyId() + "'"
                        + " --pro_test='" + SASOnlieConstant.PY_PRO_TEST  + "'"
                        + " --uuid='" + entity.getUuid() + "'"
                        + " --data_format='" + entity.getDataFormat() + "'";
            }


            String[] cmd={"/bin/sh","-c",command+" ./do*"};
            EDCServerFile.log.info("执行的python本地命名为:" + command);
            EDCServerFile.log.info("日志路径为:" + pyFolder + BlindConstant.FILE_SEPARATOR + "call_python_output.log");
            Process process = Runtime.getRuntime().exec(cmd);

            // Read the output from the command
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            BufferedWriter writer = new BufferedWriter(new FileWriter(pyFolder + BlindConstant.FILE_SEPARATOR + "call_python_output.log"));
            String line;
            //make the log to be a log file
            while ((line = reader.readLine()) != null) {
                EDCServerFile.log.info(line);
                //make above line to be a log file and save it to the log file
                if (line.contains("original_name")){
                    String[] parts = line.split(":");
                    if (parts.length > 1) {
                        original_name = parts[1].trim();
                    }
                }
                if (line.contains("数据是否在两天之内")){
                    String[] parts = line.split("：");
                    if (parts.length > 1) {
                        dataIsTwoDays = parts[1].trim();
                    }
                }
                writer.write(line);
                writer.newLine();
            }
            // Wait for the process to complete
            int exitCode = process.waitFor();
            EDCServerFile.log.info("Exited with code: " + exitCode);
        } catch (Exception e) {
            EDCServerFile.log.info("Error executing Python script: " + e.getMessage());
        }
        EDCServerFile.log.info("通过python获取到的文件导出名称为: " + original_name);
        EDCServerFile.log.info("通过python获取到的数据是否在两天之内为: " + dataIsTwoDays);

        if(!"".equals(original_name)&&!ObjectUtils.isEmpty(entity.getFileNameSuffix())){
            String[] split = original_name.split("\\.");
            original_name = split[0] + entity.getFileNameSuffix() + "." + split[1];
        }

        result.put("original_name",original_name);
        result.put("dataIsTwoDays",dataIsTwoDays);
        return result;
    }


    public static ServerConfig getHostAndAccount(String sysType) {
        Map<String, ServerConfig> configMap = new HashMap<>();
        configMap.put("cdtms-pro", new ServerConfig("cdtms.hengrui.com", "username1", "password1"));
        configMap.put("cdtms-test", new ServerConfig("cdtms-tst.hengrui.com", "username2", "password2"));
        configMap.put("edc-pro", new ServerConfig("clinical.hengruipharma.com", "username3", "password3"));
        configMap.put("edc-test", new ServerConfig("cdtms-tst.hengrui.com", "username4", "password4"));
        return configMap.getOrDefault(sysType, new ServerConfig("Invalid Param", "", ""));
    }

}
