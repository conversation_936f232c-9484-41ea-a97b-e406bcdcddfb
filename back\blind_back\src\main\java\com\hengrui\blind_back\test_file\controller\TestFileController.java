package com.hengrui.blind_back.test_file.controller;


import com.hengrui.blind_back.constant.ResponseResult;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.utils.CallPython;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

@RestController
@Slf4j
public class TestFileController {

    // 从配置文件中读取文件保存路径
    @CrossOrigin(origins = "http://localhost:9528", maxAge = 3600)
    @PostMapping("/transferXlsxToPdf")
    @ResponseBody
    public ResponseResult<?> uploadFile(@RequestParam("files") MultipartFile file) {
        if (file.isEmpty()) {
            return new ResponseResult<>(500, "ERROR", "文件为空，请校验文件");
        }

        try {
            // 生成新的文件名（UUID + 原始文件后缀）
            String originalFileName = file.getOriginalFilename();
            log.info("获取到的文件名: {}", originalFileName);
            int lastDotIndex = originalFileName.lastIndexOf('.');
            // 截取子字符串
            String fileName = originalFileName.substring(0, lastDotIndex);
           String folder="/home/<USER>/8087/onlyOfficeFile/";
            originalFileName.replace("'", "");
            // 保存文件到本地
            Path filePath = Paths.get(folder + originalFileName);
            String path=folder + originalFileName;
            File file1=new File(path);
            if(!file1.exists()){
                Files.copy(file.getInputStream(), filePath);
            }else{
                Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
            }


            // 创建保存目录（如果不存在）
            File uploadPath = new File(folder);
            if (!uploadPath.exists()) {
                uploadPath.mkdirs();
            }

            // 转换xlsx文件名
            //执行excel转pdf命令
            String command="/home/<USER>/xlsx2pdf/venv/bin/python /home/<USER>/xlsx2pdf/xlsx2pdf.py /home/<USER>/8087/onlyOfficeFile/ "+filePath.toString();
            CallPython.executeLatestFill(command);

            // 返回保存后的文件路径
            return new ResponseResult<>(200, "OK", SASOnlieConstant.SAS_ONLINE+"files/"+fileName+".pdf");
        } catch (IOException e) {
            e.printStackTrace();
            return new ResponseResult<>(500, "ERROR", "系统异常，请联系管理员");
        }
    }


}


