package com.hengrui.blind_back.smo_data.entity;

import java.util.List;

public class SearchEntity {

    //行记录Id
    private String labAeId;

    //文件名
    private String fileName;
    //查询类型 -1全部 0未审核 1已审核 2 已批注
    String type;

    String filePath;

    //中心代码
    private String centerCode;
    //分页参数
    private int pageNum;
    //分页参数
    private int pageSize;

    private String sortColumn;

    private List<String> columnName;


    private List<String> siteNum;


    private String filterColumn;


    private String sortOrder;


    private List<String> filterItem;

    public String getLabAeId() {
        return labAeId;
    }

    public void setLabAeId(String labAeId) {
        this.labAeId = labAeId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }


    private String endTime;

    public String getCenterCode() {
        return centerCode;
    }

    public void setCenterCode(String centerCode) {
        this.centerCode = centerCode;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public List<String> getSiteNum() {
        return siteNum;
    }

    public void setSiteNum(List<String> siteNum) {
        this.siteNum = siteNum;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public SearchEntity() {
    }

    public String getSortColumn() {
        return sortColumn;
    }

    public void setSortColumn(String sortColumn) {
        this.sortColumn = sortColumn;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getFilterColumn() {
        return filterColumn;
    }

    public void setFilterColumn(String filterColumn) {
        this.filterColumn = filterColumn;
    }

    public List<String> getColumnName() {
        return columnName;
    }

    public void setColumnName(List<String> columnName) {
        this.columnName = columnName;
    }

    public List<String> getFilterItem() {
        return filterItem;
    }


    public void setFilterItem(List<String> filterItem) {
        this.filterItem = filterItem;
    }
}
