<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.4.0</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.hengrui</groupId>
	<artifactId>blind_back</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>blind_back</name>
	<description>blind_back</description>
	<properties>
		<java.version>1.8</java.version>
	</properties>


	<dependencies>
        <dependency>
            <artifactId>sas</artifactId>
			<groupId>sas</groupId>
            <version>1.0-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/sas.core.jar</systemPath>
        </dependency>
		<dependency>
			<artifactId>oma</artifactId>
			<groupId>oma</groupId>
			<version>1.0-SNAPSHOT</version>
			<scope>system</scope>
			<systemPath>${pom.basedir}/lib/sas.oma.joma.jar</systemPath>
		</dependency>
		<dependency>
			<artifactId>joma</artifactId>
			<groupId>joma</groupId>
			<version>1.0-SNAPSHOT</version>
			<scope>system</scope>
			<systemPath>${pom.basedir}/lib/sas.oma.joma.rmt.jar</systemPath>
		</dependency>
		<dependency>
			<artifactId>omi</artifactId>
			<groupId>omi</groupId>
			<version>1.0-SNAPSHOT</version>
			<scope>system</scope>
			<systemPath>${pom.basedir}/lib/sas.oma.omi.jar</systemPath>
		</dependency>


		<dependency>
			<artifactId>spire.xls.free</artifactId>
			<groupId>e-iceblue</groupId>
			<version>1.0-SNAPSHOT</version>
			<scope>system</scope>
			<systemPath>${pom.basedir}/lib/Spire.Xls.jar</systemPath>
		</dependency>


		<dependency>
			<artifactId>aspose-words</artifactId>
			<groupId>com.aspose</groupId>
			<version>15.8</version>
			<scope>system</scope>
			<systemPath>${pom.basedir}/lib/aspose-words-15.8.0-jdk16.jar</systemPath>
		</dependency>


		<dependency>
			<artifactId>oma.util</artifactId>
			<groupId>oma.util</groupId>
			<version>1.0-SNAPSHOT</version>
			<scope>system</scope>
			<systemPath>${pom.basedir}/lib/sas.oma.util.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-vfs2</artifactId>
			<version>2.9.0</version>
		</dependency>
		<dependency>
			<groupId>com.github.mwiede</groupId>
			<artifactId>jsch</artifactId>
			<version>0.2.19</version>
		</dependency>
		<dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
			<version>3.9.0</version>
		</dependency>
		<dependency>
			<artifactId>security</artifactId>
			<groupId>security</groupId>
			<version>1.0-SNAPSHOT</version>
			<scope>system</scope>
			<systemPath>${pom.basedir}/lib/sas.security.sspi.jar</systemPath>
		</dependency>
		<dependency>
			<artifactId>svc</artifactId>
			<groupId>svc</groupId>
			<version>1.0-SNAPSHOT</version>
			<scope>system</scope>
			<systemPath>${pom.basedir}/lib/sas.svc.connection.jar</systemPath>
		</dependency>
		<dependency>
			<artifactId>connection</artifactId>
			<groupId>connection</groupId>
			<version>1.0-SNAPSHOT</version>
			<scope>system</scope>
			<systemPath>${pom.basedir}/lib/sas.svc.connection.platform.jar</systemPath>
		</dependency>
		<dependency>
			<artifactId>log4j</artifactId>
			<groupId>log4j</groupId>
			<version>1.0-SNAPSHOT</version>
			<scope>system</scope>
			<systemPath>${pom.basedir}/lib/log4j-1.2.17.jar</systemPath>
		</dependency>






		<dependency>
			<artifactId>itextpdf</artifactId>
			<groupId>itextpdf</groupId>
			<version>1.0-SNAPSHOT</version>
			<scope>system</scope>
			<systemPath>${pom.basedir}/lib/itextpdf-********.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.11.0</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jdbc</artifactId>
		</dependency>



		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		<!-- dynamic-datasource多数据源支持 -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>dynamic-datasource-spring-boot-starter</artifactId>
			<version>3.6.1</version>
		</dependency>

		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.10.1</version>
		</dependency>

		<!--        JDBC-clickhouse数据库-->
		<dependency>
			<groupId>com.clickhouse</groupId>
			<artifactId>clickhouse-jdbc</artifactId>
			<version>0.3.2-test3</version>
		</dependency>

		<dependency>
			<groupId>org.apache.thrift</groupId>
			<artifactId>libthrift</artifactId>
			<version>0.8.0</version>
		</dependency>

		<dependency>
			<groupId>com.xuxueli</groupId>
			<artifactId>xxl-job-core</artifactId>
			<version>2.3.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>5.0.0</version>
		</dependency>


		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml-schemas</artifactId>
			<version>4.1.2</version>
		</dependency>

		<dependency>
			<groupId>org.apache.xmlbeans</groupId>
			<artifactId>xmlbeans</artifactId>
			<version>5.1.1</version>
		</dependency>




		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>5.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>3.5.0</version>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.35</version>
		</dependency>
		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc8</artifactId>
			<version>12.2.0.1</version>
		</dependency>

		<dependency>
			<groupId>com.oracle.ojdbc</groupId>
			<artifactId>orai18n</artifactId>
			<version>19.3.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.sun.mail</groupId>
			<artifactId>javax.mail</artifactId>
			<version>1.6.2</version>
			<exclusions>
				<exclusion>
					<groupId>javax.activation</groupId>
					<artifactId>activation</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.7</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/commons-io/commons-io -->
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.4</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>1.1.13</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>3.3.2</version>
		</dependency>


		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.4</version>
		</dependency>



		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>itextpdf</artifactId>
			<version>********</version>
		</dependency>



		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>4.9.0</version>
		</dependency>

		<dependency>
			<groupId>io.minio</groupId>
			<artifactId>minio</artifactId>
			<version>8.3.0</version>
		</dependency>



		<dependency>
			<groupId>net.lingala.zip4j</groupId>
			<artifactId>zip4j</artifactId>
			<version>1.3.1</version>
		</dependency>
	</dependencies>



	<build>
		<plugins>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>UTF-8</encoding>
					<compilerArgs>
						<arg>-parameters</arg>
					</compilerArgs>
					<compilerArguments>
						<extdirs>${project.basedir}/lib</extdirs>
					</compilerArguments>
				</configuration>
			</plugin>


			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.1.0</version>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>
		</plugins>
		<resources>
			<resource>
				<directory>lib</directory>
				<targetPath>/BOOT-INF/lib/</targetPath>
				<includes>
					<include>**/*.jar</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.xml</include>
				</includes>
				<filtering>false</filtering>
			</resource>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.*</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*.*</include>
					<include>**/*.ttc</include>
					<include>**/*.ttf</include>
				</includes>
			</resource>
		</resources>
	</build>

</project>
