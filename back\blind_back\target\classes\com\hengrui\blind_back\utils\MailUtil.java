package com.hengrui.blind_back.utils;

import cn.hutool.extra.mail.MailAccount;
import com.hengrui.blind_back.constant.MailConstant;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.ecrf_unlock.utils.FileUtil;
import com.spire.xls.FileFormat;
import com.spire.xls.Workbook;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.mail.*;
import javax.mail.internet.*;
import javax.mail.search.SearchTerm;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 * @ClassName MailUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/9/12 9:44
 * @Version 1.0
 **/
@Slf4j
@Component
public class MailUtil {
    public void sendValidMail(String receiver, String title, String mailContent) {
        String mailAddress = MailConstant.RAND_MAIL_ADDRESS;
        String mailAccount = MailConstant.RAND_MAIL_ACCOUNT;
        String password = MailConstant.RAND_MAIL_PASS;
        //发送邮件
        MailAccount account = new MailAccount();
        account.setHost(MailConstant.MAIL_HOST);
        account.setPort(MailConstant.MAIL_PORT);
        account.setAuth(MailConstant.MAIL_AUTH);
        account.setFrom(mailAddress);
        account.setUser(mailAccount);
        account.setPass(password);
//        account.setStarttlsEnable(true);
        List<String> from = new ArrayList<>();
        from.add(mailAddress);
        cn.hutool.extra.mail.MailUtil.send(account, receiver, title, mailContent, false, null);

    }


    public void sendValidMailWithFile(String receiver, String title, String mailContent, File file) {
        // 配置邮件服务器
        Properties props = new Properties();
        props.put("mail.smtp.host", MailConstant.MAIL_HOST);
        props.put("mail.smtp.port", MailConstant.MAIL_PORT);
        props.put("mail.smtp.auth", MailConstant.MAIL_AUTH);
        //  props.put("mail.smtp.starttls.enable", "false"); // 如果需要 TLS 加密

        // 创建会话
        Session session = Session.getInstance(props, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(MailConstant.RAND_MAIL_ACCOUNT, MailConstant.RAND_MAIL_PASS);
            }
        });

        try {
            // 创建邮件
            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(MailConstant.RAND_MAIL_ADDRESS));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(receiver));
            message.setSubject(title, "UTF-8"); // 设置标题编码为 UTF-8

            // 创建邮件正文部分
            MimeBodyPart textPart = new MimeBodyPart();
            // 设置内容类型为"text/html"
            textPart.setContent(mailContent, "text/html;charset=UTF-8");

            // 创建附件部分
            MimeBodyPart attachmentPart = new MimeBodyPart();
            attachmentPart.attachFile(file);
            attachmentPart.setFileName(file.getName()); // 设置附件文件名编码为 UTF-8

            // 组合邮件内容
            Multipart multipart = new MimeMultipart();
            multipart.addBodyPart(textPart);
            multipart.addBodyPart(attachmentPart);
            message.setContent(multipart);

            // 发送邮件
            Transport.send(message);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("邮件发送失败", e);
        }
    }


    public static String processAttachments(Message message) throws MessagingException {
        String msgFileName = "";
        try {
            msgFileName = SASOnlieConstant.RTSM_FILE_PATH + message.getSubject() + "_" + FileUtil.getCurrentDateStr() + ".eml";
            // Save MSG file
            try (FileOutputStream fos = new FileOutputStream(msgFileName)) {
                message.writeTo(fos);
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        return msgFileName;
    }


    public static boolean fuzzyMatch(String subject, String keyword1, String keyword2, String keyword3) {
        // Convert both strings to lowercase for case-insensitive matching
        subject = subject.toLowerCase();
        keyword1 = keyword1.toLowerCase();
        keyword2 = keyword2.toLowerCase();
        keyword3 = keyword3.toLowerCase();

        // Simple fuzzy matching: check if the keyword is contained within the subject
        // You can replace this with more sophisticated fuzzy matching algorithms if needed
        return subject.contains(keyword1) && subject.contains(keyword2) && subject.contains(keyword3);
    }


    public String saveSendMailFile(String keyword1, String keyword2, String keyword3) throws MessagingException {
        String saveFilePath = "";
        //imap
        String user = "<EMAIL>";
        String password = "aaaZZZ123";

        Properties prop = System.getProperties();
        prop.put("mail.store.protocol", "imaps");
        prop.put("mail.imaps.host", "mail.hengrui.com");
        prop.put("mail.imaps.port", "993");
        prop.put("mail.imaps.starttls.enable", "true");
        prop.put("mail.imaps.ssl.trust", "mail.hengrui.com");
        Session session = Session.getInstance(prop);
        Store store = session.getStore("imaps"); // 使用imap会话机制，连接服务器
        store.connect(user, password);
        Folder sendFolder = store.getFolder("已发送邮件"); // 发件箱
        sendFolder.open(Folder.READ_ONLY);
        try {
            // Use custom search term for fuzzy matching
            SearchTerm fuzzySubjectTerm = new SearchTerm() {
                @Override
                public boolean match(Message message) {
                    try {
                        String subject = message.getContent().toString();
                        return subject != null && fuzzyMatch(subject, keyword1, keyword2, keyword3);
                    } catch (MessagingException e) {
                        return false;
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
            };
            Message[] messages = sendFolder.search(fuzzySubjectTerm);
            if (messages.length > 0) {
                // Sort messages by sent date in descending order (latest first)
                Arrays.sort(messages, Comparator.comparing((Message m) -> {
                    try {
                        return m.getSentDate();
                    } catch (MessagingException e) {
                        return null;
                    }
                }).reversed());

                // Get the latest message
                Message latestMessage = messages[0];
                MailUtil.log.info("Content: " + latestMessage.getContent());
                MailUtil.log.info("ContentType: " + latestMessage.getContentType());
                MailUtil.log.info("Subject: " + latestMessage.getSubject());
                MailUtil.log.info("Sent Date: " + latestMessage.getSentDate());
                // Process attachments
                saveFilePath = processAttachments(latestMessage);

            } else {
                MailUtil.log.info("No messages found with the subject keyword: " + keyword1 + keyword2 + keyword3);
            }
        } catch (MessagingException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            // 释放资源
            if (sendFolder != null)
                sendFolder.close(true);
            if (store != null)
                store.close();
        }
        return saveFilePath;
    }


    public static void convertToPDF() {
        String excelFilePath = "C:\\Work\\随机化生成平台\\file\\SHR8735-301_add5fb52fe7dd169.xlsx";
        String pdfOutputPath = "C:\\Work\\随机化生成平台\\file\\SHR8735-301_tst.pdf";


        //加载Excel文档
        Workbook wb = new Workbook();
        wb.loadFromFile(excelFilePath);

        //调用方法保存为PDF格式
        wb.saveToFile(pdfOutputPath, FileFormat.PDF);
    }

    public void sendMailWithHtmlFormat(String email, String subject, String content) {
        String mailAddress = MailConstant.RAND_MAIL_ADDRESS;
        String mailAccount = MailConstant.RAND_MAIL_ACCOUNT;
        String password = MailConstant.RAND_MAIL_PASS;
        //发送邮件
        MailAccount account = new MailAccount();
        account.setHost(MailConstant.MAIL_HOST);
        account.setPort(MailConstant.MAIL_PORT);
        account.setAuth(MailConstant.MAIL_AUTH);
        account.setFrom(mailAddress);
        account.setUser(mailAccount);
        account.setPass(password);
//        account.setStarttlsEnable(true);
        List<String> from = new ArrayList<>();
        from.add(mailAddress);
        cn.hutool.extra.mail.MailUtil.send(account, email, subject, content, true, null);
    }




    public Map<String, String> sendSignNotification(String email, String name, String fileName, String expireDate, String fileCode, String signUrl, String signerName) {

        // 主题
        String subject = "<<" + fileName + ">>需要签署！";

        // 内容模板
        String content = "您好，" + signerName + "<br> " +
                "您有一份文件 《" + fileName + "》 需要签署，请于" + expireDate + "前完成。<br>" +
                "签字验证码: " + fileCode + "<br>" +
                "<a href=\"https://cdtms.hengrui.com/webutil/js/pdfjs/web/viewer.show.do?file=/%E7%BA%BF%E4%B8%8A%E7%AD%BE%E5%90%8D%E5%8A%9F%E8%83%BD%E6%93%8D%E4%BD%9C%E6%8C%87%E5%8D%97_20231101.pdf&amp;unloadstr=\" target=\"_blank\" style=\"font-weight: bold;color: red;\">在线签署操作指南</a><br><br>" +
                "<a href=\"" + signUrl + "\" title=\"去签署\">去签署</a><br><br><br>" +
                "签字发起人：" + name +
                "<br><span style=\"font-size: 9px;\">该邮件为系统发出，请勿回复</span>";

        // 发送邮件
        sendMailWithHtmlFormat(email, subject, content);
        Map<String, String> result = new HashMap<>();
        result.put("subject", subject);
        result.put("content", content);
        return result;

    }

    public Map<String, String> sendSignNotificationDIY(String email, String name, String fileName, String expireDate, String fileCode, String signUrl, String signerName,String subject,String mailContent) {
        //根据文件名获取申请表记录对应的邮件正文和主题
        // 内容模板
        String content = "您好，" + signerName + "<br> " +
                "您有一份文件 《" + fileName + "》 需要签署，请于" + expireDate + "前完成。<br>" +
                "签字验证码: " + fileCode + "<br>" +
                "<a href=\"https://cdtms.hengrui.com/webutil/js/pdfjs/web/viewer.show.do?file=/%E7%BA%BF%E4%B8%8A%E7%AD%BE%E5%90%8D%E5%8A%9F%E8%83%BD%E6%93%8D%E4%BD%9C%E6%8C%87%E5%8D%97_20231101.pdf&amp;unloadstr=\" target=\"_blank\" style=\"font-weight: bold;color: red;\">在线签署操作指南</a><br><br>" +
                "<a href=\"" + signUrl + "\" title=\"去签署\">去签署</a><br><br><br>" +
                "签字发起人：" + name +
                "<br><span style=\"font-size: 9px;\">该邮件为系统发出，请勿回复</span>" + "<br> "+ "<br> " + "<br> "+mailContent;

        // 发送邮件
        sendMailWithHtmlFormat(email, subject, content);
        Map<String, String> result = new HashMap<>();
        result.put("subject", subject);
        result.put("content", content);
        return result;

    }


    public Map<String, String> sendSignNotificationWithSignURL(String email, String name, String fileName, String signURL, String signFinishTime) {
        String subject = "<<" + fileName + ">>签署完成！";
        String content = String.format(
                "<div>" +
                        "<h3>尊敬的 %s：</h3>" +
                        "<div>您于 %s 签署的《%s》已签署完成。</div>" +
                        "<br><br>" +
                        "文件链接: <a href=\"%s\">点击查看</a><br><br><br>" +
                        "<div>该邮件为系统发出，请勿回复</div>" +
                        "</div>",
                name, signFinishTime, fileName,signURL);
        sendMailWithHtmlFormat(email, subject, content);

        Map<String, String> result = new HashMap<>();
        result.put("subject", subject);
        result.put("content", content);
        return result;
    }




    public Map<String, String> sendSignNotificationToAuthor(String email, String name, String fileName, String signURL, String signFinishTime,String signerName) {
        String subject = "<<" + fileName + ">>签署完成！";
        String content = String.format(
                "<div>" +
                        "<h3>尊敬的 %s：</h3>" +
                        "<div>您于 %s 提交的《%s》 %s已签署完成。</div>" +
                        "<br><br>" +
                        "文件链接: <a href=\"%s\">点击查看</a><br><br><br>" +
                        "<div>该邮件为系统发出，请勿回复</div>" +
                        "</div>",
                name, signFinishTime, fileName,signerName,signURL);
        sendMailWithHtmlFormat(email, subject, content);

        Map<String, String> result = new HashMap<>();
        result.put("subject", subject);
        result.put("content", content);
        return result;
    }



    // 临期24小时未签字邮件通知

    public Map<String, String> sendSignExpiringNotification(String email, String name, String fileName, String expireTime, String signUrl) {
        String subject = "<<" + fileName + ">>签署文件任务即将过期！";
        String content = String.format(
                "<div>" +
                        "<h3>尊敬的 %s：</h3>" +
                        "<div>您有一份文件《%s》需要签署，将于 %s 过期。请尽快完成签署，以免影响业务流程。</div>" +
                        "<br>" +
                        "<div>签署链接: <a href=\"%s\">点击此处进行签署</a></div>" +
                        "<br><br>" +
                        "<div>该邮件为系统发出，请勿回复</div>" +
                        "</div>",
                name, fileName, expireTime, signUrl);
        sendValidMail(email, subject, content);

        Map<String, String> result = new HashMap<>();
        result.put("subject", subject);
        result.put("content", content);
        return result;
    }


    public Map<String, String> sendSignAllDoneWithFile(String email, String name, String fileName, File file, String url) {
        String subject = "<<" + fileName + ">>签署完成！";
        String content = String.format("尊敬的 %s：<br><br>" +
                        "您提交的《%s》已签署完成。<br><br>" +
                        "文件链接: <a href=\"%s\">点击查看</a><br><br><br>" +
                        "<span style=\"font-size: 9px;\">该邮件为系统发出，请勿回复</span>",
                name, fileName, url);

        sendValidMailWithFile(email, subject, content, file);
        Map<String, String> result = new HashMap<>();
        result.put("subject", subject);
        result.put("content", content);
        return result;
    }

    public Map<String, String> sendSignFailNotification(String email, String name, String fileName) {
        String subject = "<<" + fileName + ">>签署文件返回失败！";
        String content = String.format("尊敬的 %s：\n\n您提交的" + "<<" + fileName + ">>已签署完成，文件返回失败，请联系管理员",
                name);
        sendValidMail(email, subject, content);
        Map<String, String> result = new HashMap<>();
        result.put("subject", subject);
        result.put("content", content);
        return result;
    }


    public Map<String, String> sendSignExpireNotification(String email, String name, String fileName) {
        String subject = "<<" + fileName + ">>签署文件任务过期！";
        String content = String.format("尊敬的 %s：\n\n您提交的" + "<<" + fileName + ">签署未完成并已过期，请补签后生效。",
                name);
        sendValidMail(email, subject, content);
        Map<String, String> result = new HashMap<>();
        result.put("subject", subject);
        result.put("content", content);
        return result;
    }

    public Map<String, String> sendSignerRemoveNotification(String email, String name, String fileName) {
        String subject = "<<" + fileName + ">> 您被移除签署人列表";
        String content = String.format("尊敬的 %s：\n\n" + "<<" + fileName + "> 您被移除签署人列表。",
                name);
        sendValidMail(email, subject, content);
        Map<String, String> result = new HashMap<>();
        result.put("subject", subject);
        result.put("content", content);
        return result;
    }
}
