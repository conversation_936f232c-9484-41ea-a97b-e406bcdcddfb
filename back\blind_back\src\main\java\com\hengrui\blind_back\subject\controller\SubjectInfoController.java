package com.hengrui.blind_back.subject.controller;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.hengrui.blind_back.utils.EDCAPI;
import com.hengrui.blind_back.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName SubjectInfoController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/21 16:27
 * @Version 1.0
 **/
@RestController
@Slf4j
public class SubjectInfoController {


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("getSubjectInfoFirst")
    public  Map<String, Object> getSubjectInfoFirst(String taskId,
                                          String server,
                                          String projectId) {
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);

        Map<String, Object> finalResult = new HashMap<>();
        SubjectInfoController.log.info("get the first subjectInfo from subjects");
        SubjectInfoController.log.info("taskId is :" + taskId + " server is :" + server + " projectId is :" + projectId);
        //1. call cdtms API to get studyCode
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId="";
        String tableId="";
        if(!ObjectUtils.isEmpty(formInfo.get("recordId"))){
            recordId= formInfo.get("recordId");
            tableId=formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id",recordId);
            object.put("formData",formData);
            projectId=object.toJSONString();
        }else{
            tableId=projectId;
            recordId=taskId;
        }
        String studyId = formInfo.get("studyId");
        FileUtils.setEDCAPIUrl(studyId,server);
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        //2. call EDC API to get subject info
        JSONObject result = EDCAPI.getSubjectsInfo(studyId, "pro", "SV", true);
        result.put("taskId", recordId);
        result.put("projectId", tableId);
        result.put("formId",formId);
        Boolean isFull = (Boolean) result.get("isFull");
        result.remove("isFull");
        //3.call getInfo API
        Map<String, String> formInfoByTaskId = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        //4. get id from formInfoByTaskId
        SubjectInfoController.log.info("--------------------------------------------------get formInfoByTaskId is :" + formInfoByTaskId);
        String data = formInfoByTaskId.get("param");
        String dataId="";
        if(!data.isEmpty()){
            JSONObject formInfoData = new JSONObject(data.toString());
            dataId=formInfoData.get("id").toString();
        }
        //5.put id into data
        JSONObject temp = new JSONObject(result.get("data").toString());
        temp.put("id",dataId);
        result.put("data",temp);
        SubjectInfoController.log.info("call dataSave params is :" + result);
        String newFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        result.put("formId",newFormId);
        SubjectInfoController.log.info("最新的formId是 :" + newFormId);
        CDTMSAPI.dataSave(result);

        if(isFull){
            finalResult.put("success", false);
            finalResult.put("code", 404);
            finalResult.put("msg", "访视数超25000条，请反馈后自行填写");
        }else {
            finalResult.put("success", true);
            finalResult.put("code", 200);
            finalResult.put("msg", "由于系统限制，当前只能获取25000条记录");
        }

        SubjectInfoController.log.info("返回给CDTMS的数据是：" + finalResult.toString());
        return finalResult;
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("getSubjectInfoLast")
    public  Map<String, Object> getSubjectInfoLast(String taskId,
                                         String server,
                                         String projectId) {
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, Object> finalResult = new HashMap<>();
        SubjectInfoController.log.info("get the last subjectInfo from subjects");
        SubjectInfoController.log.info("taskId is :" + taskId + " server is :" + server + " projectId is :" + projectId);
        //1. call cdtms API to get studyCode
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId="";
        String tableId="";
        if(!ObjectUtils.isEmpty(formInfo.get("recordId"))){
            recordId= formInfo.get("recordId");
            tableId=formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id",recordId);
            object.put("formData",formData);
            projectId=object.toJSONString();
        }else{
            tableId=projectId;
            recordId=taskId;
        }
        String studyId = formInfo.get("studyId");
        FileUtils.setEDCAPIUrl(studyId,server);
        //2. call EDC API to get subject info
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        //3. call dataSave API from CDTMS
        JSONObject result = EDCAPI.getSubjectsInfo(studyId, "pro", "SV", false);
        result.put("taskId", recordId);
        result.put("projectId", tableId);
        result.put("formId",formId);
        Boolean isFull = (Boolean) result.get("isFull");

        result.remove("isFull");
        //1.call getInfo API
        Map<String, String> formInfoByTaskId = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        if(!ObjectUtils.isEmpty(formInfoByTaskId.get("recordId"))){
            recordId= formInfoByTaskId.get("recordId");
            tableId=formInfoByTaskId.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formInfoByTaskId.put("id",recordId);
            object.put("formData",formData);
            projectId=object.toJSONString();
        }else{
            tableId=projectId;
            recordId=taskId;
        }
        //1.1 get id from formInfoByTaskId
        String data = formInfoByTaskId.get("param");
        String dataId="";
        if(!data.isEmpty()){
            JSONObject formInfoData = new JSONObject(data.toString());
            dataId=formInfoData.get("id").toString();
        }
        //5.put id into data
        JSONObject temp = new JSONObject(result.get("data").toString());
        temp.put("id",dataId);
        result.put("data",temp);
        SubjectInfoController.log.info("call dataSave params is :" + result);
        String newFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        result.put("formId",newFormId);
        SubjectInfoController.log.info("最新的formId是 :" + newFormId);
        CDTMSAPI.dataSave(result);
        if(isFull){
            finalResult.put("success", false);
            finalResult.put("code", 404);
            finalResult.put("msg", "访视数超25000条，请反馈后自行填写");
        }else {
            finalResult.put("success", true);
            finalResult.put("code", 200);
            finalResult.put("msg", "由于系统限制，当前只能获取25000条记录");
        }
        SubjectInfoController.log.info("返回给CDTMS的数据是：" + finalResult.toString());
        return finalResult;
    }


}
