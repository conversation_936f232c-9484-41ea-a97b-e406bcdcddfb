package com.hengrui.blind_back.entity;

/**
 * @ClassName CallPythonEntity
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/3 15:02
 * @Version 1.0
 **/
public class CallPythonEntity {
   // String dataFormat,String pythonEnv,String dataType,String env,String studyId
    private String dataFormat;
    private String dataType;

    private String uuid;

    private String env;

    private String studyId;

    private String pyPath;

    private String isLatest;


    private String fileNameSuffix;

    public String getFileNameSuffix() {
        return fileNameSuffix;
    }

    public void setFileNameSuffix(String fileNameSuffix) {
        this.fileNameSuffix = fileNameSuffix;
    }

    public String getIsLatest() {
        return isLatest;
    }

    public void setIsLatest(String isLatest) {
        this.isLatest = isLatest;
    }

    public String getDataFormat() {
        return dataFormat;
    }

    public void setDataFormat(String dataFormat) {
        this.dataFormat = dataFormat;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }


    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getStudyId() {
        return studyId;
    }

    public void setStudyId(String studyId) {
        this.studyId = studyId;
    }

    public String getPyPath() {
        return pyPath;
    }

    public void setPyPath(String pyPath) {
        this.pyPath = pyPath;
    }
}
