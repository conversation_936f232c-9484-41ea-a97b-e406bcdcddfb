<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengrui.blind_back.smo_data.mapper.SMODataMapper">
    <select id="getLabTableHeader" parameterType="string" resultType="map">
        WITH
            minio_tbl AS
                (
                    SELECT
                        *
                    FROM s3(#{filePath}, 'minioadmin', 'minioadmin')
                )
        SELECT *
        FROM minio_tbl
                 LIMIT 1
    </select>


    <select id="getLabTableNum" parameterType="string" resultType="map">
        WITH
            minio_tbl AS
                (
                    SELECT
                        *
                    FROM s3(#{filePath}, 'minioadmin', 'minioadmin')
                )
        SELECT *
        FROM minio_tbl
                 LIMIT 2
    </select>


    <select id="getTableContent" parameterType="string" resultType="map">
        WITH
            minio_tbl AS
                (
                    SELECT
                        *
                    FROM s3(#{filePath}, 'minioadmin', 'minioadmin','CSVWithNames')
                )
        SELECT *
        FROM minio_tbl
        where 1 = 1
        <foreach collection="siteNum" item="item" open=" and (`中心编号` in (" separator="," close="))">
            #{item}
        </foreach>
    </select>

    <select id="getENTableContent" parameterType="string" resultType="map">
        WITH
        minio_tbl AS
        (
        SELECT
        *
        FROM s3(#{filePath}, 'minioadmin', 'minioadmin','CSVWithNames')
        )
        SELECT *
        FROM minio_tbl
        where 1 = 1
        <foreach collection="siteNum" item="item" open=" and (`Site Number` in (" separator="," close="))">
            #{item}
        </foreach>
    </select>


    <select id="getENHRTableContent" parameterType="string" resultType="map">
        WITH
        minio_tbl AS
        (
        SELECT
        *
        FROM s3(#{filePath}, 'minioadmin', 'minioadmin','CSVWithNames')
        )
        SELECT *
        FROM minio_tbl
        where 1 = 1
        <foreach collection="siteNum" item="item" open=" and (`Site ID` in (" separator="," close="))">
            #{item}
        </foreach>
    </select>
</mapper>
