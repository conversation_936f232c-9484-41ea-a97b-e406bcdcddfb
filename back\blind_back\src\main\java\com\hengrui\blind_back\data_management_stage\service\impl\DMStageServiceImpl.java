package com.hengrui.blind_back.data_management_stage.service.impl;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.hengrui.blind_back.blind.config.MinioConfig;
import com.hengrui.blind_back.blind.constant.BlindConstant;
import com.hengrui.blind_back.blind.utils.Decode64Util;
import com.hengrui.blind_back.blind.utils.MinioUtil;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.data_management_stage.service.DMStageService;
import com.hengrui.blind_back.ecrf_unlock.utils.FileUtil;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.hengrui.blind_back.utils.CallPython;
import com.hengrui.blind_back.utils.SubmitSAS;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName DMStageServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/9/11 9:44
 * @Version 1.0
 **/

@Slf4j
@Service
public class DMStageServiceImpl implements DMStageService {
    @Autowired
    private SubmitSAS submitSAS;

    @Autowired
    CallPython callPython;

    @Autowired
    MinioConfig minioConfig;

    @Autowired
    MinioUtil minioUtil;

    @Autowired
    Decode64Util decode64Util;

    @Override
    public Map<String, String> submitToDMStageSAS(String taskId, String projectId) {
        //1.从edc server端那文件的 文件信息
        List<Map<String, String>> uploadFilesFromEDC = new ArrayList<>();
        //2.调用的表单所需的部分信息，如参数存放minio的地址和uuid
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId = "";
        String tableId = "";
        if (!ObjectUtils.isEmpty(formInfo.get("recordId"))) {
            recordId = formInfo.get("recordId");
            tableId = formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }

        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        //获取语言选项
        String used_language = CDTMSAPI.getStudyLanguage(studyId);
        String uuid = ULIDGenerator.generateULID();
        formInfo.put("uuid", uuid);
        String paramFileName = formInfo.get("studyId").toString() + "_DMStage_" + uuid + ".json";
        //2.1 submit to sas via json params and define the json path on the minio storage platform
        formInfo.put("jsonMinioPath", SASOnlieConstant.PREFIX_PRO_MINIO+"/datamanagement/json/" + paramFileName);
        formInfo.put("sasCodePath", SASOnlieConstant.SAS_DMSTAGE_CODE_PATH);
        formInfo.put("paramFileName", paramFileName);
        formInfo.put("bucket", "datamanagement");
        formInfo.put("language", used_language);
        String dataSetName = studyId + "_sas.zip";
        //1.先查询minio raw下面有么有同名文件，如果没有，调用python获取sas数据集
        minioConfig.setBucketName("raw");
        boolean objectExist = minioUtil.isObjectExist(dataSetName, minioConfig);
        if (!objectExist) {
            Map<String, String> ENVInfoA = new HashMap<>();
            ENVInfoA.put("ENV", "PRO");
            ENVInfoA.put("data_type", "data_set");
            ENVInfoA.put("studyId", studyId);
            ENVInfoA.put("taskId", recordId);
            ENVInfoA.put("formId", formId);
            ENVInfoA.put("projectId", tableId);
            ENVInfoA.put("uuid", uuid);
            ENVInfoA.put("data_format", "SAS");
            ENVInfoA.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
            ENVInfoA.put("isLatest", "");
            ENVInfoA.put("fileSuffix", "a".toString());
            callPython.downloadEDCServerFile(ENVInfoA, uploadFilesFromEDC);
        }


        //3.请求cdtms API所需的环境信息和接口所需的params
        Map<String, String> ENVInfo = new HashMap<>();
        ENVInfo.put("ENV", "PRO");
        ENVInfo.put("data_type", "Query_Summary_for_Protocol_Violation");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", recordId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", tableId);
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("data_format", "Excel");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        ENVInfo.put("isLatest", "");
        ENVInfo.put("fileSuffix", "a".toString());
        //4.after call sas programe need the cdtms formInfo for upload

        String outputName = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.DMSTAGE_SUFFIX;

        formInfo.put("outputName", outputName);
        List<Map<String, String>> sasOutputFilesInfo = new ArrayList<>();
        Map<String, String> sasOutputFile = new HashMap<>();
        sasOutputFile.put("fid", "project_report_doc");

        sasOutputFile.put("outputName", outputName);
        sasOutputFilesInfo.add(sasOutputFile);
        //5.use above params to call the sas programe
        return submitSAS.submitToSAS(ENVInfo, uploadFilesFromEDC, formInfo, sasOutputFilesInfo);
    }


    @Override
    public Map<String, String> getProgressReport(String taskId, String projectId) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId = "";
        String tableId = "";
        if (!ObjectUtils.isEmpty(formInfo.get("recordId"))) {
            recordId = formInfo.get("recordId");
            tableId = formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        String params = formInfo.get("param");
        String uuid = ULIDGenerator.generateULID();
        formInfo.put("uuid", uuid);
        String used_language = CDTMSAPI.getStudyLanguage(studyId);
        formInfo.put("jsonMinioPath", "");
        formInfo.put("sasCodePath", SASOnlieConstant.SAS_PROGRESS_REPORT_CODE_PATH);
        formInfo.put("paramFileName", "");
        formInfo.put("bucket", "datamanagement");
        formInfo.put("language", used_language);



        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(params);
        String id = jsonObject.get("id").toString();
        DMStageServiceImpl.log.info("------------获取到的formId是：" + formId + "------------");
        DMStageServiceImpl.log.info("------------获取到的studyId是：" + studyId + "------------");
        Map<String, String> result = new HashMap<>();
        List<Map<String, String>> filesFromEDC = new ArrayList<>();
        Map<String, String> fileObject = new HashMap<>();
        fileObject.put("fid", "project_report_doc");
        fileObject.put("fileType", ".xlsx");

        Map<String, String> ENVInfo = new HashMap<>();
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("ENV", "PRO");
        ENVInfo.put("data_format", "Excel");
        ENVInfo.put("data_type", "Overall_Progress_Report");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", recordId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", tableId);
        ENVInfo.put("fileSuffix", "a");
        ENVInfo.put("isLatest", "");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        Map<String,String> pyResult=callPython.downloadEDCServerFile(ENVInfo, filesFromEDC);
        String results = pyResult.get("original_name");
        ENVInfo.put("isLatest", "FALSE");

        //拿到本地文件路径
        String fileName = SASOnlieConstant.EDC_DATA_LOCAL_FOLDER + BlindConstant.FILE_SEPARATOR + ENVInfo.get("studyId") + "_" + ENVInfo.get("ENV") + "_" + ENVInfo.get("uuid") + "_" + ENVInfo.get("fileSuffix") + fileObject.get("fileType");
        //上传到minio
        File file = new File(fileName);

        String md5 = decode64Util.getMd5(file);
        String edcFileTime = FileUtil.transferEDCFIleName(results);
        minioUtil.uploadObjectWithTag(file, md5, studyId, edcFileTime, "datamanagement", "report_online");
        //调用sas程序
        DMStageServiceImpl.log.info("------------调用了sas程序！！！------------");
        List<Map<String, String>> sasOutputFilesInfo = new ArrayList<>();
        Map<String, String> sasOutputFile = new HashMap<>();
        sasOutputFile.put("fid", "project_report_doc");
        String outputName = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.PROGRESS_REPORT_SUFFIX;
        formInfo.put("outputName", outputName);
        sasOutputFile.put("outputName", outputName);
        sasOutputFilesInfo.add(sasOutputFile);

        Map<String, String> sasResult = submitSAS.submitToSAS(ENVInfo, filesFromEDC, formInfo, sasOutputFilesInfo);
        DMStageServiceImpl.log.info("------------调用了sas程序的结果是：------------"+sasResult);

        //下载统计数文件
        minioUtil.downloadObjectWithPath("datamanagement", "output/"+studyId+"_metrix_return.xlsx", SASOnlieConstant.SAS_OUTPUT_PATH);
        Map<String,String> tagInfo= minioUtil.getObjectTags("datamanagement", "output/"+formInfo.get("studyId").toString() + SASOnlieConstant.PROGRESS_REPORT_SUFFIX);
        String date=tagInfo.get("date");
        //解析统计数文件
        String parseResult ="";
        try {
            // Specify the path to your Excel file
            String excelFilePath =SASOnlieConstant.SAS_OUTPUT_PATH+System.getProperty("file.separator") +studyId+"_metrix_return.xlsx";
            FileInputStream inputStream = new FileInputStream(new File(excelFilePath));

            // Create Workbook instance from excel file
            Workbook workbook = new XSSFWorkbook(inputStream);

            // Get first sheet
            Sheet sheet = workbook.getSheetAt(0);

            // Read first 5 rows
            for (int rowIndex = 0; rowIndex < 5; rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    // Iterate through each cell in the row
                    for (Cell cell : row) {
                        parseResult += cell.getStringCellValue() + "\t";
                        //   System.out.print(cell.getStringCellValue() + "\t");

                    }

                }

            }
            DMStageServiceImpl.log.info(parseResult);
            // Close workbook and stream
            workbook.close();
            inputStream.close();

        } catch (IOException e) {
            e.printStackTrace();
        }
        // Method 1: Using regex to find all numbers
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(parseResult);

        List<Integer> numbers = new ArrayList<>();
        while (matcher.find() && numbers.size() < 5) {
            numbers.add(Integer.parseInt(matcher.group()));
        }

        // Print results
        DMStageServiceImpl.log.info("Extracted numbers: " + numbers);
        // 获取当前日期
        String resultStr="附件是截至"+date+"数据导出的进展报告。摘要如下：\n" +
                "① 访视缺失：共"+numbers.get(0)+"个，\n" +
                "② 页面缺失：共"+numbers.get(1)+"条，\n" +
                "③ 质疑："+numbers.get(2)+"条质疑未回复。待关闭质疑"+numbers.get(3)+"条。\n" +
                "④ 待SDV: 待核查变量共"+numbers.get(4)+"个，\n" +
                "以上内容烦请转发相关CRA督促尽快处理。";
        DMStageServiceImpl.log.info(resultStr);
        //回填统计数文本信息
        JSONObject temp = new JSONObject();
        JSONObject param = new JSONObject();
        temp.put("id", id);
        temp.put("measures", resultStr);
        param.put("data", temp);
        DMStageServiceImpl.log.info("call dataSave params is :" + param);
        String newFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        param.put("formId", newFormId);
        param.put("taskId", recordId);
        param.put("projectId", tableId);
        DMStageServiceImpl.log.info("最新的formId是 :" + newFormId);
        CDTMSAPI.dataSave(param);
        result.put("result", sasResult.toString());
        //call python program
        return result;
    }

    @Override
    public Map<String, String> submitToDMDateClean(String taskId, String projectId) {
        //回填开始日期和结束日期 当月1号、月末日期
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId = "";
        String tableId = "";
        if (!ObjectUtils.isEmpty(formInfo.get("recordId"))) {
            recordId = formInfo.get("recordId");
            tableId = formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }

        DMStageServiceImpl.log.info("获取到的表单数据为:" + formInfo.toString());
        String params = formInfo.get("param");
        String studyId = formInfo.get("studyId");
        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(params);
        String uuid = jsonObject.get("uuid").toString();
        String id = jsonObject.get("id").toString();

        // 获取当前日期
        LocalDate currentDate = LocalDate.now();


        //90天前
        LocalDate before90Days = currentDate.minusDays(90);


        // 获取当月最后一天的日期
        LocalDate lastDayOfMonth = currentDate.withDayOfMonth(currentDate.lengthOfMonth());
        //获取当月第一天的日期
        LocalDate firstDayOfMonth = currentDate.withDayOfMonth(1);

        // 创建日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 将日期转换为字符串
        String lastDayOfMonthString = lastDayOfMonth.format(formatter);
        //将日期转换为字符串
        String firstDayOfMonthString = firstDayOfMonth.format(formatter);



        String currentDateStr = currentDate.format(formatter);
        String Before90DaysStr=before90Days.format(formatter);

        //创建定期审核的记录,回填数据起始和截止日期
      /*  String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "crf_handover");
        String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
        //根据表单接口，查询对应的项目的studyId 整数值
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
        long finalStudyId = Long.parseLong(studyInt);
        DMStageServiceImpl.log.info("--------------------------------定期审核的studyId是:" + finalStudyId);
        com.alibaba.fastjson.JSONObject scheduleParam = new com.alibaba.fastjson.JSONObject();
        scheduleParam.put("study_id", studyInt);
        scheduleParam.put("data_start_date", firstDayOfMonthString);
        scheduleParam.put("data_deadline_date", lastDayOfMonthString);
        scheduleParam.put("type", "2");
        scheduleParam.put("zq", "6");
        scheduleParam.put("contents", "数据清理");
        scheduleParam.put("reviewer_name", "System");


        //创建新审核记录
        String saveRecord = CDTMSAPI.usersyndataSave(token, "study_regular_review", formId, "", "", scheduleParam.toString());
        long irecordId = Long.parseLong(com.alibaba.fastjson.JSONObject.parseObject(saveRecord).get("id").toString());
        DMStageServiceImpl.log.info("--------------------------------定期审核的记录id是:" + irecordId);
        String result = CDTMSAPI.getDataListInfo(token, "study_regular_review", "obj.study_id='" + finalStudyId + "'" + "and obj.id='" + irecordId + "'", "edit", "");
        //查询uuid
        String schedule_uuid = JSONArray.parseArray(result).getJSONObject(0).get("uuid").toString();
        DMStageServiceImpl.log.info("--------------------------------定期审核的记录id是:" + schedule_uuid);*/
        //调用定期审核python接口
        String res = CDTMSAPI.callDataCleanPyAPI(SASOnlieConstant.EDC_API, "review_uuid_"+uuid ,studyId, "query_summary",Before90DaysStr,currentDateStr);
        com.alibaba.fastjson.JSONObject jsonObj = JSON.parseObject(res);
        DMStageServiceImpl.log.info("--------------------------------查询到的质疑审核的统计数是:" + jsonObj.get("msg").toString());

        //保存回填数据
        JSONObject temp = new JSONObject();
        JSONObject param = new JSONObject();
        temp.put("id", id);
        temp.put("cdate", currentDateStr);
        temp.put("measures", jsonObj.get("msg").toString());
        param.put("data", temp);
        DMStageServiceImpl.log.info("call dataSave params is :" + param.toString());
        String newFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        param.put("formId", newFormId);
        param.put("taskId", recordId);
        param.put("projectId", tableId);
        DMStageServiceImpl.log.info("最新的formId是 :" + newFormId);
        CDTMSAPI.dataSave(param);
        String latestFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        //获取minio上的附件.并上传
        String CNFileName = studyId + "_PRO_质疑汇总报告.xlsx";
        String ENFileName = studyId + "_PRO_Query_Summary_Report.xlsx";
        String filePath = "";
        if (minioUtil.isExist("/output/" + CNFileName, "regularreview")) {
            minioUtil.downloadGetObject("regularreview", "/output/" + CNFileName);
            filePath = SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + CNFileName;
        } else if (minioUtil.isExist("/output/" + ENFileName, "regularreview")) {
            minioUtil.downloadGetObject("regularreview", "/output/" + ENFileName);
            filePath = SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + ENFileName;
        }

        DMStageServiceImpl.log.info("上传的文件路径是 :" + filePath);
        File cleanFile = new File(filePath);
        if (cleanFile.exists()) {
            FileUtil.uploadSASOutputFile(taskId, latestFormId, "project_report_doc", filePath, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.REMOTE_SERVER_API_PREFIX, studyId + "_PRO_质疑汇总报告_" + currentDateStr + ".xlsx", "excel");
        }
        return formInfo;
    }
}
