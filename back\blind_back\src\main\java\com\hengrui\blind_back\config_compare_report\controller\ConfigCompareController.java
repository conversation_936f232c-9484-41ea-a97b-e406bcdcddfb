package com.hengrui.blind_back.config_compare_report.controller;

import com.hengrui.blind_back.config_compare_report.service.ConfigCompareService;
import com.hengrui.blind_back.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName ConfigCompareController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/24 9:47
 * @Version 1.0
 **/
@RestController
@Slf4j
public class ConfigCompareController {

    @Autowired
    private ConfigCompareService configCompareService;

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getConfigCompareReport")
    public Map<String, Object> getConfigCompareReport(String taskId,
                                                  String server,
                                                  String projectId) {
        ConfigCompareController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> comparison = configCompareService.getConfigCompareReport(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", comparison);
        return result;
    }



    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getProCompareReport")
    public Map<String, Object> getProCompareReport(String taskId,
                                                      String server,
                                                      String projectId) {
        ConfigCompareController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> comparison = configCompareService.getProCompareReport(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
      //  result.put("data", comparison);
        return result;
    }


}
