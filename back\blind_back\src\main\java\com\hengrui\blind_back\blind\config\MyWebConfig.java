package com.hengrui.blind_back.blind.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.util.UrlPathHelper;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

@Slf4j
@Configuration
public class MyWebConfig implements WebMvcConfigurer {
    @Resource
    private PermissionInterceptor permissionInterceptor;

    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        UrlPathHelper urlPathHelper = new UrlPathHelper();
        urlPathHelper.setUrlDecode(false);
        urlPathHelper.setDefaultEncoding(StandardCharsets.UTF_8.name());
        configurer.setUrlPathHelper(urlPathHelper);
    }


    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        //文件磁盘图片url 映射
        //配置server虚拟路径，handler为前台访问的目录，locations为files相对应的本地路径,本处映射的地址方便操作记录预览文件使用
        registry.addResourceHandler("/file/**")
    //            .addResourceLocations("file:/home/<USER>/ex_check_xlsx/");
               .addResourceLocations("file:C:/MyFile/external_data/compare_file/");
    }
}
