package com.hengrui.blind_back.ecrf_unlock.utils;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.hengrui.blind_back.blind.constant.BlindConstant;
import com.hengrui.blind_back.blind.utils.Decode64Util;
import com.hengrui.blind_back.blind.utils.MinioUtil;
import com.hengrui.blind_back.blind.utils.MyX509TrustManager;
import com.hengrui.blind_back.blind.utils.NullHostNameVerifier;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.utils.CDTMSAPI;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName FileUtils
 * @Description TODO 实现文件下载
 * <AUTHOR>
 * @Date 2024/4/16 9:25
 * @Version 1.0
 **/
@Slf4j
@Component
public class FileUtil {

    @Autowired
    MinioUtil minioUtil;

    @Autowired
    Decode64Util decode64Util;

    public FileUtil() {
    }

    @SneakyThrows
    public static void downloadSnapshotData(String tableId, String token, String ufn, HttpServletResponse response) throws IOException {
        // Call the download interface
        String fileUrl = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/download?tableid=" + tableId + "&token=" + URLEncoder.encode(token, "UTF-8") + "&ufn=" + URLEncoder.encode(ufn, "UTF-8");

        // Set up to access the https request through ip address
        HttpsURLConnection.setDefaultHostnameVerifier(new NullHostNameVerifier());
        TrustManager[] tm = {new MyX509TrustManager()};
        SSLContext sslContext = SSLContext.getInstance("TLS");
        try {
            sslContext.init(null, tm, new java.security.SecureRandom());
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        }
        // Get the SSLSocketFactory object from the above SSLContext object
        SSLSocketFactory ssf = sslContext.getSocketFactory();
        String urlStr = fileUrl;
        URL url = null;
        try {
            url = new URL(urlStr);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
        HttpsURLConnection con = null;
        try {
            con = (HttpsURLConnection) url.openConnection();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        con.setSSLSocketFactory(ssf);
        try {
            con.setRequestMethod("POST"); // Set to submit data using POST method
        } catch (ProtocolException e) {
            throw new RuntimeException(e);
        }
        con.setDoInput(true); // Open input stream to get data from the server
        con.setDoOutput(true);// Open output stream to send data to the server
        // Set the sending parameters
        PrintWriter out = null;
        try {
            out = new PrintWriter(new OutputStreamWriter(con.getOutputStream(), "UTF-8"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        out.flush();
        out.close();
        try {
            con.getInputStream();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // Set the appropriate headers on the HttpServletResponse object
        String fileName = ufn; // Set the desired file name for the download
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        response.setHeader("Content-Transfer-Encoding", "binary");

        // Write the file stream to the HttpServletResponse output stream
        byte[] bs = new byte[1024];
        int len;
        OutputStream os = response.getOutputStream();
        while ((len = con.getInputStream().read(bs)) != -1) {
            os.write(bs, 0, len);
        }
        os.flush();
        os.close();
    }

    @SneakyThrows
    public static String downloadFileromCDTMS(String taskId, String projectId, String fid, String fileType) throws IOException {
        String studyId = getStudyIdByTaskId(taskId, projectId);
        // Construct the URL
        String fileUrl = BlindConstant.ECRF_UNLOCK_DOWNLOAD_URL + taskId + "&projectId=" + URLEncoder.encode(projectId, "UTF-8") + "&fid=" + URLEncoder.encode(fid, "UTF-8");
        // Set up SSL context
        SSLContext sslContext = SSLContext.getInstance("TLS");
        TrustManager[] tm = {new MyX509TrustManager()};
        sslContext.init(null, tm, new java.security.SecureRandom());
        SSLSocketFactory ssf = sslContext.getSocketFactory();

        // Open connection
        URL url = new URL(fileUrl);
        HttpsURLConnection con = (HttpsURLConnection) url.openConnection();
        con.setSSLSocketFactory(ssf);
        con.setRequestMethod("GET");

        // Set hostname verifier if necessary
        con.setHostnameVerifier(new NullHostNameVerifier());
        if (studyId.equals("CDTMS手册优化测试项目")) {
            studyId = "CDTMS-ECRF-UNLOCK-TEST";
        }
        // Define the local path where the file will be saved
        //  String localFilePath = SASOnlieConstant.SAS_DATA_LOCAL_FOLDER + studyId + fileType;
        String localFilePath = SASOnlieConstant.SAS_BLIND_LOG_PATH + BlindConstant.FILE_SEPARATOR + studyId + fileType;
        // Read the input stream and write to the specified local file path
        try (InputStream in = new BufferedInputStream(con.getInputStream());
             FileOutputStream fos = new FileOutputStream(localFilePath);
             BufferedOutputStream bout = new BufferedOutputStream(fos, 2048)) {
            byte[] data = new byte[2048];
            int count;
            while ((count = in.read(data, 0, 2048)) != -1) {
                bout.write(data, 0, count);
            }
        }
        //then return the localFile path
        return localFilePath;
    }


    public static String getStudyIdByTaskId(String taskId, String projectId) {
        FileUtil.log.info("the request taskId is:" + taskId);
        String requestURL = SASOnlieConstant.REMOTE_SERVER_API + "remoteButtonTask/getInfo?taskId=" + taskId + "&projectId=" + projectId + "&type=list";
        try {
            URL url = new URL(requestURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            FileUtil.log.info("Response Code: " + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                JSONObject jsonResponse = new JSONObject(response.toString());
                // Extract the study_id
                if (!ObjectUtils.isEmpty(jsonResponse.getJSONObject("data"))) {
                    String studyId = jsonResponse.getJSONObject("data").getJSONObject("data").getStr("studyid");
                    FileUtil.log.info("Study ID: " + studyId);
                    return studyId;
                }


            } else {
                FileUtil.log.info("GET request not worked");
                return "falil";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "falil";
    }


    //获取文件名
    public static String getDownloadFilesName(String taskId, String fid) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, SASOnlieConstant.REMOTE_SERVER_PROJECTID);
        //获取表单id
        String tableId = formInfo.get("tableId").toString();

        String param = formInfo.get("param");
        cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(param);
        String studyId = formInfoData.get("studyid").toString();
        //获取模板文件
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");

        String dataListInfo = CDTMSAPI.getDataListInfo(token, "xsht", "obj.studyid='" + studyId + "'", "", "");
        String temp = JSON.parseArray(dataListInfo).get(0).toString();
        String id = JSON.parseObject(temp).get("id").toString();
        String getFileInfo = CDTMSAPI.getDataListInfo(token, tableId, "obj.studyid='" + id + "'", "edit", "obj.lastmodifytime desc");
        String fileInfo = JSON.parseArray(getFileInfo).get(0).toString();
        String input = JSON.parseObject(fileInfo).get(fid).toString();
        int endIndex = input.indexOf('*');
        String result = "";
        // 提取子字符串
        if(endIndex != -1){
            result = input.substring(0, endIndex);
        }

        log.info("-------------------------------------------------------拿到了下载文件的文件名 {}---------------------------", result);


        return result;

    }

    public static String downloadFiles(String taskId, String fid, String fileType) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, SASOnlieConstant.REMOTE_SERVER_PROJECTID);
        String regex = "";
        //获取表单id
        String tableId = formInfo.get("tableId").toString();
        if (fileType.contains("xlsx")) {
            regex = "\\*([A-Z0-9]+\\.xlsx)\\|";
        } else if (fileType.contains("zip")) {
            regex = "\\*([A-Z0-9]+\\.zip)\\|";
        }else if (fileType.contains("csv")) {
            regex = "\\*([A-Z0-9]+\\.csv)\\|";
        }
        String param = formInfo.get("param");
        cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(param);
        String studyId = formInfoData.get("studyid").toString();
        //获取模板文件
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");
        String filePath = SASOnlieConstant.SAS_BLIND_LOG_PATH + BlindConstant.FILE_SEPARATOR + studyId + fileType;
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "xsht", "obj.studyid='" + studyId + "'", "", "");
        String temp = JSON.parseArray(dataListInfo).get(0).toString();
        String id = JSON.parseObject(temp).get("id").toString();
        String getFileInfo = CDTMSAPI.getDataListInfo(token, tableId, "obj.studyid='" + id + "'", "edit", "obj.lastmodifytime desc");
        String fileInfo = JSON.parseArray(getFileInfo).get(0).toString();
        String input = JSON.parseObject(fileInfo).get(fid).toString();
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        String ufn = "";
        if (matcher.find()) {
            ufn = matcher.group(1);
            log.info(ufn);
        } else {
            log.info("No match found");
        }
        log.info("-------------------------------------------------------found the file name is {}---------------------------", ufn);
        if (!ufn.isEmpty()) {
            try {
                CDTMSAPI.downloadDataByUserSync(tableId, token, ufn, filePath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        return filePath;

    }

    public String uploadLocalFileToMinio(String taskId, String projectId, String fid, String fileType) throws IOException {
        String filePath = downloadFiles(taskId, fid, fileType);

        FileUtil.log.info("----------------下载的文件路径为：{}------------------", filePath);
        String studyId = getStudyIdByTaskId(taskId, projectId);

        File file = new File(filePath);
        if (file.exists()) {
            String md5 = decode64Util.getMd5(file);
            String minioPath = minioUtil.uploadObject(file, md5, studyId);
            return minioPath;
        } else {
            return "";
        }


    }


    public static String getFormIdByTaskId(String taskId) {
        FileUtil.log.info("the request taskId is:" + taskId);
        String formId = "";
        String requestURL = SASOnlieConstant.REMOTE_SERVER_API + "remoteButtonTask/formId?taskId=" + taskId + "&projectId=" + BlindConstant.TABLE_ID;
        try {
            URL url = new URL(requestURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            FileUtil.log.info("Response Code: " + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                JSONObject jsonResponse = new JSONObject(response.toString());
                // Extract the study_id
                formId = jsonResponse.getJSONObject("data").getStr("formId");
                FileUtil.log.info("formId: " + formId);
                return formId;
            } else {
                FileUtil.log.info("GET request not worked");
                return "falil";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return formId;
    }

    //自动调度的文件上传
    public static String uploadSASOutputFile(String taskId, String formId, String fid, String filePath,
                                             String projectId, String urlPrefix, String newFileName,
                                             String dataType) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId_back(taskId, projectId);
        String result = "";
        if (!formInfo.get("param").isEmpty()) {
            result = uploadSASOutputFile_back(taskId, formId, fid, filePath, projectId, urlPrefix, newFileName, dataType);
        } else {
            //使用usersync上传附件，taskId为记录id，projectId为tableId
            result = uploadFileByUsersyn(taskId, formId, fid, filePath, projectId, urlPrefix, newFileName, dataType);
        }
        return result;

    }

    public static String uploadFileByUsersyn(String recordid, String formId, String fid, String filePath,
                                             String tableId, String urlPrefix, String fn,
                                             String dataType) {
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, tableId);
        formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
        FileUtil.log.info("--------------------------上传sas输出文件到CDTMS开始！！！-------------------------------------------");
        File file = new File(filePath);
        String fileName = file.getName();
        String urlString = "";
        FileUtil.log.info("-----------------需要上传的本地文件名：" + fileName + "--------------------------------------------------");
        FileUtil.log.info("-----------------需要上传的原始文件名：" + fn + "--------------------------------------------------");
        try {
            urlString = "https://" + urlPrefix + "usersyn/upload"
                    + "?token=" + token + "&formid=" + formId + "&tableid=" + tableId + "&fid=" + fid + "&recordid=" + recordid + "&fn=" + URLEncoder.encode(fn, "UTF8");
        } catch (UnsupportedEncodingException e) {
            FileUtil.log.error("------------------地址栏文件名URLEncoder异常!!!---------------------------------------------------");
            throw new RuntimeException(e);
        }


        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }
        String ret = null;
        try {
            //call cdtms API to upload file
            if (dataType.equals("edc_account_history")) {
                ret = postContent(urlString, fis, 600000, 524288);
            } else  if(dataType.equals("Subject_CRF")){
                ret = postContent(urlString, fis, 600000, 524288);
            }else {
                ret = postContent(urlString, fis, 15000, 8192);
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        FileUtil.log.info(ret);

        String ufn="";
        JSONObject jsonResponse = new JSONObject(ret.toString());
        // Extract the study_id
        String data = jsonResponse.get("ufn").toString();
        if (!ObjectUtils.isEmpty(data) && !data.isEmpty()) {
            ufn = data;
        }
        return ufn;


    }

    public static String uploadSASOutputFile_back(String taskId, String formId, String fid, String filePath,
                                                  String projectId, String urlPrefix, String newFileName,
                                                  String dataType) {
        FileUtil.log.info("--------------------------上传sas输出文件到CDTMS开始！！！-------------------------------------------");
        File file = new File(filePath);
        String fileName = file.getName();
        String urlString = "";
        FileUtil.log.info("-----------------需要上传的本地文件名：" + fileName + "--------------------------------------------------");
        FileUtil.log.info("-----------------需要上传的表单文件名：" + newFileName + "--------------------------------------------------");
        try {
            urlString = "https://" + urlPrefix + "remoteButtonTask/upload"
                    + "?taskId=" + taskId + "&formId=" + formId + "&fid=" + fid + "&fn=" + URLEncoder.encode(newFileName, "UTF8") + "&projectId=" + projectId;
        } catch (UnsupportedEncodingException e) {
            FileUtil.log.error("------------------地址栏文件名URLEncoder异常!!!---------------------------------------------------");
            throw new RuntimeException(e);
        }


        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }
        String ret = null;
        try {
            //call cdtms API to upload file
            if (dataType.equals("edc_account_history")) {
                ret = postContent(urlString, fis, 600000, 524288);
            } else {
                ret = postContent(urlString, fis, 15000, 8192);
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //更新
        String notify = notify(taskId, urlPrefix);
        if (!notify.equals("fail")) {
            FileUtil.log.info("--------------------------通知CDTMS上传好了 成功，do not go gentle into that good night !!!-------------------------------------------");
            return "success";
        }
        FileUtil.log.info(ret);

        return "success";
    }


    public static String notify(String taskId, String urlPrefix) {
        FileUtil.log.info("the request taskId is:" + taskId);
        String responseStr = "";
        String requestURL = "https://" + urlPrefix + "remoteButtonTask/notify?taskId=" + taskId + "&projectId=" + BlindConstant.TABLE_ID;
        try {
            URL url = new URL(requestURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            FileUtil.log.info("Response Code: " + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                JSONObject jsonResponse = new JSONObject(response.toString());
                responseStr = response.toString();
                // Extract the study_id
                FileUtil.log.info("response is : " + responseStr);
                return responseStr;
            } else {
                FileUtil.log.info("GET request not worked");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "fail";
    }


    public static void prependLineToFile(File logFile, String firstLine) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(logFile), StandardCharsets.UTF_8));
        List<String> lines = new ArrayList<>();

        // Add the new first line
        lines.add(firstLine);

        // Read the rest of the lines and add them
        String currentLine;
        while ((currentLine = reader.readLine()) != null) {
            lines.add(currentLine);
        }
        reader.close();

        // Write everything back to the file
        BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(logFile), StandardCharsets.UTF_8));
        for (String line : lines) {
            writer.write(line);
            writer.newLine();
        }
        writer.close();
    }


    public static String postContent(String urlStr, InputStream inputstream, int timeOut, int byteSize) throws Exception {
        FileUtil.log.info("------------------------------调用的上传接口地址urlStr: " + urlStr + "-------------------------------------------------");
        URL url = new URL(urlStr);
        HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
        httpConn.setDoInput(true);
        httpConn.setDoOutput(true);
        httpConn.setRequestMethod("POST");
        httpConn.setConnectTimeout(timeOut);
        httpConn.setReadTimeout(timeOut);
        httpConn.setRequestProperty("content-type", "application/octet-stream");

        httpConn.connect();
        int len = 0;
        byte[] byteA = new byte[byteSize];
        OutputStream outStream = httpConn.getOutputStream();
        while ((len = inputstream.read(byteA)) > 0) {
            outStream.write(byteA, 0, len);
        }
        httpConn.getOutputStream().flush();
        httpConn.getOutputStream().close();

        OutputStream os = new ByteArrayOutputStream();

        InputStream is = httpConn.getInputStream();
        byte[] bA = new byte[byteSize];
        len = is.read(bA);
        while (len > 0) {
            os.write(bA, 0, len);
            len = is.read(bA);
        }


        os.close();
        is.close();

        int responseCode = httpConn.getResponseCode();
        String responseMessage = httpConn.getResponseMessage();

        FileUtil.log.info("-------------------------------call cdtms upload API responseCode is :" + responseCode);
        FileUtil.log.info("-------------------------------call cdtms upload API responseMessage is :" + responseMessage);
        FileUtil.log.info("-------------------------------call cdtms upload API responseBody is :" + os.toString());

        return os.toString();
    }


    public static String getDownloadFileName(String filePath) {
        FileUtil.log.info("--------------------------上传sas输出文件到CDTMS开始！！！-------------------------------------------");
        File file = new File(filePath);
        String fileName = file.getName();
        String newFilename = "";
        String[] parts = fileName.split("_");
        FileUtil.log.info("-----------------需要上传的文件名：" + fileName + "--------------------------------------------------");
        String[] split = fileName.split("\\.");

        if (split.length == 2) {
            String fileNamePrefix = split[0];
            String fileNameSufix = "." + split[1];
            if (parts.length >= 3) {
                // Reconstruct the filename using the first two parts and the file extension
                newFilename = parts[0] + "_" + parts[1] + fileNameSufix;
                FileUtil.log.info("------------------Transformed Filename: " + newFilename);
            } else {
                FileUtil.log.info("------------------Unexpected filename format.------------------");
            }
            FileUtil.log.info("-----------------需要上传的文件名前缀：" + fileNamePrefix + "--------------------------------------------------");
            FileUtil.log.info("-----------------需要上传的文件名后缀：" + fileNameSufix + "---------------------------------------------------");
        } else {
            newFilename = parts[0] + "_" + parts[1];
        }
        return newFilename;
    }


    public static String eee(String filePath) {
        int lastIndex = filePath.lastIndexOf('/');
        return filePath.substring(lastIndex + 1);
    }

    public static String transferEDCFIleName(String originalString) {
        String formattedDate = "";
        if (!originalString.isEmpty()) {
            String extractedDate = originalString.substring(originalString.indexOf("_20") + 1, originalString.indexOf("_20") + 9);
            String dateString = extractedDate;
            // Parse the input string
            LocalDate date = LocalDate.parse(dateString, DateTimeFormatter.BASIC_ISO_DATE);
            // Format the date to yyyy-MM-dd
            formattedDate = date.format(DateTimeFormatter.ISO_LOCAL_DATE);
        }
        return formattedDate;
    }

    public static String getCurrentDateStr() {
        LocalDateTime currentDate = LocalDateTime.now();

        // Define the desired format
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH-mm-ss");

        // Format the date as a string
        String dateString = currentDate.format(formatter);


        return dateString;
    }


    public static String formatDate(String input) {

        // Parse the input string
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy/M/dd/HH/mm");
        LocalDateTime dateTime = LocalDateTime.parse(input, inputFormatter);

        // Format to desired output
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = dateTime.format(outputFormatter);
        return formattedDate;
    }


    public static String uploadFileByRemoteAPI(String taskId, String formId, String fid, String filePath,
                                               String projectId, String urlPrefix, String newFileName,
                                               String dataType) {
        FileUtil.log.info("--------------------------上传sas输出文件到CDTMS开始！！！-------------------------------------------");
        File file = new File(filePath);
        String fileName = file.getName();
        String urlString = "";
        FileUtil.log.info("-----------------需要上传的本地文件名：" + fileName + "--------------------------------------------------");
        FileUtil.log.info("-----------------需要上传的原始文件名：" + newFileName + "--------------------------------------------------");
        try {
            urlString = "https://" + urlPrefix + "remoteButtonTask/upload"
                    + "?taskId=" + taskId + "&formId=" + formId + "&fid=" + URLEncoder.encode(fid, "UTF8") + "&fn=" + URLEncoder.encode(newFileName, "UTF8") + "&projectId=" + projectId;
        } catch (UnsupportedEncodingException e) {
            FileUtil.log.error("------------------地址栏文件名URLEncoder异常!!!---------------------------------------------------");
            throw new RuntimeException(e);
        }


        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }

        String ufn = "";
        String ret = null;
        try {
            //call cdtms API to upload file
            if (dataType.equals("edc_account_history")) {
                ret = postContent(urlString, fis, 600000, 524288);
            } else {
                ret = postContent(urlString, fis, 15000, 8192);
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        JSONObject jsonResponse = new JSONObject(ret.toString());
        // Extract the study_id
        String data = jsonResponse.get("data").toString();
        if (!ObjectUtils.isEmpty(data) && !data.isEmpty()) {
            JSONObject dataObj = new JSONObject(data);
            ufn = dataObj.get("ufn").toString();
        }
        return ufn;
    }


}


