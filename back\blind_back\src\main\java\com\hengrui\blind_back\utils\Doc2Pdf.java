package com.hengrui.blind_back.utils;

import com.aspose.words.*;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

/**
 * @ClassName Doc2Pdf
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/2 14:26
 * @Version 1.0
 **/
@Slf4j
public class Doc2Pdf {
    public static boolean getLicense() {
        boolean result = false;
        try {
            InputStream is = Doc2Pdf.class.getClassLoader().getResourceAsStream("license.xml"); //  license.xml应放在..\WebRoot\WEB-INF\classes路径下
            License aposeLic = new License();
            aposeLic.setLicense(is);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    // Updated font configuration for Aspose.Words 15.8.0
    private static void configureFontsForLinux(Document doc) throws Exception {
        try {
            // Set system font paths
            System.setProperty("java.awt.fonts", "/usr/share/fonts");

            // Check if wingding font exists
            File wingdingFile = new File("/usr/share/fonts/WINGDING.TTF");
            boolean wingdingAvailable = wingdingFile.exists();
            log.info("Wingdings font available: " + wingdingAvailable);

            // Set default font
            DocumentBuilder builder = new DocumentBuilder(doc);
            builder.getFont().setName("Arial");

            // If wingding is available, it will be accessible to Aspose.Words
            // through the system font path we set above
        } catch (Exception e) {
            log.warn("Font configuration failed: " + e.getMessage());
        }
    }




    public static void doc2pdf(String Address, String outputPath) {
        if (!getLicense()) {
            log.warn("License验证失败，可能会产生水印");
            return;
        }
        try {
            long old = System.currentTimeMillis();
            File file = new File(outputPath);
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            FileOutputStream os = new FileOutputStream(file);
            Document doc = new Document(Address);
            
            // Configure fonts for Linux compatibility
            configureFontsForLinux(doc);
            
            // Make all form fields readonly before PDF conversion
            makeFormFieldsReadonly(doc);
            
            PdfSaveOptions pdfOptions = new PdfSaveOptions();
            pdfOptions.setPreserveFormFields(true); // Keep checkboxes visible but readonly
            pdfOptions.setEmbedFullFonts(true);

            // Remove encryption details that cause issues in 15.8.0
            // Instead, rely on flattening form fields to make them readonly

            // 修改：使用简化的表格处理方法
            optimizeDocumentForPdf(doc);

            // 配置PDF保存选项
            pdfOptions.setCompliance(PdfCompliance.PDF_15);
            pdfOptions.setPageMode(PdfPageMode.USE_OUTLINES);
            pdfOptions.setZoomBehavior(PdfZoomBehavior.NONE);
            pdfOptions.setUseHighQualityRendering(true);

            // 页面设置
            PageSetup pageSetup = doc.getFirstSection().getPageSetup();
            pageSetup.setPaperSize(PaperSize.A4);
            pageSetup.setOrientation(Orientation.PORTRAIT);
            // 使用较小的边距
            pageSetup.setTopMargin(10);
            pageSetup.setBottomMargin(10);
            pageSetup.setLeftMargin(10);
            pageSetup.setRightMargin(10);

            try {
                doc.save(os, pdfOptions);
            } finally {
                os.close();
            }

            long now = System.currentTimeMillis();
            log.info("PDF转换完成，耗时：{} 秒", (now - old) / 1000.0);

        } catch (Exception e) {
            log.error("PDF转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 优化文档布局，避免内容重叠和表格错位
     * 适用于Aspose.Words 15.8.0版本
     */
    private static void optimizeDocumentForPdf(Document doc) throws Exception {
        // 获取所有表格
        NodeCollection tables = doc.getChildNodes(NodeType.TABLE, true);

        for (Table table : (Iterable<Table>) tables) {
            // 禁用自动调整，保持原始布局
            table.setAllowAutoFit(false);

            // 处理表格中的单元格
            for (Row row : table.getRows()) {
                for (Cell cell : row.getCells()) {
                    // 禁用文本自动调整，保持原始布局
                    cell.getCellFormat().setFitText(true);
                    // 确保文本正常换行
                    cell.getCellFormat().setWrapText(true);

                    // 设置单元格边距，避免内容重叠
                    cell.getCellFormat().setLeftPadding(2);
                    cell.getCellFormat().setRightPadding(2);
                    cell.getCellFormat().setTopPadding(2);
                    cell.getCellFormat().setBottomPadding(2);
                }
            }
        }

        // 设置默认字体，确保文本正确显示
        try {
            // 尝试使用替代方法设置字体
            DocumentBuilder builder = new DocumentBuilder(doc);
            builder.getFont().setName("SimSun");
            builder.getFont().setSize(10.5);
        } catch (Exception e) {
            log.warn("设置默认字体失败: " + e.getMessage());
        }
    }

    /**
     * Make all form fields readonly while preserving their current values
     */
    private static void makeFormFieldsReadonly(Document doc) throws Exception {
        try {
            NodeCollection formFields = doc.getChildNodes(NodeType.FORM_FIELD, true);

            for (FormField formField : (Iterable<FormField>) formFields) {
                if (formField.getType() == FieldType.FIELD_FORM_CHECK_BOX) {
                    DocumentBuilder builder = new DocumentBuilder(doc);

                    // Try using Wingdings if available, fallback to simple symbols
                    try {
                        builder.getFont().setName("Wingdings");
                        String checkedSymbol = "\uF052";  // Wingdings checkmark
                        String uncheckedSymbol = "\uF0A3"; // Wingdings empty box
                        builder.write(formField.getChecked() ? checkedSymbol : uncheckedSymbol);
                    } catch (Exception e) {
                        // Fallback to basic symbols if Wingdings not available
                        builder.getFont().setName("Arial");
                        builder.write(formField.getChecked() ? "[X]" : "[ ]");
                    }

                    formField.remove();
                } else {
                    formField.setEnabled(false);
                }
            }
        } catch (Exception e) {
            log.warn("Form field processing failed: " + e.getMessage());
        }
    }



}
