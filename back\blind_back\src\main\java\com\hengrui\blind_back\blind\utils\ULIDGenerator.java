package com.hengrui.blind_back.blind.utils;

import java.security.SecureRandom;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class ULIDGenerator {
    private static final SecureRandom RANDOM = new SecureRandom();

    public static String generateULID() {
        long timestamp = Instant.now().toEpochMilli();
        byte[] randomBytes = new byte[10];
        RANDOM.nextBytes(randomBytes);

        long ulid = (timestamp << 80) | byteArrayToLong(randomBytes);

        return Long.toHexString(ulid);
    }

    private static long byteArrayToLong(byte[] bytes) {
        long result = 0;
        for (int i = 0; i < bytes.length; i++) {
            result = (result << 8) | (bytes[i] & 0xFF);
        }
        return result;
    }

    public static String getCurrentTime() {
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        String formattedDate = currentDate.format(formatter);
        return formattedDate;
    }

    public static String getCurrentTimes() {
        LocalDateTime currentDate = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy_MM_dd_HH_mm_ss");
        String formattedDate = currentDate.format(formatter);
        return formattedDate;
    }

    public static String getMinioTagsCurrentTimes() {
        LocalDateTime currentDate = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd/HH/mm/ss");
        String formattedDate = currentDate.format(formatter);
        return formattedDate;
    }


    public static String getMinioCurrentTimes() {
        LocalDateTime currentDate = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
        String formattedDateTime = currentDate.format(formatter);
        return formattedDateTime;
    }
}
