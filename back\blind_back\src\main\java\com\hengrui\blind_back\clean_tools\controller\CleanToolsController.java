package com.hengrui.blind_back.clean_tools.controller;

import com.hengrui.blind_back.clean_tools.service.CleanToolsService;
import com.hengrui.blind_back.uat.controller.UatController;
import com.hengrui.blind_back.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName CleanToolsController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/9/20 15:44
 * @Version 1.0
 **/

@RestController
@Slf4j
public class CleanToolsController {

    @Autowired
    private CleanToolsService cleanToolsService;

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/submitToRecistSAS")
    public Map<String, Object> submitToRecistSAS(String taskId,
                                              String server,
                                              String projectId) {
        CleanToolsController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIPre(server);
        FileUtils.getCDTMSAPIProjectId(server);
        Map<String, String> results = cleanToolsService.submitToRecistSAS(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", results.get("dataIsTwoDays"));
        result.put("data", results);
        return result;
    }

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/submitToAEXOTSAS")
    public Map<String, Object> submitToAEXOTSAS(String taskId,
                                              String server,
                                              String projectId) {
        CleanToolsController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> results = cleanToolsService.submitToAEXOTSAS(taskId, projectId);
        if (results.isEmpty()){
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("code", 200);
            result.put("msg","未检测到参数配置!");
            result.put("data", results);
        }
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", results.get("dataIsTwoDays"));
        result.put("data", results);
        return result;
    }



    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/submitToLabAESAS")
    public Map<String, Object> submitToLabAESAS(String taskId,
                                                String server,
                                                String projectId) {
        CleanToolsController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> results = cleanToolsService.submitToLabAESAS(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", results.get("dataIsTwoDays"));
        result.put("data", results);
        return result;
    }



}
