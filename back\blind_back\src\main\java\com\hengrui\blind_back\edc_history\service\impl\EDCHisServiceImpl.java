package com.hengrui.blind_back.edc_history.service.impl;

import com.alibaba.fastjson.JSON;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.edc_history.service.EDCHisService;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.hengrui.blind_back.utils.CallPython;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName EDCHisServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/3 14:16
 * @Version 1.0
 **/
@Slf4j
@Service
public class EDCHisServiceImpl implements EDCHisService {
    @Autowired
    CallPython callPython;

    @Override
    public Map<String, String> getUserHistoryFile(String taskId, String projectId,String fid,String fileSuffix) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId="";
        String tableId="";
        if(!ObjectUtils.isEmpty(formInfo.get("recordId"))){
            recordId= formInfo.get("recordId");
            tableId=formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id",recordId);
            object.put("formData",formData);
            projectId=object.toJSONString();
        }else{
            tableId=projectId;
            recordId=taskId;
        }
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        EDCHisServiceImpl.log.info("------------获取到的formId是：" + formId + "------------");
        EDCHisServiceImpl.log.info("------------获取到的studyId是：" + studyId + "------------");
        Map<String, String> result = new HashMap<>();
        List<Map<String, String>> filesFromEDC = new ArrayList<>();
        Map<String, String> fileObject = new HashMap<>();
        fileObject.put("fid", fid);
        fileObject.put("fileType", ".xlsx");
        filesFromEDC.add(fileObject);

        String uuid = ULIDGenerator.generateULID();
        Map<String, String> ENVInfo = new HashMap<>();
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("ENV", "PRO");
        if(fid.equals("permission_his")){
            ENVInfo.put("data_type", "edc_account_history");
        }else{
            ENVInfo.put("data_type", "edc_account_info");
        }

        ENVInfo.put("data_format", "Excel");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", recordId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", tableId);
        ENVInfo.put("fileSuffix", fileSuffix);
        ENVInfo.put("isLatest", "");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        Map<String,String> pyResult=callPython.downloadEDCServerFile(ENVInfo, filesFromEDC);
        String results = pyResult.get("original_name");
        result.put("result", results);
        //call python program
        CDTMSAPI.updateWorkFlowStatus(taskId, projectId, "status","10");
        return result;

    }

}
