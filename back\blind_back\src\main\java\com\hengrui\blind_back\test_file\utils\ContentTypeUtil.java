package com.hengrui.blind_back.test_file.utils;


import org.springframework.stereotype.Component;

@Component
public class ContentTypeUtil {
    public String getContentType(String suffix) {
        String contentType = "";
        //转小写
        switch (suffix.toLowerCase()) {
            case "doc":
                contentType = "application/msword";
                break;
            case "docx":
                contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
                break;
            case "txt":
                contentType = "text/plain";
                break;
            case "html":
                contentType = "text/html";
                break;
            case "css":
                contentType = "text/css";
                break;
            case "js":
                contentType = "text/javascript";
                break;
            case "json":
                contentType = "application/json";
                break;
            case "xml":
                contentType = "application/xml";
                break;
            case "jpeg":
            case "jpg":
                contentType = "image/jpeg";
                break;
            case "png":
                contentType = "image/png";
                break;
            case "gif":
                contentType = "image/gif";
                break;
            case "mp3":
                contentType = "audio/mpeg";
                break;
            case "wav":
                contentType = "audio/wav";
                break;
            case "ogg":
                contentType = "audio/ogg";
                break;
            case "mp4":
                contentType = "video/mp4";
                break;
            case "webm":
                contentType = "video/webm";
                break;
            case "pdf":
                contentType = "application/pdf";
                break;
            case "tiff":
                contentType = "image/tiff";
                break;
            default:
                contentType = "application/octet-stream";
                break;
        }
        return contentType;
    }

    //获取文件后缀
    public String getSuffix(String filename) {
        int dotIndex = filename.lastIndexOf(".");
        return filename.substring(dotIndex + 1);
    }

}
