package com.hengrui.blind_back.sas_check_content.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hengrui.blind_back.blind.mapper.EDMCDTMSInfoMapper;
import com.hengrui.blind_back.blind.utils.MinioUtil;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import com.hengrui.blind_back.clean_tools.service.impl.CleanToolsServiceImpl;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.ecrf_unlock.utils.FileUtil;
import com.hengrui.blind_back.sas_check_content.service.SASCheckContentService;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.hengrui.blind_back.utils.CallPython;
import com.hengrui.blind_back.utils.FileUtils;
import com.hengrui.blind_back.utils.SubmitSAS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import org.springframework.util.ObjectUtils;

import java.io.File;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName SASCheckContentServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/9/23 15:37
 * @Version 1.0
 **/
@Service
@Slf4j
public class SASCheckContentServiceImpl implements SASCheckContentService {
    @Autowired
    private SubmitSAS submitSAS;

    @Autowired
    MinioUtil minioUtil;


    @Autowired
    CallPython callPython;

    @Autowired
    FileUtils fileUtils;

    @Override
    public String testSASCheckContent(String taskId, String projectId) {
        //获取核查内容，判断调用的程序类型,回填到sas核查程序表单 周期是6
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        if (!ObjectUtils.isEmpty(formInfo.get("param")) && !formInfo.get("param").isEmpty()) {
            JSONObject formData = JSON.parseObject(formInfo.get("param"));
            String nodeName = formData.get("checkarea").toString();
            String data = formInfo.get("param");
            String uuid = "";
            if (!data.isEmpty()) {
                cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(data);
                uuid = formInfoData.get("uuid").toString();
            }
            String type = "";
            if (nodeName.contains("定期审核")) {

                if (nodeName.contains("账号审核")) {
                    type = "账户审核";
                } else if (nodeName.contains("质疑管理")) {
                    type = "质疑审核";
                } else if (nodeName.contains("稽查轨迹")) {
                    type = "稽查轨迹";
                }
                formInfo.get("param");
                CDTMSAPI.callScheduleReview(SASOnlieConstant.EDC_API, "sascheck_uuid_" + uuid, type);
            }

        }
        return "success";
    }

    @Autowired
    CDTMSAPI cdtmsapi;

    @Override
    public String getIRCData(String taskId, String projectId) {

        //查询项目id
        //获取studyId
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId = "";
        String tableId = "";
        if (!ObjectUtils.isEmpty(formInfo.get("recordId"))) {
            recordId = formInfo.get("recordId");
            tableId = formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }
        List<Map<String, String>> uploadFilesFromEDC = new ArrayList<>();
        Map<String, String> fileObject1 = new HashMap<>();
        fileObject1.put("fid", "edc_dataset");
        fileObject1.put("fileType", ".zip");
        uploadFilesFromEDC.add(fileObject1);
        //edc 数据集下载，有数据返回就调用sas,没有返回就不调用
        String studyId = formInfo.get("studyId");
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        Map<String, String> ENVInfo = new HashMap<>();
        //获取als同步到minio
        minioUtil.uploadALSUATFile(ENVInfo,studyId,taskId,formId,projectId);
        ENVInfo.put("taskId", taskId);
        ENVInfo.put("projectId", projectId);
        ENVInfo.put("isLatest", "YES");
        ENVInfo.put("ENV", "");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        ENVInfo.put("data_type", "cleanTool");


        //获取语言选项
        String used_language = CDTMSAPI.getStudyLanguage(studyId);
        //json参数
        String  customcode= "";
        String sascodeStr="";
        String parmStr="";
        String data = formInfo.get("param");
        if (!data.isEmpty()) {
            cn.hutool.json.JSONObject formInfos = new cn.hutool.json.JSONObject(data);
            customcode = formInfos.get("customcode").toString();
            parmStr=FileUtils.extractInsideBrackets(customcode);
            sascodeStr= FileUtils.extractOutsideBrackets(customcode);
        }
        JSONObject param = new JSONObject();
        param.put("parm", parmStr);
        param.put("sascode", sascodeStr);
        cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(data);
        param.put("studyId", studyId);
        param.put("cohort", formInfoData.get("cohort"));
        String paramStr = param.toString();
        //2.参数放入json，上传到minio
        String uuid = ULIDGenerator.generateULID();
        String paramFileName = formInfo.get("studyId").toString() + "_IRCDataTrans_" + uuid + ".json";
        //2.1 submit to sas via json params and define the json path on the minio storage platform
        formInfo.put("jsonMinioPath", SASOnlieConstant.PREFIX_PRO_MINIO+"/sdv/json/" + paramFileName);
        formInfo.put("sasCodePath", SASOnlieConstant.SAS_IRCDATATRANS_CODE_PATH);
        formInfo.put("paramFileName", paramFileName);
        formInfo.put("bucket", "dimage");
        formInfo.put("param", paramStr);
        formInfo.put("language", used_language);
        formInfo.put("uuid", uuid);

        //3.调用SAS接口，获取结果,将输出文件回填到cdtms
        List<Map<String, String>> sasOutputFilesInfo = new ArrayList<>();
        ENVInfo.put("ENV", "UAT");
        ENVInfo.put("data_type", "data_set");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", taskId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", projectId);
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("data_format", "SAS");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        ENVInfo.put("isLatest", "");
        ENVInfo.put("fileSuffix", "a".toString());
        Map<String,String> pyResult=callPython.downloadEDCServerFile(ENVInfo, uploadFilesFromEDC);
        String dataIsTwoDays = pyResult.get("dataIsTwoDays");
        if(dataIsTwoDays.equals("否")){
            return "数据是否在两天之内："+dataIsTwoDays;
        }
        String edcFileName = pyResult.get("original_name");
        if (edcFileName == null || edcFileName.isEmpty()) {
            //没有下载到数据集，使用表单上传的附件，上传到minio raw下,tag  example env:uat, key1:RAVE, key2:SHR-2010-201, key3:2024/09/27/13/17
            cdtmsapi.uploadSAS(studyId);
        }
        Map<String, String> result = submitSAS.submitToSAS(ENVInfo, uploadFilesFromEDC, formInfo, sasOutputFilesInfo);
        String outputName1 = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.IRC_SUFFIX_ONE;
        formInfo.put("outputName", outputName1);
        Map<String, String> tagInfo = minioUtil.getObjectTags(formInfo.get("bucket"), formInfo.get("outputName"));
        //获取日期
        String date = tagInfo.get("date");
        String fileDate1 = date;
        Boolean isSuccess1 = minioUtil.downloadGetObject(formInfo.get("bucket"), formInfo.get("outputName"));
        String outputName2 = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.IRC_SUFFIX_TWO;
        formInfo.put("outputName", outputName2);
        tagInfo = minioUtil.getObjectTags(formInfo.get("bucket"), formInfo.get("outputName"));
        //获取日期
        date = tagInfo.get("date");
        String fileDate2 = date;
        Boolean isSuccess2 = minioUtil.downloadGetObject(formInfo.get("bucket"), formInfo.get("outputName"));
        String outputName3 = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.IRC_SUFFIX_THREE;
        formInfo.put("outputName", outputName3);
        tagInfo = minioUtil.getObjectTags(formInfo.get("bucket"), formInfo.get("outputName"));
        //获取日期
        date = tagInfo.get("date");
        String fileDate3 = date;
        Boolean isSuccess3 = minioUtil.downloadGetObject(formInfo.get("bucket"), formInfo.get("outputName"));
        //单独下载，上传三个文件
        String ufn1 = "";
        String ufn2 = "";
        String ufn3 = "";
        if (isSuccess1) {
            String filePath1 = SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + formInfo.get("studyId").toString() + SASOnlieConstant.IRC_SUFFIX_ONE;
            ufn1 = CDTMSAPI.getUfn(filePath1, taskId, projectId);
        }
        if (isSuccess2) {
            String filePath2 = SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + formInfo.get("studyId").toString() + SASOnlieConstant.IRC_SUFFIX_TWO;
            ufn2 = CDTMSAPI.getUfn(filePath2, taskId, projectId);
        }
        if (isSuccess3) {
            String filePath3 = SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + formInfo.get("studyId").toString() + SASOnlieConstant.IRC_SUFFIX_THREE;
            ufn3 = CDTMSAPI.getUfn(filePath3, taskId, projectId);
        }

        //获取数据集日期
        tagInfo = minioUtil.getObjectTags("raw", formInfo.get("studyId").toString() + "_sas.zip");
        date = tagInfo.get("key3");
        String EDCDate = FileUtil.formatDate(date);
        //核查程序日期
        LocalDate currentDate = LocalDate.now();
        // Define the desired format
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // Format the date as a string
        String sasCheckDate = currentDate.format(formatter);

        String dataId = "";
        if (!data.isEmpty()) {
            cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();
            dataId = formInfoData.get("id").toString();
            cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
            temp.put("id", dataId);
            temp.put("edc_data_dt", EDCDate);
            temp.put("edc_dataset_t", formInfo.get("studyId").toString() + "_UAT_SAS_" + EDCDate + ".zip");
            temp.put("manual_rev_prog_version", "pgmout_dimage.sas");
            temp.put("crf_zt", "05");
            temp.put("output", formInfo.get("studyId").toString() + "_List_of_Visits_Median_" + fileDate1 + ".xlsx" + "*" + ufn1 + "|" + formInfo.get("studyId").toString() + "_Prior_Radiotherapy_Median_" + fileDate2 + ".xlsx" + "*" + ufn2 + "|" + formInfo.get("studyId").toString() + "_Safety_Status_Report_Median_" + fileDate3 + ".xlsx" + "*" + ufn3 + "|");
            params.put("data", temp);
            String newFormId = CDTMSAPI.getFormIdByTaskId(recordId, projectId);
            params.put("formId", newFormId);
            params.put("taskId", recordId);
            params.put("projectId", tableId);
            CDTMSAPI.dataSave(params);
            //upload the sas log report to cdtms API
            formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
            String logPath = result.get("sasLogPath").toString();
            File logFile = new File(logPath);
            FileUtil.uploadSASOutputFile(taskId, formId, "uat_log", logPath, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.REMOTE_SERVER_API_PREFIX, logFile.getName(), "log");
        }

        return "数据是否在两天之内："+dataIsTwoDays;
    }

    @Override
    public String getIRCDataBack(String taskId, String projectId) {
        //查询项目id
        //获取studyId
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId = "";
        String tableId = "";
        if (!ObjectUtils.isEmpty(formInfo.get("recordId"))) {
            recordId = formInfo.get("recordId");
            tableId = formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }


        List<Map<String, String>> uploadFilesFromEDC = new ArrayList<>();
        Map<String, String> ENVInfo = new HashMap<>();
        ENVInfo.put("taskId", taskId);
        ENVInfo.put("projectId", projectId);
        ENVInfo.put("isLatest", "YES");
        ENVInfo.put("ENV", "");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        ENVInfo.put("data_type", "cleanTool");
        String studyId = formInfo.get("studyId");
        //获取语言选项
        String used_language = CDTMSAPI.getStudyLanguage(studyId);
        //json参数
        String data = formInfo.get("param");
        String cohort = "";
        //json参数
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "manual_rev_prog", "obj.studyid=(select obj2.id from Xsht as obj2 where obj2.studyid='" + studyId + "') and obj.version_zt=4 and obj.checkarea=" + "03", "", URLEncoder.encode("createtime desc"));
        JSONArray jsonArray = JSONArray.parseArray(dataListInfo);
        String tmp = jsonArray.get(0).toString();
        JSONObject jsonObject = JSONObject.parseObject(tmp);
        String temps = jsonObject.toString();
        if (!temps.isEmpty()) {
            cn.hutool.json.JSONObject formInfos = new cn.hutool.json.JSONObject(temps);
            cohort = formInfos.get("cohort").toString();
        }


        JSONObject param = new JSONObject();
        param.put("studyId", studyId);
        param.put("cohort", cohort);
        String paramStr = param.toString();
        //2.参数放入json，上传到minio
        String uuid = ULIDGenerator.generateULID();
        String paramFileName = formInfo.get("studyId").toString() + "_IRCDataTrans_" + uuid + ".json";
        //2.1 submit to sas via json params and define the json path on the minio storage platform
        formInfo.put("jsonMinioPath", SASOnlieConstant.PREFIX_PRO_MINIO+"/sdv/json/" + paramFileName);
        formInfo.put("sasCodePath", SASOnlieConstant.SAS_IRCDATATRANS_CODE_PRO_PATH);
        formInfo.put("paramFileName", paramFileName);
        formInfo.put("bucket", "dimage");
        formInfo.put("param", paramStr);
        formInfo.put("language", used_language);
        formInfo.put("uuid", uuid);
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        //3.调用SAS接口，获取结果,将输出文件回填到cdtms

        List<Map<String, String>> sasOutputFilesInfo = new ArrayList<>();
        Map<String, String> ENVInfoA = new HashMap<>();
        ENVInfoA.put("ENV", "PRO");
        ENVInfoA.put("data_type", "data_set");
        ENVInfoA.put("studyId", studyId);
        ENVInfoA.put("taskId", recordId);
        ENVInfoA.put("formId", formId);
        ENVInfoA.put("projectId", tableId);
        ENVInfoA.put("uuid", uuid);
        ENVInfoA.put("data_format", "SAS");
        ENVInfoA.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        ENVInfoA.put("isLatest", "");
        ENVInfoA.put("fileSuffix", "a".toString());
        Map<String,String> pyResult=callPython.downloadEDCServerFile(ENVInfo, uploadFilesFromEDC);
        String fileName = pyResult.get("original_name");
        SASCheckContentServiceImpl.log.info("-----------调用数据集下载程序返回的文件名是:{}", fileName);
        if (!ObjectUtils.isEmpty(fileName) && !fileName.isEmpty()) {
            Map<String, String> result = submitSAS.submitToSAS(ENVInfo, uploadFilesFromEDC, formInfo, sasOutputFilesInfo);


            String outputName1 = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.IRC_SUFFIX_ONE;
            formInfo.put("outputName", outputName1);
            //文件下载
            Boolean isSuccess1 = minioUtil.downloadGetObject(formInfo.get("bucket"), formInfo.get("outputName"));
            Map<String, String> tagInfo = minioUtil.getObjectTags(formInfo.get("bucket"), formInfo.get("outputName"));
            //获取日期
            String date = tagInfo.get("date");
            String fileDate1 = date;
            String outputName2 = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.IRC_SUFFIX_TWO;
            formInfo.put("outputName", outputName2);
            Boolean isSuccess2 = minioUtil.downloadGetObject(formInfo.get("bucket"), formInfo.get("outputName"));
            tagInfo = minioUtil.getObjectTags(formInfo.get("bucket"), formInfo.get("outputName"));
            date = tagInfo.get("date");
            String fileDate2 = date;
            String outputName3 = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.IRC_SUFFIX_THREE;
            formInfo.put("outputName", outputName3);
            Boolean isSuccess3 = minioUtil.downloadGetObject(formInfo.get("bucket"), formInfo.get("outputName"));
            tagInfo = minioUtil.getObjectTags(formInfo.get("bucket"), formInfo.get("outputName"));
            date = tagInfo.get("date");
            String fileDate3 = date;
            //单独下载，上传三个文件
            String ufn1 = "";
            String ufn2 = "";
            String ufn3 = "";
            if (isSuccess1) {
                String filePath1 = SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + formInfo.get("studyId").toString() + SASOnlieConstant.IRC_SUFFIX_ONE;
                //重命名文件
                ufn1 = CDTMSAPI.getUfn(filePath1, taskId, projectId);
            }
            if (isSuccess2) {
                String filePath2 = SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + formInfo.get("studyId").toString() + SASOnlieConstant.IRC_SUFFIX_TWO;
                ufn2 = CDTMSAPI.getUfn(filePath2, taskId, projectId);
            }
            if (isSuccess3) {
                String filePath3 = SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + formInfo.get("studyId").toString() + SASOnlieConstant.IRC_SUFFIX_THREE;
                ufn3 = CDTMSAPI.getUfn(filePath3, taskId, projectId);
            }


            String dataId = "";
            if (!data.isEmpty()) {
                cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(data);
                cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();
                dataId = formInfoData.get("id").toString();
                cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
                temp.put("id", dataId);
                //获取edc数据集的日期
                Map<String, String> objectTags = minioUtil.getObjectTags("raw", formInfo.get("studyId").toString() + "_sas.zip");
                String edcFileDate = objectTags.get("key3");
                edcFileDate = fileUtils.formatDate(edcFileDate);
                temp.put("edc_dataset_t", formInfo.get("studyId").toString() + "_PRO_SAS_" + edcFileDate + ".zip");
                temp.put("output", formInfo.get("studyId").toString() + "_List_of_Visits_Median_" + fileDate1 + ".xlsx" + "*" + ufn1 + "|" + formInfo.get("studyId").toString() + "_Prior_Radiotherapy_Median_" + fileDate2 + ".xlsx" + "*" + ufn2 + "|" + formInfo.get("studyId").toString() + "_Safety_Status_Report_Median_" + fileDate3 + ".xlsx" + "*" + ufn3 + "|");
                params.put("data", temp);
                String newFormId = CDTMSAPI.getFormIdByTaskId(recordId, projectId);
                params.put("formId", newFormId);
                params.put("taskId", recordId);
                params.put("projectId", tableId);
                CDTMSAPI.dataSave(params);
            }


            //upload the sas log report to cdtms API
            formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
            String logPath = result.get("sasLogPath").toString();
            File logFile = new File(logPath);
            FileUtil.uploadSASOutputFile(taskId, formId, "uat_log", logPath, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.REMOTE_SERVER_API_PREFIX, logFile.getName(), "log");
        }else{
            return "该项目EDC没有导出的数据";
        }

        return "success";
    }

    @Override
    public Map<String, String> getLABAE(String taskId, String projectId) {
        Map<String,String> result=new HashMap<>();
        List<Map<String, String>> uploadFilesFromEDC = new ArrayList<>();
        Map<String, String> fileObject1 = new HashMap<>();
        fileObject1.put("fid", "edc_dataset");
        fileObject1.put("fileType", ".zip");
        uploadFilesFromEDC.add(fileObject1);
        Map<String, String> ENVInfo = new HashMap<>();

        //1.拿到页面的参数，studyId ,核查内容，肿瘤影像学选项
        String dataId = "";
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        //json参数
        String formData = formInfo.get("param");
        cn.hutool.json.JSONObject formInfoDatas = new cn.hutool.json.JSONObject(formData);
        dataId = formInfoDatas.get("id").toString();

        String uuid = ULIDGenerator.generateULID();
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        //上传进行阶段als到minio
        minioUtil.uploadALSPROFile(ENVInfo,studyId,taskId,formId,projectId);
        ENVInfo.put("ENV", "PRO");
        ENVInfo.put("data_type", "data_set");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", taskId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", projectId);
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("data_format", "SAS");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        ENVInfo.put("isLatest", "");
        ENVInfo.put("fileSuffix", "a".toString());
        Map<String,String> pyResult=callPython.downloadEDCServerFile(ENVInfo, uploadFilesFromEDC);
        String fileName = pyResult.get("original_name");
        String dataIsTwoDays= pyResult.get("dataIsTwoDays");
        if(dataIsTwoDays.equals("否")){
            result.put("dataIsTwoDays","数据是否在两天之内："+dataIsTwoDays);
            return result;
        }
        String EDCTime = FileUtil.transferEDCFIleName(fileName);

        //获取语言选项
        String used_language = CDTMSAPI.getStudyLanguage(studyId);
        //json参数
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "manual_rev_prog", "obj.studyid=(select obj2.id from Xsht as obj2 where obj2.studyid='" + studyId + "') and obj.version_zt=4 and obj.checkarea=" + "50", "", URLEncoder.encode("createtime desc"));
        JSONArray jsonArray = JSONArray.parseArray(dataListInfo);
        String tmp = jsonArray.get(0).toString();
        JSONObject jsonObject = JSONObject.parseObject(tmp);
        String data = jsonObject.toString();
        SASCheckContentServiceImpl.log.info("------------------------------------------------------获取到的设计阶段的sas核查程序的内容是：{}" + data);
        String customcode = "";
        String sascodeStr = "";
        String parmStr = "";
        if (!data.isEmpty()) {
            cn.hutool.json.JSONObject formInfos = new cn.hutool.json.JSONObject(data);
            customcode = formInfos.get("customcode").toString();
            parmStr = FileUtils.extractInsideBrackets(customcode);
            sascodeStr = FileUtils.extractOutsideBrackets(customcode);
        }
        JSONObject param = new JSONObject();
        param.put("parm", parmStr);
        param.put("sascode", sascodeStr);
        String paramStr = param.toString();
        //2.参数放入json，上传到minio
        String paramFileName = formInfo.get("studyId").toString() + "_AE-EX-EOT" + ".json";
        //2.1 submit to sas via json params and define the json path on the minio storage platform
        formInfo.put("jsonMinioPath", SASOnlieConstant.PREFIX_PRO_MINIO+"/sdv/json/" + paramFileName);
        formInfo.put("sasCodePath", SASOnlieConstant.SAS_AEOXT_CODE_PATH);
        formInfo.put("paramFileName", paramFileName);
        formInfo.put("bucket", "sdv");
        formInfo.put("param", paramStr);
        formInfo.put("language", used_language);
        formInfo.put("uuid", uuid);

        //3.调用SAS接口，获取结果,将输出文件回填到cdtms
        List<Map<String, String>> sasOutputFilesInfo = new ArrayList<>();
        Map<String, String> sasOutputFile = new HashMap<>();
        sasOutputFile.put("fid", "output");

        String outputName = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.AEOXT;
        sasOutputFile.put("outputName", outputName);
        formInfo.put("outputName", outputName);
        sasOutputFilesInfo.add(sasOutputFile);
        result = submitSAS.submitToSAS(ENVInfo, uploadFilesFromEDC, formInfo, sasOutputFilesInfo);


        //核查程序日期
        LocalDate currentDate = LocalDate.now();

        // Define the desired format
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // Format the date as a string
        String sasCheckDate = currentDate.format(formatter);


        if (!data.isEmpty()) {
            cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(data);
            cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();

            cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
            temp.put("id", dataId);
            temp.put("edc_data_dt", EDCTime);
            temp.put("edc_dataset", fileName);
            temp.put("mr_plan_version_date", sasCheckDate);
            temp.put("manual_rev_prog_version", "AE-EX-EOT");
            params.put("data", temp);
            String newFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
            params.put("formId", newFormId);
            params.put("taskId", taskId);
            params.put("projectId", projectId);
            CDTMSAPI.dataSave(params);
        }

        //upload the sas log report to cdtms API
        formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String logPath = result.get("sasLogPath").toString();
        File logFile = new File(logPath);
        FileUtil.uploadSASOutputFile(taskId, formId, "uat_log", logPath, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.REMOTE_SERVER_API_PREFIX, logFile.getName(), "log");
       result.put("dataIsTwoDays","数据是否在两天之内："+dataIsTwoDays);
        return result;
    }

    @Override
    public Map<String, String> getRecist(String taskId, String projectId) {
        Map<String,String> result = new HashMap<>();
        List<Map<String, String>> uploadFilesFromEDC = new ArrayList<>();
        Map<String, String> fileObject1 = new HashMap<>();
        fileObject1.put("fid", "edc_dataset");
        fileObject1.put("fileType", ".zip");
        uploadFilesFromEDC.add(fileObject1);
        //1.拿到页面的参数，studyId ,核查内容，肿瘤影像学选项
        String dataId = "";
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        //json参数
        String formData = formInfo.get("param");
        cn.hutool.json.JSONObject formInfoDatas = new cn.hutool.json.JSONObject(formData);
        dataId = formInfoDatas.get("id").toString();

        String uuid = ULIDGenerator.generateULID();
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        Map<String, String> ENVInfo = new HashMap<>();
        //上传进行阶段als到minio
        minioUtil.uploadALSPROFile(ENVInfo,studyId,taskId,formId,projectId);
        ENVInfo.put("ENV", "PRO");
        ENVInfo.put("data_type", "data_set");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", taskId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", projectId);
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("data_format", "SAS");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        ENVInfo.put("isLatest", "");
        ENVInfo.put("fileSuffix", "a".toString());
        Map<String,String> pyResult=callPython.downloadEDCServerFile(ENVInfo, uploadFilesFromEDC);
        String fileName = pyResult.get("original_name");
        String dataIsTwoDays= pyResult.get("dataIsTwoDays");
        if(dataIsTwoDays.equals("否")){
            result.put("dataIsTwoDays","数据是否在两天之内："+dataIsTwoDays);
            return result;
        }
        String EDCTime = FileUtil.transferEDCFIleName(fileName);

        ENVInfo.put("taskId", taskId);
        ENVInfo.put("projectId", projectId);
        ENVInfo.put("isLatest", "YES");
        ENVInfo.put("ENV", "");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        ENVInfo.put("data_type", "cleanTool");


        //获取语言选项
        String used_language = CDTMSAPI.getStudyLanguage(studyId);
        //json参数
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "manual_rev_prog", "obj.studyid=(select obj2.id from Xsht as obj2 where obj2.studyid='" + studyId + "') and obj.version_zt=4 and obj.checkarea=70 ", "", URLEncoder.encode("createtime desc"));
        JSONArray jsonArray = JSONArray.parseArray(dataListInfo);
        String tmp = jsonArray.get(0).toString();
        JSONObject jsonObject = JSONObject.parseObject(tmp);
        String data = jsonObject.toString();
        String isAdd = "";
        String parmStr="";
        String sascodeStr="";
        String customcode = "";
        if (!data.isEmpty()) {
            SASCheckContentServiceImpl.log.info("测试阶段上线版最新的一条记录:"+data);
            cn.hutool.json.JSONObject formInfos = new cn.hutool.json.JSONObject(data);
            isAdd = formInfos.get("isadd").toString();
            customcode = formInfos.get("customcode").toString();
            parmStr=FileUtils.extractInsideBrackets(customcode);
            sascodeStr= FileUtils.extractOutsideBrackets(customcode);
        }
        JSONObject param = new JSONObject();
        param.put("isadd", isAdd);
        param.put("system", "HRTAU4");
        param.put("parm", parmStr);
        param.put("sascode", sascodeStr);
        String paramStr = param.toString();
        //获取测试阶段上线版最新的一条记录的参数作为参数
        SASCheckContentServiceImpl.log.info("需要传递的参数为:"+paramStr);

        //2.参数放入json，上传到minio
        String paramFileName = formInfo.get("studyId").toString() + "_Recist1.1_" + uuid + ".json";
        //2.1 submit to sas via json params and define the json path on the minio storage platform
        formInfo.put("jsonMinioPath", SASOnlieConstant.PREFIX_PRO_MINIO+"/sdv/json/" + paramFileName);
        formInfo.put("sasCodePath", SASOnlieConstant.SAS_RECIST_CODE_PATH);
        formInfo.put("paramFileName", paramFileName);
        formInfo.put("bucket", "sdv");
        formInfo.put("param", paramStr);
        formInfo.put("language", used_language);
        formInfo.put("uuid", uuid);


        //3.调用SAS接口，获取结果,将输出文件回填到cdtms
        List<Map<String, String>> sasOutputFilesInfo = new ArrayList<>();
        Map<String, String> sasOutputFile = new HashMap<>();
        sasOutputFile.put("fid", "output");
        String outputName1 = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.RECIST_SUFFIX;
        sasOutputFile.put("fid", "output");
        String outputName2 = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.RECIST_SUFFIX2;


        result = submitSAS.submitToSAS(ENVInfo, uploadFilesFromEDC, formInfo, sasOutputFilesInfo);
        Boolean isSuccess1 = minioUtil.downloadGetObject(formInfo.get("bucket"), outputName1);
        Boolean isSuccess2 = minioUtil.downloadGetObject(formInfo.get("bucket"), outputName2);
        String ufn1 = "";
        String ufn2 = "";
        if (isSuccess1) {
            String filePath1 = SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + formInfo.get("studyId").toString() + SASOnlieConstant.RECIST_SUFFIX;
            ufn1 = CDTMSAPI.getUfn(filePath1, taskId, projectId);
        }
        if (isSuccess2) {
            String filePath2 = SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + formInfo.get("studyId").toString() + SASOnlieConstant.RECIST_SUFFIX2;
            ufn2 = CDTMSAPI.getUfn(filePath2, taskId, projectId);
        }



        //获取数据集日期
        Map<String, String> tagInfo = minioUtil.getObjectTags("raw", formInfo.get("studyId").toString() + "_sas.zip");
        String date = tagInfo.get("key3");
        String EDCDate = FileUtil.formatDate(date);


        //核查程序日期
        LocalDate currentDate = LocalDate.now();

        // Define the desired format
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // Format the date as a string
        String sasCheckDate = currentDate.format(formatter);


        if (!data.isEmpty()) {
            cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(data);
            cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();

            cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
            temp.put("id", dataId);
            temp.put("edc_data_dt", EDCTime);
            temp.put("edc_dataset_t",fileName);
            temp.put("mr_plan_version_date", sasCheckDate);
            temp.put("manual_rev_prog_version", "Recist1.1");
            temp.put("output", formInfo.get("studyId").toString() +  SASOnlieConstant.RECIST_SUFFIX + "*" + ufn1 + "|" + formInfo.get("studyId").toString() + SASOnlieConstant.RECIST_SUFFIX2  + "*" + ufn2+ "|");

            params.put("data", temp);
            String newFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
            params.put("formId", newFormId);
            params.put("taskId", taskId);
            params.put("projectId", projectId);

            CDTMSAPI.dataSave(params);
        }


        //upload the sas log report to cdtms API
        formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String logPath = result.get("sasLogPath").toString();
        File logFile = new File(logPath);
        FileUtil.uploadSASOutputFile(taskId, formId, "uat_log", logPath, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.REMOTE_SERVER_API_PREFIX, logFile.getName(), "log");
        result.put("dataIsTwoDays","数据是否在两天之内："+dataIsTwoDays);
        return result;
    }


    @Autowired
    EDMCDTMSInfoMapper edmcdtmsInfoMapper;

    @Override
    public Map<String, String> getRealLABAE(String taskId, String projectId) {
        Map<String,String> result=new HashMap<>();
        List<Map<String, String>> uploadFilesFromEDC = new ArrayList<>();
        Map<String, String> fileObject1 = new HashMap<>();
        fileObject1.put("fid", "edc_dataset");
        fileObject1.put("fileType", ".zip");
        uploadFilesFromEDC.add(fileObject1);

        Map<String, String> ENVInfo = new HashMap<>();
        //1.拿到页面的参数，studyId ,核查内容，肿瘤影像学选项
        String dataId = "";
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        //json参数
        String formData = formInfo.get("param");
        cn.hutool.json.JSONObject formInfoDatas = new cn.hutool.json.JSONObject(formData);
        dataId = formInfoDatas.get("id").toString();

        String uuid = ULIDGenerator.generateULID();
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        //上传进行阶段als 到minio
        minioUtil.uploadALSPROFile(ENVInfo,studyId,taskId,formId,projectId);
        ENVInfo.put("ENV", "PRO");
        ENVInfo.put("data_type", "data_set");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", taskId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", projectId);
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("data_format", "SAS");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        ENVInfo.put("isLatest", "");
        ENVInfo.put("fileSuffix", "a".toString());
        Map<String,String> pyResult=callPython.downloadEDCServerFile(ENVInfo, uploadFilesFromEDC);
        String fileName = pyResult.get("original_name");
        String dataIsTwoDays= pyResult.get("dataIsTwoDays");
        if(dataIsTwoDays.equals("否")){
            result.put("dataIsTwoDays","数据是否在两天之内："+dataIsTwoDays);
            return result;
        }
        String EDCTime = FileUtil.transferEDCFIleName(fileName);

        //获取语言选项
        String used_language = CDTMSAPI.getStudyLanguage(studyId);


        JSONObject param = new JSONObject();
        String edcName = edmcdtmsInfoMapper.getEDCName(studyId);
        if (!ObjectUtils.isEmpty(edcName) && !edcName.isEmpty()) {
            if (edcName.equals("TAU")) {
                param.put("system", "HRTAU4");
            } else if (edcName.equals("Rave")) {
                param.put("system", "RAVE");
            }
        }

        String sascodeStr="";
        String parmStr="";
        String  customcode="";
        if (!formData.isEmpty()) {
            cn.hutool.json.JSONObject formInfos = new cn.hutool.json.JSONObject(formData);
            customcode = formInfos.get("customcode").toString();
            parmStr=FileUtils.extractInsideBrackets(customcode);
            sascodeStr= FileUtils.extractOutsideBrackets(customcode);
        }

        param.put("parm", parmStr);
        param.put("sascode", sascodeStr);

        String paramStr = param.toString();
        //2.参数放入json，上传到minio
        String paramFileName = formInfo.get("studyId").toString() + "_LAB-AE" + ".json";
        //2.1 submit to sas via json params and define the json path on the minio storage platform
        formInfo.put("jsonMinioPath", SASOnlieConstant.PREFIX_PRO_MINIO+"/sdv/json/" + paramFileName);
        formInfo.put("sasCodePath", SASOnlieConstant.SAS_LABAE_PRO_CODE_PATH);
        formInfo.put("paramFileName", paramFileName);
        formInfo.put("bucket", "sdv");
        formInfo.put("param", paramStr);
        formInfo.put("language", used_language);
        formInfo.put("uuid", uuid);

        //3.调用SAS接口，获取结果,将输出文件回填到cdtms
        List<Map<String, String>> sasOutputFilesInfo = new ArrayList<>();
        Map<String, String> sasOutputFile = new HashMap<>();
        sasOutputFile.put("fid", "output");

        String outputName = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.LAB_AE;
        sasOutputFile.put("outputName", outputName);
        formInfo.put("outputName", outputName);
        sasOutputFilesInfo.add(sasOutputFile);
        result = submitSAS.submitToSAS(ENVInfo, uploadFilesFromEDC, formInfo, sasOutputFilesInfo);


        //核查程序日期
        LocalDate currentDate = LocalDate.now();

        // Define the desired format
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // Format the date as a string
        String sasCheckDate = currentDate.format(formatter);


        cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();

        cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
        temp.put("id", dataId);
        temp.put("edc_data_dt", EDCTime);
        temp.put("edc_dataset_t", fileName);
        temp.put("mr_plan_version_date", sasCheckDate);
        temp.put("manual_rev_prog_version", "LAB-AE");
        params.put("data", temp);
        String newFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        params.put("formId", newFormId);
        params.put("taskId", taskId);
        params.put("projectId", projectId);
        CDTMSAPI.dataSave(params);


        //upload the sas log report to cdtms API
        formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String logPath = result.get("sasLogPath").toString();
        File logFile = new File(logPath);
        FileUtil.uploadSASOutputFile(taskId, formId, "uat_log", logPath, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.REMOTE_SERVER_API_PREFIX, logFile.getName(), "log");
        result.put("dataIsTwoDays","数据是否在两天之内："+dataIsTwoDays);
        return result;
    }
}