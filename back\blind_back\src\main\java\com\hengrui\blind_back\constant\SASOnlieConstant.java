package com.hengrui.blind_back.constant;

import org.springframework.stereotype.Component;

/**
 * @ClassName SASOnlieConstant
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/21 14:43
 * @Version 1.0
 **/
@Component
public class SASOnlieConstant {
    public static final String ECRF_SUFFIX = "_eCRF_unlock_check.xlsx";
    public static String ECRF_BUCKET="ecrfunlock/";
    public static String UAT_BUCKET="uat/json/";


    public static String SAS_BLIND_LOG_PATH="/home/<USER>/8087/sas_call_log";
    public  static String ECRF_LIST_FOLDER="/home/<USER>/8087/ECRF";
    //public  static String ECRF_LIST_FOLDER="C:\\MyFile\\testFile";
    public static String EDC_DATA_LOCAL_FOLDER="/home/<USER>/edcserver";

    public static final String UAT_SUFFIX = "_UAT_dataset_check.xlsx";

    public static final String RECIST_SUFFIX = "_肿瘤核查工具1.xlsx";

    public static final String RECIST_SUFFIX2 = "_肿瘤核查工具2.xlsx";
    public static final String IRC_SUFFIX_ONE = "_List_of_Visits_Median.xlsx";
    public static final String IRC_SUFFIX_TWO = "_Prior_Radiotherapy_Median.xlsx";
    public static final String IRC_SUFFIX_THREE = "_Safety_Status_Report_Median.xlsx";

    public static final String AEOXT = "_AE-EX-EOT.xlsx";

    public static final String LAB_AE = "_LAB-AE.xlsx";

 public static final String DMSTAGE_SUFFIX = "_PD.xlsx";

    public static final String PROGRESS_REPORT_SUFFIX = "_进展报告.xlsx";
    public static final String COMP_SUFFIX = "_compare.xlsx";

    public static final String RTSM_COMP_CH_SUFFIX = "_随机化与供应管理数据一致性比对报告.xlsx";

    public static final String RTSM_COMP_CH__INIT_SUFFIX = "_随机化与供应管理数据一致性比对报告_init.xlsx";

    public static final String RTSM_COMP_EN_SUFFIX = "_RTSM Data Reconciliation Report.xlsx";

    public static final String RTSM_COMP_EN_INIT_SUFFIX = "_RTSM Data Reconciliation Report_init.xlsx";

    public static final String PPC_SUFFIX = "_ProTree_input.xlsx";
    public static final String _SUFFIX = "_UAT.zip";
    public  static final String ECRF_SAS_OUTPUT_BUCKET="dmreview";

    public  static final String UAT_SAS_OUTPUT_BUCKET="uat";

//    public static String SAS_CODE_PATH="C:\\Users\\<USER>\\Desktop\\test.txt";

    public static String SAS_CODE_PATH="/home/<USER>/8087/sas_code/test.txt";
    public static String EDC_API_PRFIX="https://meduap-tst.hengrui.com:89/";


    public static String SAS_RECIST_CODE_PATH="/home/<USER>/8087/sas_code/recist.txt";

    public static String SAS_RECIST_UAT_CODE_PATH="/home/<USER>/8087/sas_code/recist_back.txt";

    public static String SAS_IRCDATATRANS_CODE_PATH="/home/<USER>/8087/sas_code/ircDataTrans.txt";

    public static String SAS_IRCDATATRANS_CODE_PRO_PATH="/home/<USER>/8087/sas_code/ircDataTransPRO.txt";
    public static String SAS_AEOXT_CODE_PATH="/home/<USER>/8087/sas_code/aexot.txt";

    public static String SAS_LABAE_CODE_PATH="/home/<USER>/8087/sas_code/lab-ae.txt";

    public static String SAS_LABAE_PRO_CODE_PATH="/home/<USER>/8087/sas_code/lab-ae-pro.txt";
    public static String SAS_AEOXT_UAT_CODE_PATH="/home/<USER>/8087/sas_code/aexot_back.txt";

 public static String SAS_DMSTAGE_CODE_PATH="/home/<USER>/8087/sas_code/dmstage.txt";

    public static String SAS_PROGRESS_REPORT_CODE_PATH="/home/<USER>/8087/sas_code/progress_report_sas.txt";
    public static String RTSM_SAS_COMP_CODE_PRO_PATH="/home/<USER>/8087/sas_code/rtsm_sas_pro.txt";
    public static String RTSM_SAS_COMP_CODE_UAT_PATH="/home/<USER>/8087/sas_code/rtsm_sas_uat.txt";
    public static String RTSM_MED_UTR_PATH="/home/<USER>/8087/sas_code/med_utr.txt";
    public static String SAS_PROTOCOL_CHART_CODE_PATH="/home/<USER>/8087/sas_code/m_protocol_run.txt";
    public static String SAS_OUTPUT_PATH="/home/<USER>/8087/sas_output";

    public static String SAS_DATA_LOCAL_FOLDER="/home/<USER>/8087/dbs/";


    public static String REMOTE_SERVER_API_PREFIX="meduap-tst.hengrui.com:8085/";
//    public static String REMOTE_SERVER_API_PREFIX="https://cdtms.hengrui.com";
//    public static String REMOTE_SERVER_API_PREFIX="cdtms-pilot.hengrui.com";


    public static String RTSM_GENERATOR_URL="https://rtsmgenerator-tst.hengrui.com/#/onlyOffice?studyId=";

   public static String REMOTE_SERVER_API="https://meduap-tst.hengrui.com:8085/";
//    public static String REMOTE_SERVER_API="https://cdtms.hengrui.com/";
//    public static String REMOTE_SERVER_API="https://cdtms-pilot.hengrui.com/";

    public static String REMOTE_SERVER_PROJECTID="cdtmsen_val";

    public static void setRemoteServerApi(String apiUrl) {
        REMOTE_SERVER_API = apiUrl;
    }
    public static void setRemoteServerProjectid(String projectid) {
        REMOTE_SERVER_PROJECTID = projectid;
    }
    public static void setRemoteServerApiPrefix(String apiUrl) {
        REMOTE_SERVER_API_PREFIX = apiUrl;
    }

    public static void setEDCApiPrefix(String apiUrl) {
        EDC_API_PRFIX = apiUrl;
    }


    public static void setRTSMApiPrefix(String apiUrl) {
        RTSM_API = apiUrl;
    }

    public static void setCdtmsCheckLoginApi(String apiUrl){
        CDTMS_MED_CHECK_LOGIN_API=apiUrl;
    }


//    public static String CDTMS_TST_CHECK_LOGIN_API="https://cdtms-tst.hengrui.com/extdatabind.checktoken.do?token=";

   public static String CDTMS_MED_CHECK_LOGIN_API="https://meduap-tst.hengrui.com:8085/extdatabind.checktoken.do?token=";

//    public static String CDTMS_MED_CHECK_LOGIN_API="https://cdtms.hengrui.com/extdatabind.checktoken.do?token=";

//    public static String CDTMS_MED_CHECK_LOGIN_API="https://cdtms-pilot.hengrui.com/extdatabind.checktoken.do?token=";

    public static String EDC_API="http://************:5000/";

//   public static String EDC_API="http://***********:5000/";
    public static String RTSM_API="https://clinical-tst.hengruipharma.com:88/";
   public static String RTSM_FILE_PATH="/home/<USER>/8087/rtsm_file/";

//    public static String RTSM_FILE_PATH="C:\\Work\\随机化生成平台\\file\\";

    public static String RTSM_API_USER="<EMAIL>";

    public static String RTSM_API_PASS="Zh654321";

    public static String EDC_API_USER="<EMAIL>";
    //meduap-tst
    public static String EDC_API_PASS="Hr123456";
    //cdtms-pilot
//    public static String EDC_API_PASS="Hryy7777";


    public static String SECRET_KEY="5AF2E70E-1E6D-47B4-A9C5-3809448E11E8";

    //本服务域名
    public static String SAS_ONLINE="https://sas-online-tst.hengrui.com/sas_online/";

//   public static String SAS_ONLINE="https://cdtms-api.hengrui.com:8087/sas_online/";


    //电子签-链接前缀
//    public static String ESIGN_API_PREFIX="https://clinical-esign-val.hengrui.com/";

    //public static String ESIGN_API_PREFIX="http://localhost:9528/#/signPage";

    public static String ESIGN_API_PREFIX="https://rtsmgenerator-tst.hengrui.com//#/signPage";

    public static String RTSMGEN_API_PREFIX="https://rtsmgenerator-tst.hengrui.com/";
    //电子签-文件
    public static String ESIGN_TST_FILE="https://clinical-esign-val.hengrui.com/api/files";

    //电子签-任务
    public static String ESIGN_TST_TASK="https://clinical-esign-val.hengrui.com/api/tasks";
    //电子签-设置签字位置
    public static String ESIGN_SET_SIGN="https://clinical-esign-val.hengrui.com/set-sign?file_id=";

    public static String SAS_USER="satableau";

    public static String SAS_PASS="Hr@tableau0401";


//    public static String SAS_USER="jinyh";
//
//    public static String SAS_PASS="68320hc!?jyh520";


//   public static String SMO_MINIO_URL="http://**********:9000/smo/";

    public static String SMO_MINIO_URL="http://***********:9000/smo/";

    public static String MINIO_URL="http://***********:9000";

// public static String MINIO_URL="http://**********:9000";


    public static String PY_PRO_TEST="val";

//    public static String PY_PRO_TEST="pro";

    public static String PREFIX_PRO_MINIO="minios3";

    public static String PREFIX_TST_MINIO="minios3-t";

//    public static String PREFIX_TST_MINIO="minios3";


    public static String SASONLINE_SERVER_DOMAIN="https://sas-online-tst.hengrui.com/sas_online/";
}
