package com.hengrui.blind_back.smo_data.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Component
@Mapper
@Repository
@DS("slave6")
public interface SMODataMapper {

    List<Map<String, String>> getLabTableHeader(String filePath);

    List<Map<String, String>> getTableContent(String filePath, List<String> siteNum);

    List<Map<String, String>> getENTableContent(String filePath, List<String> siteNum);
    List<Map<String, String>> getENHRTableContent(String filePath, List<String> siteNum);
    List<Map<String, String>> getLabTableNum(String filePath);

}
