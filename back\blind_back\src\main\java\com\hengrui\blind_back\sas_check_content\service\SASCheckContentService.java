package com.hengrui.blind_back.sas_check_content.service;

import java.util.Map;

public interface SASCheckContentService {
    String testSASCheckContent(String taskId, String projectId);

    String getIRCData(String taskId, String projectId);

    String getIRCDataBack(String taskId, String projectId);

    Map<String, String> getLABAE(String taskId, String projectId);
    Map<String, String> getRealLABAE(String taskId, String projectId);


    Map<String, String> getRecist(String taskId, String projectId);
}
