package com.hengrui.blind_back.rtsm.job;

import com.hengrui.blind_back.rtsm.service.RTSMService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SignTaskScheduler {

    @Autowired
    private RTSMService rtsmService;

    /**
     * 每小时检查一次临期签字任务
     * 每小时的第0分钟执行
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void checkExpiringSignTasks() {
        log.info("开始检查临期签字任务...");
        rtsmService.checkExpiringSignTasks();
        log.info("临期签字任务检查完成");
    }
}