package com.hengrui.blind_back.medcoding_plan.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.hengrui.blind_back.blind.utils.MinioUtil;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.ecrf_unlock.utils.FileUtil;
import com.hengrui.blind_back.medcoding_plan.service.MedCodingPlanService;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.hengrui.blind_back.utils.SubmitSAS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * @ClassName MedCodingPlanServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/2/20 15:54
 * @Version 1.0
 **/

@Service
@Slf4j
public class MedCodingPlanServiceImpl implements MedCodingPlanService {

    @Autowired
    SubmitSAS submitSAS;

    /**
     * 比对并更新编码历史
     *
     * @param taskId
     * @param projectId
     * @return
     */
    @Override
    public String updateCodingHis(String taskId, String projectId) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        String data = formInfo.get("param").toString();
        String ver_date = "";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "esign_account");
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "study_coding_plan", "obj.status='10' and obj.study_id='" + studyInt + "'", "edit", "");
            String codingPlanInfo = CDTMSAPI.getDataListInfo(token, "coding_name", "obj.studyid='" + studyInt + "'", "edit", "");
            String medDraSubTable = CDTMSAPI.getDataListInfo(token, "study_coding_ver_update", "obj.study_id ='" + studyInt + "'", "edit", "");
            String whoDraSubTable = CDTMSAPI.getDataListInfo(token, "study_coding_ver_update1", "obj.study_id ='" + studyInt + "'", "edit", "");
            JSONArray objects = JSONArray.parseArray(result);
            JSONArray codingPlanObjs = JSONArray.parseArray(codingPlanInfo);
            JSONArray medDraSubTableObjs = JSONArray.parseArray(medDraSubTable);
            JSONArray whoDraSubTableObjs = JSONArray.parseArray(whoDraSubTable);
            if (objects.size() > 1 && !data.isEmpty()) {
                String codeDict = codingPlanObjs.getJSONObject(codingPlanObjs.size() - 1).get("id").toString();
                MedCodingPlanServiceImpl.log.info("-----------------------------------------项目版本值是：" + codeDict);
                cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();
                ver_date = objects.getJSONObject(0).get("ver_date").toString();
                cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
                //比对前两个元素中的
                String codingmeddraVer1 = objects.getJSONObject(0).get("meddra_ver").toString();
                String codingmeddraVer2 = objects.getJSONObject(1).get("meddra_ver").toString();
                if (!StringUtils.isEmpty(codingmeddraVer1) && !StringUtils.isEmpty(codingmeddraVer2) && !codingmeddraVer1.equals(codingmeddraVer2) && codingPlanObjs.size() > 0) {
                    if (medDraSubTableObjs.size() > 0) {
                        String ver = medDraSubTableObjs.getJSONObject(0).get("ver").toString();
                        // String change_date = medDraSubTableObjs.getJSONObject(0).get("change_date").toString();
                        if (!codingmeddraVer1.equals(ver)) {
                            MedCodingPlanServiceImpl.log.info("codingmeddraVer1:" + codingmeddraVer1 + "codingmeddraVer2:" + codingmeddraVer2 + "codingPlanObjs.size():" + codingPlanObjs.size());
                            temp.put("study_id", studyInt);
                            temp.put("change_date", ver_date);
                            temp.put("study_coding_edition_id", codeDict);
                            temp.put("ver", codingmeddraVer1);
                            params.put("data", temp);
                            String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
                            CDTMSAPI.usersyndataSave(token, "study_coding_ver_update", formId, "", "", temp.toString());
                        }
                    } else {
                        MedCodingPlanServiceImpl.log.info("codingmeddraVer1:" + codingmeddraVer1 + "codingmeddraVer2:" + codingmeddraVer2 + "codingPlanObjs.size():" + codingPlanObjs.size());
                        temp.put("study_id", studyInt);
                        temp.put("change_date", ver_date);
                        temp.put("study_coding_edition_id", codeDict);
                        temp.put("ver", codingmeddraVer1);
                        params.put("data", temp);
                        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
                        CDTMSAPI.usersyndataSave(token, "study_coding_ver_update", formId, "", "", temp.toString());
                    }

                }
                String codingwhodraVer1 = objects.getJSONObject(0).get("whodrug_ver").toString();
                String codingwhodraVer2 = objects.getJSONObject(1).get("whodrug_ver").toString();
                if (!StringUtils.isEmpty(codingwhodraVer1) && !StringUtils.isEmpty(codingwhodraVer2) && !codingwhodraVer1.equals(codingwhodraVer2) && codingPlanObjs.size() > 0) {
                    if (whoDraSubTableObjs.size() > 0) {
                        String ver = whoDraSubTableObjs.getJSONObject(0).get("ver").toString();
                        //   String change_date = whoDraSubTableObjs.getJSONObject(0).get("change_date").toString();
                        if (!codingwhodraVer1.equals(ver)) {
                            MedCodingPlanServiceImpl.log.info("codingwhodraVer1:" + codingwhodraVer1 + "codingwhodraVer2:" + codingwhodraVer2 + "codingPlanObjs.size():" + codingPlanObjs.size());
                            temp.put("study_id", studyInt);
                            temp.put("change_date", ver_date);
                            temp.put("study_coding_edition_id", codeDict);
                            temp.put("ver", codingwhodraVer1);
                            params.put("data", temp);
                            String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
                            CDTMSAPI.usersyndataSave(token, "study_coding_ver_update1", formId, "", "", temp.toString());
                        }
                    } else {
                        MedCodingPlanServiceImpl.log.info("codingwhodraVer1:" + codingwhodraVer1 + "codingwhodraVer2:" + codingwhodraVer2 + "codingPlanObjs.size():" + codingPlanObjs.size());
                        temp.put("study_id", studyInt);
                        temp.put("change_date", ver_date);
                        temp.put("ver", codingwhodraVer1);
                        temp.put("study_coding_edition_id", codeDict);
                        params.put("data", temp);
                        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
                        CDTMSAPI.usersyndataSave(token, "study_coding_ver_update1", formId, "", "", temp.toString());
                    }


                }
            }
            return "success";
        } else {
            return "cdtms中没有该项目";
        }

    }


    @Autowired
    MinioUtil minioUtil;
    @Override
    public String getMedCodingUTR(String taskId, String projectId) {
        //获取参数，调用sas程序
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId = "";
        String tableId = "";
        if (!ObjectUtils.isEmpty(formInfo.get("recordId"))) {
            recordId = formInfo.get("recordId");
            tableId = formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }

        String studyId = formInfo.get("studyId");
        String used_language = CDTMSAPI.getStudyLanguage(studyId);
        String data = formInfo.get("param").toString();
        List<String> medUTRNameStr=new ArrayList<>();
        List<String> whoUTRNameStr=new ArrayList<>();
        String dataId="";
        if (!data.isEmpty()) {
            cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(data);
            dataId = formInfoData.get("id").toString();
            cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();
            String medraCoding = formInfoData.get("code_doc").toString();
            String whodrugCoding = formInfoData.get("whodrugcoding").toString();
            if (medraCoding != null && !"".equals(medraCoding)) {
                //获取ufn
                String input = medraCoding;
                // 获取文件名和文件索引
                String[] fileEntries = input.split(",");
                for (String entry : fileEntries) {
                    if (entry.isEmpty())
                        continue;
                        String fileName = entry;
                        MedCodingPlanServiceImpl.log.info("File Name: " + fileName);
                        // Pattern to extract the Chinese text between underscores
                        String pattern = ".*-\\d{8}-\\d{4}_([^_]+)_\\d{8}_\\d{4}_.*";
                        Pattern regex = Pattern.compile(pattern);
                        Matcher matcher = regex.matcher(fileName);
                        String extractedText = "";
                        if (matcher.matches()) {
                            extractedText = matcher.group(1); // "不良事件"
                            medUTRNameStr.add(extractedText);
                        } else {
                            MedCodingPlanServiceImpl.log.info("Could not extract text from filename: " + fileName);
                        }

                }
            }

            if (whodrugCoding != null && !"".equals(whodrugCoding)) {
                //获取ufn
                String input = whodrugCoding;
                // 获取文件名和文件索引
                String[] fileEntries = input.split(",");
                for (String entry : fileEntries) {
                    if (entry.isEmpty())
                        continue;
                        String fileName =entry;
                        MedCodingPlanServiceImpl.log.info("the added File Name: " + fileName);
                        // Pattern to extract the Chinese text between underscores
                        String pattern = ".*-\\d{8}-\\d{4}_([^_]+)_\\d{8}_\\d{4}_.*";
                        Pattern regex = Pattern.compile(pattern);
                        Matcher matcher = regex.matcher(fileName);
                        String extractedText = "";
                        if (matcher.matches()) {
                            extractedText = matcher.group(1);
                            whoUTRNameStr.add(extractedText);
                        } else {
                            MedCodingPlanServiceImpl.log.info("Could not extract text from filename: " + fileName);
                        }

                }
            }

        }else{
            return "当前记录查询异常!";
        }

        String outputName="";
        List<Map<String, String>> sasOutputFilesInfo = new ArrayList<>();
        for(String utrName:medUTRNameStr){
            //7.upload the  sas output report to cdtms API
            Map<String, String> sasOutputFile = new HashMap<>();
            sasOutputFile.put("fid", "utrmedra");
            outputName=studyId+"_UTR("+utrName+").xlsx";
            sasOutputFile.put("outputName", outputName);
            MedCodingPlanServiceImpl.log.info("the added utrName:{}",outputName);
            sasOutputFilesInfo.add(sasOutputFile);
        }

        for(String utrName:whoUTRNameStr){
            //7.upload the  sas output report to cdtms API
            Map<String, String> sasOutputFile = new HashMap<>();
            sasOutputFile.put("fid", "utrwhodrug");
            outputName=studyId+"_UTR("+utrName+").xlsx";
            sasOutputFile.put("outputName", outputName);
            MedCodingPlanServiceImpl.log.info("the added utrName:{}",outputName);
            sasOutputFilesInfo.add(sasOutputFile);
        }

        String paramStr = data.toString();

        //8. call sas program
        String uuid = ULIDGenerator.generateULID();
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);

        formInfo.put("outputName", "");
        formInfo.put("bucket", "utr");
        formInfo.put("language", used_language);
        formInfo.put("uuid", uuid);
        String paramFileName = formInfo.get("studyId").toString() + "_medutr.json";
        //5.1 submit to sas via json params and define the json path on the minio storage platform
        formInfo.put("jsonMinioPath", SASOnlieConstant.PREFIX_PRO_MINIO+"/utr/json/" + paramFileName);
        formInfo.put("sasCodePath", SASOnlieConstant.RTSM_MED_UTR_PATH);
        formInfo.put("paramFileName", paramFileName);
        formInfo.put("param", paramStr);
        formInfo.put("taskId", taskId);
        formInfo.put("projectId", projectId);
        formInfo.put("dataId",dataId);

        Map<String,String> ENVInfo=new HashMap<>();
        ENVInfo.put("isLatest","");
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("ENV", "PRO");
        ENVInfo.put("data_format", "");
        ENVInfo.put("data_type", "");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", recordId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", tableId);
        ENVInfo.put("fileSuffix", "a".toString());
        //4.1.upload the latest report and second latest report to cdtms API
        //4.2 upload second latest report to cdtms
        ENVInfo.put("isLatest", "");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        Map<String, String> result = submitSAS.submitToSAS(ENVInfo, null, formInfo, sasOutputFilesInfo);
        for(Map<String, String> sasOutputFile: sasOutputFilesInfo){
            MedCodingPlanServiceImpl.log.info("---------------------------下载minio上的urt文件： " + sasOutputFile.get("outputName"));
            //从minio下载utrmedra和utrwhodrug
            minioUtil.downloadGetObject("utr", sasOutputFile.get("outputName"));
        }
        List<Map<String,String>> utrFiles=new ArrayList<>();
        for (Map<String, String> sasOutputFile : sasOutputFilesInfo) {
            Map<String,String> uploadFileInfo=new HashMap<>();
            MedCodingPlanServiceImpl.log.info("---------------------------上传minio上的urt文件： " + sasOutputFile.get("outputName"));
            String  ufn = CDTMSAPI.getUfnWithType(SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + sasOutputFile.get("outputName"), formInfo.get("taskId"), formInfo.get("projectId"),"zip");
            uploadFileInfo.put("ufn", ufn);
            uploadFileInfo.put("fid", sasOutputFile.get("fid"));
            uploadFileInfo.put("fileName",sasOutputFile.get("outputName"));
            utrFiles.add(uploadFileInfo);
        }


        cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();
        cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
        temp.put("id", formInfo.get("dataId").toString());
        for(Map<String, String> uploadFileInfo:utrFiles){
            temp.put("output", formInfo.get("studyId").toString() + "_List_of_Visits_Median_" + fileDate1 + ".xlsx" + "*" + ufn1 + "|" + formInfo.get("studyId").toString() + "_Prior_Radiotherapy_Median_" + fileDate2 + ".xlsx" + "*" + ufn2 + "|" + formInfo.get("studyId").toString() + "_Safety_Status_Report_Median_" + fileDate3 + ".xlsx" + "*" + ufn3 + "|");
        }
        params.put("data", temp);
        String newFormId = CDTMSAPI.getFormIdByTaskId(recordId, projectId);
        params.put("formId", newFormId);
        params.put("taskId", taskId);
        params.put("projectId", projectId);
        CDTMSAPI.dataSave(params);



        return "success";
    }


}
