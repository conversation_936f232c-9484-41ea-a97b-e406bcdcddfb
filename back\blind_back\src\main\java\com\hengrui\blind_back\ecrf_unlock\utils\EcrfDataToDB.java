package com.hengrui.blind_back.ecrf_unlock.utils;

import com.hengrui.blind_back.blind.entity.CSVMappingEntity;
import com.hengrui.blind_back.blind.entity.CSVMetaEntity;
import com.hengrui.blind_back.blind.mapper.BlindBackMapper;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class EcrfDataToDB {


    @Autowired
    BlindBackMapper blindBackMapper;
    public static final String UTF8_BOM = "\uFEFF";


    public String parseCSVToDB(String filePath) {
        String fileId = "";
        String line;
        String csvSplitBy = ",";
        List<CSVMetaEntity> csvMetaEntityList = new ArrayList<>();
        int lineCount = 0; // Counter variable
        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            //赋值fileId with one batch insert
            fileId = com.hengrui.blind_back.blind.utils.ULIDGenerator.generateULID();
            while ((line = br.readLine()) != null) {
                String lineData = line;
                // 处理除表头以外的行数据
                lineCount++;
                //表头数据
                String newHeadline = "";
                if (line.startsWith(UTF8_BOM)) {
                    newHeadline = line.substring(1);
                } else {
                    newHeadline = line.substring(0);
                }
                if (lineCount == 1) {
                    CSVMappingEntity csvMappingEntity = new CSVMappingEntity();
                    String[] titleSplit = newHeadline.split(",");
                    if (titleSplit.length > 1) {
                        for (int i = 0; i < titleSplit.length; i++) {
                            csvMappingEntity.setId(com.hengrui.blind_back.blind.utils.ULIDGenerator.generateULID());
                            csvMappingEntity.setFileId(fileId);
                            csvMappingEntity.setMapSequence("var" + (i + 1));
                            csvMappingEntity.setMapValue(titleSplit[i]);
                            csvMappingEntity.setSeq(i + 1);
                            blindBackMapper.insertColMap(csvMappingEntity);
                        }

                    }
                } else if (lineCount >= 2) {
                    //给CSVMetaEntity赋值,并加到csvMetaEntityList中
                    CSVMetaEntity csvMetaEntity = new CSVMetaEntity();
                    String[] split = lineData.split(",");
                    int length = split.length;
                    if (length > 1) {
                        //赋值
                        csvMetaEntity.setId(com.hengrui.blind_back.blind.utils.ULIDGenerator.generateULID());
                        csvMetaEntity.setFileId(fileId);
                        csvMetaEntity.setVar1(split[0]);
                        csvMetaEntity.setRowSeq(lineCount - 1);
                        csvMetaEntity.setCreateTime(String.valueOf(System.currentTimeMillis()));
                        for (int i = 1; i < length && i <= 50; i++) {
                            if (!split[i].isEmpty()) {
                                csvMetaEntity.getClass()
                                        .getMethod("setVar" + (i + 1), String.class)
                                        .invoke(csvMetaEntity, split[i]);
                            }

                        }
                    }
                    csvMetaEntityList.add(csvMetaEntity);

                } else {
                    //记录行为空
                    //给CSVMetaEntity赋值,并加到csvMetaEntityList中
                    CSVMetaEntity csvMetaEntity = new CSVMetaEntity();
                    String[] split = newHeadline.split(",");
                    int length = split.length;
                    if (length > 1) {
                        //赋值
                        csvMetaEntity.setId(ULIDGenerator.generateULID());
                        csvMetaEntity.setFileId(fileId);
                        csvMetaEntity.setVar1("");
                        csvMetaEntity.setRowSeq(lineCount - 1);
                        csvMetaEntity.setCreateTime(String.valueOf(System.currentTimeMillis()));
                        for (int i = 1; i < length && i <= 50; i++) {
                            if (!split[i].isEmpty()) {
                                csvMetaEntity.getClass()
                                        .getMethod("setVar" + (i + 1), String.class)
                                        .invoke(csvMetaEntity, "");
                            }

                        }
                    }
                    csvMetaEntityList.add(csvMetaEntity);
                }

            }
            if (csvMetaEntityList.size() > 1) {
                //存到表里
                blindBackMapper.insertBatch(csvMetaEntityList);
            }

        } catch (IOException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
        //返回生成的文件id
        return fileId;
    }
}
