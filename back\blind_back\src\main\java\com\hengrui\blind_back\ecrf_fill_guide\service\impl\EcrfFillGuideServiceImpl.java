package com.hengrui.blind_back.ecrf_fill_guide.service.impl;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import com.hengrui.blind_back.config_compare_report.service.impl.ConfigCompareServiceImpl;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.ecrf_fill_guide.service.EcrfFillGuideService;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.hengrui.blind_back.utils.CallPython;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName EcrfFillGuideServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/24 10:04
 * @Version 1.0
 **/
@Service
@Slf4j
public class EcrfFillGuideServiceImpl implements EcrfFillGuideService {

    @Autowired
    CallPython callPython;
    @Override
    public Map<String, String> getEcrfFillGuide(String taskId, String projectId) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId="";
        String tableId="";
        if(!ObjectUtils.isEmpty(formInfo.get("recordId"))){
            recordId= formInfo.get("recordId");
            tableId=formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id",recordId);
            object.put("formData",formData);
            projectId=object.toJSONString();
        }else{
            tableId=projectId;
            recordId=taskId;
        }
        String data = formInfo.get("param");
        String bbh = "";
        if (!data.isEmpty()) {
            JSONObject formInfoData = new JSONObject(data);
            bbh = formInfoData.get("dqbb").toString();
        }
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        EcrfFillGuideServiceImpl.log.info("------------获取到的formId是：" + formId + "------------");
        EcrfFillGuideServiceImpl.log.info("------------获取到的studyId是：" + studyId + "------------");
        Map<String, String> result = new HashMap<>();
        List<Map<String, String>> filesFromEDC = new ArrayList<>();
        Map<String, String> fileObject = new HashMap<>();
        fileObject.put("fid", "ecrf_v_doc");
        fileObject.put("fileType", ".docx");
        String fileName=studyId+"_CCG_"+bbh+".docx";
        fileObject.put("name", fileName);
        filesFromEDC.add(fileObject);



        String uuid = ULIDGenerator.generateULID();
        Map<String, String> ENVInfo = new HashMap<>();
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("ENV", "UAT");
        ENVInfo.put("data_format", "docx");
        ENVInfo.put("data_type", "eCRF_fill_guide");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", recordId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", tableId);
        ENVInfo.put("fileSuffix", "a".toString());
        ENVInfo.put("isLatest", "");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        Map<String,String> pyResult=callPython.downloadEDCServerFile(ENVInfo, filesFromEDC);
        String dataIsTwoDays= pyResult.get("dataIsTwoDays");
        result.put("dataIsTwoDays","数据是否在两天之内："+dataIsTwoDays);
        String results = pyResult.get("original_name");
        result.put("result", results);
        //update status for workflow
        CDTMSAPI.updateWorkFlowStatus(taskId, projectId, "crf_zt","01");
        return result;
    }
}
