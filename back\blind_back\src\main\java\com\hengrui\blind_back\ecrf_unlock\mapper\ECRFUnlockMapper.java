package com.hengrui.blind_back.ecrf_unlock.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

@Component
@Mapper
@Repository
@DS("slave_3")
public interface ECRFUnlockMapper {
    int insertUnlockRecords(String id, String param, String userName, String fileName);

    void updateUnlockFailRecords(String id,String failReason,String SASLogPath);

    void updateUnlockSuccessRecords(String id, String minioPath, String localPath, String SASlogPath);
}
