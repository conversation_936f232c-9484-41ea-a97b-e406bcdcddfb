<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengrui.blind_back.parse_excel_toDB.mapper.ParseExcelToDBMapper">
    <!-- 批量插入数据 -->

    <insert id="insertDatatoECRFDB" parameterType="java.util.List">
        INSERT INTO tbl_ecrf (id, study_id, table_name_ch,table_name,standard_table_name,reference_table_name,variable_name,variable,variable_type,create_time)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.uuId}, #{item.studyId}, #{item.tableNameCH},#{item.tableNameEN},#{item.standardTableName},#{item.referenceTableName},#{item.variableName},
            #{item.variable},#{item.variableType},current_timestamp)
        </foreach>

    </insert>
</mapper>