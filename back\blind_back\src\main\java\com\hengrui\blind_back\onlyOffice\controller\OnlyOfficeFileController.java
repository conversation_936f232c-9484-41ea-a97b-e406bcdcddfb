package com.hengrui.blind_back.onlyOffice.controller;

import com.hengrui.blind_back.onlyOffice.service.OnlyOfficeFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @ClassName OnlyOfficeFileController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/8/27 13:58
 * @Version 1.0
 **/
@RestController
@Slf4j
public class OnlyOfficeFileController {


    @Autowired
    OnlyOfficeFileService onlyOfficeFileService;
    //1.文件保存接口
    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/saveOnlyOfficeFile")
    public void saveOnlyOfficeFile(HttpServletRequest request, HttpServletResponse response){
        try {
            onlyOfficeFileService.saveOnlyOfficeFile(request,response);
        } catch (ServletException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }





    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/saveAuditApprovalFile")
    public void saveAuditApprovalFile(HttpServletRequest request, HttpServletResponse response){
        try {
            onlyOfficeFileService.saveAuditApprovalFile(request,response);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
