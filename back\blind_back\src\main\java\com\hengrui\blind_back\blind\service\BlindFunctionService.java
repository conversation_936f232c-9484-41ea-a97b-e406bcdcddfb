package com.hengrui.blind_back.blind.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hengrui.blind_back.blind.entity.CSVTableDataEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface BlindFunctionService {

    CSVTableDataEntity getCSVTable(String fileId);

    String submitJsonParams(String jsonData, String blindDemand, String blindMember, String fileName, String projectName, String userName, String role, String originalFileId, String exDataId, String token, String dataType, String blindOperateName,
                            String blindMemberWithMail);

    String getCSVFile(String projectName, String fileName, String token);

    List<Map<String, String>> getBlindHistory(String projectName, String fileName);

    JSON getBlindOperate(String historyId);


    List<Map<String, Object>> fileUpload(HttpServletRequest req, String fileName, String projectName, String userName, String isApprove, String fileId, String url, String exDataId, String comments);

    String getUnBlindOriginFileId(String fileName, String projectName);

    List<Map<String, Object>> getProjectMembersInfo(String projectName);

    List<Map<String, Object>> sendFinalData(List<String> emails, String fileName, String projectName, String userName, String exDataId);

    boolean getFileIsSendCount(String fileName, String projectName);

    boolean judgeIsBlinded(String fileId);

    JSONObject checkLogin(String token, String isLogin);

    //获取操作记录和邮件记录
    List<Map<String, Object>> OperateAndEmailRecords(String id);

    String getCompareFileUrl(String fileId);

    Map<String, String> getQCFileInfo(String fileName, String projectName);


    Map<String, String> getDemandAndName(String historyId);

    String getBlindedFile(String fileId, HttpServletResponse response);

    String getMemberInfoByFileId(String fileId);

    String getMemberWithMailByFileId(String fileId);

    String setDataSetPass(String password, String userName,String studyId,String taskId);

    JSONObject getLoginInfo(String taskId, String projectId);

    String getDataSetPass(String studyId);


    String processUploadedFile(MultipartFile file, String userName, String studyId,String taskId,String server);
}
