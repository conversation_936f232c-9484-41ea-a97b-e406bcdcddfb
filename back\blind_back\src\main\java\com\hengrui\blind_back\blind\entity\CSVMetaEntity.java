package com.hengrui.blind_back.blind.entity;

public class CSVMetaEntity {

    //记录id
    private String id;
    //文件id
    private String fileId;

    private int rowSeq;

    private String var1;

    private String var2;

    private String var3;

    private String var4;

    private String var5;

    private String var6;

    private String var7;

    private String var8;

    private String var9;

    private String var10;

    private String var11;

    private String var12;

    private String var13;

    private String var14;

    private String var15;

    private String var16;

    private String var17;

    private String var18;

    private String var19;

    private String var20;

    private String var21;

    private String var22;

    private String var23;

    private String var24;

    private String var25;

    private String var26;

    private String var27;

    private String var28;

    private String var29;

    private String var30;

    private String var31;

    private String var32;

    private String var33;

    private String var34;

    private String var35;

    private String var36;
    private String var37;
    private String var38;
    private String var39;
    private String var40;
    private String var41;
    private String var42;
    private String var43;
    private String var44;
    private String var45;
    private String var46;

    private String var47;

    private String var48;

    private String var49;

    private String var50;

    private String createTime;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public int getRowSeq() {
        return rowSeq;
    }

    public void setRowSeq(int rowSeq) {
        this.rowSeq = rowSeq;
    }

    public String getVar1() {
        return var1;
    }

    public void setVar1(String var1) {
        this.var1 = var1;
    }

    public String getVar2() {
        return var2;
    }

    public void setVar2(String var2) {
        this.var2 = var2;
    }

    public String getVar3() {
        return var3;
    }

    public void setVar3(String var3) {
        this.var3 = var3;
    }

    public String getVar4() {
        return var4;
    }

    public void setVar4(String var4) {
        this.var4 = var4;
    }

    public String getVar5() {
        return var5;
    }

    public void setVar5(String var5) {
        this.var5 = var5;
    }

    public String getVar6() {
        return var6;
    }

    public void setVar6(String var6) {
        this.var6 = var6;
    }

    public String getVar7() {
        return var7;
    }

    public void setVar7(String var7) {
        this.var7 = var7;
    }

    public String getVar8() {
        return var8;
    }

    public void setVar8(String var8) {
        this.var8 = var8;
    }

    public String getVar9() {
        return var9;
    }

    public void setVar9(String var9) {
        this.var9 = var9;
    }

    public String getVar10() {
        return var10;
    }

    public void setVar10(String var10) {
        this.var10 = var10;
    }

    public String getVar11() {
        return var11;
    }

    public void setVar11(String var11) {
        this.var11 = var11;
    }

    public String getVar12() {
        return var12;
    }

    public void setVar12(String var12) {
        this.var12 = var12;
    }

    public String getVar13() {
        return var13;
    }

    public void setVar13(String var13) {
        this.var13 = var13;
    }

    public String getVar14() {
        return var14;
    }

    public void setVar14(String var14) {
        this.var14 = var14;
    }

    public String getVar15() {
        return var15;
    }

    public void setVar15(String var15) {
        this.var15 = var15;
    }

    public String getVar16() {
        return var16;
    }

    public void setVar16(String var16) {
        this.var16 = var16;
    }

    public String getVar17() {
        return var17;
    }

    public void setVar17(String var17) {
        this.var17 = var17;
    }

    public String getVar18() {
        return var18;
    }

    public void setVar18(String var18) {
        this.var18 = var18;
    }

    public String getVar19() {
        return var19;
    }

    public void setVar19(String var19) {
        this.var19 = var19;
    }

    public String getVar20() {
        return var20;
    }

    public void setVar20(String var20) {
        this.var20 = var20;
    }

    public String getVar21() {
        return var21;
    }

    public void setVar21(String var21) {
        this.var21 = var21;
    }

    public String getVar22() {
        return var22;
    }

    public void setVar22(String var22) {
        this.var22 = var22;
    }

    public String getVar23() {
        return var23;
    }

    public void setVar23(String var23) {
        this.var23 = var23;
    }

    public String getVar24() {
        return var24;
    }

    public void setVar24(String var24) {
        this.var24 = var24;
    }

    public String getVar25() {
        return var25;
    }

    public void setVar25(String var25) {
        this.var25 = var25;
    }

    public String getVar26() {
        return var26;
    }

    public void setVar26(String var26) {
        this.var26 = var26;
    }

    public String getVar27() {
        return var27;
    }

    public void setVar27(String var27) {
        this.var27 = var27;
    }

    public String getVar28() {
        return var28;
    }

    public void setVar28(String var28) {
        this.var28 = var28;
    }

    public String getVar29() {
        return var29;
    }

    public void setVar29(String var29) {
        this.var29 = var29;
    }

    public String getVar30() {
        return var30;
    }

    public void setVar30(String var30) {
        this.var30 = var30;
    }

    public String getVar31() {
        return var31;
    }

    public void setVar31(String var31) {
        this.var31 = var31;
    }

    public String getVar32() {
        return var32;
    }

    public void setVar32(String var32) {
        this.var32 = var32;
    }

    public String getVar33() {
        return var33;
    }

    public void setVar33(String var33) {
        this.var33 = var33;
    }

    public String getVar34() {
        return var34;
    }

    public void setVar34(String var34) {
        this.var34 = var34;
    }

    public String getVar35() {
        return var35;
    }

    public void setVar35(String var35) {
        this.var35 = var35;
    }

    public String getVar36() {
        return var36;
    }

    public void setVar36(String var36) {
        this.var36 = var36;
    }

    public String getVar37() {
        return var37;
    }

    public void setVar37(String var37) {
        this.var37 = var37;
    }

    public String getVar38() {
        return var38;
    }

    public void setVar38(String var38) {
        this.var38 = var38;
    }

    public String getVar39() {
        return var39;
    }

    public void setVar39(String var39) {
        this.var39 = var39;
    }

    public String getVar40() {
        return var40;
    }

    public void setVar40(String var40) {
        this.var40 = var40;
    }

    public String getVar41() {
        return var41;
    }

    public void setVar41(String var41) {
        this.var41 = var41;
    }

    public String getVar42() {
        return var42;
    }

    public void setVar42(String var42) {
        this.var42 = var42;
    }

    public String getVar43() {
        return var43;
    }

    public void setVar43(String var43) {
        this.var43 = var43;
    }

    public String getVar44() {
        return var44;
    }

    public void setVar44(String var44) {
        this.var44 = var44;
    }

    public String getVar45() {
        return var45;
    }

    public void setVar45(String var45) {
        this.var45 = var45;
    }

    public String getVar46() {
        return var46;
    }

    public void setVar46(String var46) {
        this.var46 = var46;
    }

    public String getVar47() {
        return var47;
    }

    public void setVar47(String var47) {
        this.var47 = var47;
    }

    public String getVar48() {
        return var48;
    }

    public void setVar48(String var48) {
        this.var48 = var48;
    }

    public String getVar49() {
        return var49;
    }

    public void setVar49(String var49) {
        this.var49 = var49;
    }

    public String getVar50() {
        return var50;
    }

    public void setVar50(String var50) {
        this.var50 = var50;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
}
