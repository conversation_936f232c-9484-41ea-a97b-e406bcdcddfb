package com.hengrui.blind_back.database_change_report.controller;

import com.hengrui.blind_back.database_change_report.service.DBChangeReportService;
import com.hengrui.blind_back.uat.controller.UatController;
import com.hengrui.blind_back.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName DBChangeReportController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/27 17:18
 * @Version 1.0
 **/
@RestController
@Slf4j
public class DBChangeReportController {
    @Autowired
    DBChangeReportService dbChangeReportService;

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/submitToDBChangeReportSAS")

    public Map<String, Object> submitToDBChangeReportSAS(String taskId,
                                              String server,
                                              String projectId) {
        DBChangeReportController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> results = dbChangeReportService.submitToDBChangeReportSAS(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", results);
        return result;
    }

}
