package com.hengrui.blind_back.edc_history.controller;

import com.hengrui.blind_back.edc_history.service.EDCHisService;
import com.hengrui.blind_back.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName EDCHisController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/3 14:00
 * @Version 1.0
 **/
@RestController
@Slf4j
public class EDCHisController {


    @Autowired
    EDCHisService edcHisService;

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getUserHistoryFile")

    public Map<String, Object> getUserHistoryFile(String taskId,
                                                  String server,
                                                  String projectId) {
        EDCHisController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> resultsA = edcHisService.getUserHistoryFile(taskId,projectId,"permission_his","a".toString());
        Map<String, String> resultsB = edcHisService.getUserHistoryFile(taskId,projectId,"edc_userinfo","a".toString());
        EDCHisController.log.info("获取到的文件名的类型为：" + "a");
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", resultsA);
        return result;
    }
}
