package com.hengrui.blind_back.utils;

import com.hengrui.blind_back.blind.constant.BlindConstant;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.ecrf_unlock.utils.FileUtil;
import com.hengrui.blind_back.entity.CallPythonEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName CallPython
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/5 16:40
 * @Version 1.0
 **/
@Component
@Slf4j

public class CallPython {

    @Autowired
    EDCServerFile edcServerFile;

    public Map<String,String> downloadEDCServerFile(Map<String, String> ENVInfo, List<Map<String, String>> uploadFilesFromEDC) {
        Map<String,String> result=new HashMap<>();
        String fileName = "";
        //1.调用python程序, set call  python param entity
        CallPythonEntity entity = new CallPythonEntity();
        entity.setStudyId(ENVInfo.get("studyId"));
        entity.setDataType(ENVInfo.get("data_type"));
        entity.setDataFormat(ENVInfo.get("data_format"));
        entity.setPyPath("/home/<USER>/edc_to_minio/edc_to_minio.py");
        entity.setEnv(ENVInfo.get("ENV"));
        entity.setUuid(ENVInfo.get("uuid"));
        if(!ObjectUtils.isEmpty(ENVInfo.get("fileNameSuffix"))){
           entity.setFileNameSuffix(ENVInfo.get("fileNameSuffix"));
        }
        if(!"".equals(ENVInfo.get("isLatest").toString())){
            entity.setIsLatest(ENVInfo.get("isLatest"));
        }else{
            entity.setIsLatest("default");
        }
        //download edcServer file via python program and provide some indispensable param only one file
        Map<String,String> pyResult= new HashMap<>();
        pyResult= edcServerFile.getEDCServerFileByPy(entity, "/home/<USER>/8087/python_call_log");
        String original_name =pyResult.get("original_name");
        String dataIsTwoDays=pyResult.get("dataIsTwoDays");

        //wtf this is a bug
        for (Map<String, String> fileObject : uploadFilesFromEDC) {
            log.info("-------上传文件的类型是："+fileObject.get("fileType"));
            fileName = SASOnlieConstant.EDC_DATA_LOCAL_FOLDER + BlindConstant.FILE_SEPARATOR + ENVInfo.get("studyId") + "_" + ENVInfo.get("ENV") + "_" + ENVInfo.get("uuid") + "_" + ENVInfo.get("fileSuffix") + fileObject.get("fileType");
            File file=new File(fileName);
            if(!"".equals(original_name)){
                if(!ENVInfo.get("data_type").equals("eCRF_fill_guide")&&!ENVInfo.get("data_type").equals("Logic_Check_Setting")){
                    FileUtil.uploadSASOutputFile(ENVInfo.get("taskId"), ENVInfo.get("formId"), fileObject.get("fid"), fileName, ENVInfo.get("projectId"), ENVInfo.get("requestPrefix"),original_name,ENVInfo.get("data_type"));
                }else if(ENVInfo.get("data_type").equals("Logic_Check_Setting")){
                    String Date=FileUtil.transferEDCFIleName(original_name);
                    FileUtil.uploadSASOutputFile(ENVInfo.get("taskId"), ENVInfo.get("formId"), fileObject.get("fid"), fileName, ENVInfo.get("projectId"), ENVInfo.get("requestPrefix"),fileObject.get("name")+Date+".xlsx",ENVInfo.get("data_type"));
                }else{
                    //1.执行额外的python脚本]
                    String name=ENVInfo.get("studyId") + "_" + ENVInfo.get("ENV") + "_" + ENVInfo.get("uuid") + "_" + ENVInfo.get("fileSuffix") + fileObject.get("fileType");

                    String command="python3.9 /home/<USER>/ecrf_guide_fill/ecrf_guide_fill.py  --studyid='" + ENVInfo.get("studyId")+"'"
                            +" --filename='" + name +  "'";
                    executeLatestFill(command);
                    fileName=SASOnlieConstant.EDC_DATA_LOCAL_FOLDER + BlindConstant.FILE_SEPARATOR + ENVInfo.get("studyId") + "_" + ENVInfo.get("ENV") + "_" + ENVInfo.get("uuid") + "_" + "a" + fileObject.get("fileType");
                    if(file.exists()){
                        FileUtil.uploadSASOutputFile(ENVInfo.get("taskId"), ENVInfo.get("formId"), fileObject.get("fid"), fileName, ENVInfo.get("projectId"), ENVInfo.get("requestPrefix"),fileObject.get("name"),ENVInfo.get("data_type"));
                    }

                }


                if(ENVInfo.get("data_type").equals("Subject_CRF")&&file.exists()&&!ObjectUtils.isEmpty(ENVInfo.get("ftp"))){
                    SFTPFile.uploadFileToSFTP(fileName,
                            ENVInfo.get("ftp"),
                            "clinical-ftp.hengrui.com",
                            22357, SASOnlieConstant.RTSM_API_USER, SASOnlieConstant.RTSM_API_PASS,original_name);
                }else if(ENVInfo.get("data_type").equals("Subject_CRF")&&file.exists()&&ObjectUtils.isEmpty(ENVInfo.get("ftp"))){
                    SFTPFile.uploadFileToSFTP(fileName,
                            "/Projects/CDTMS手册优化测试项目/0/",
                            "clinical-ftp.hengrui.com",
                            22357, SASOnlieConstant.RTSM_API_USER, SASOnlieConstant.RTSM_API_PASS,original_name);
                }
                fileName=original_name;
            }
        }

        result.put("original_name",original_name);
        result.put("dataIsTwoDays",dataIsTwoDays);

        return result;
    }

    public static void executeFIleTransfer(String studyCode){
        //1.make a format system command
        String command ="python3.9 /home/<USER>/edc_to_minio/get_old_new.py --file_list= '" + studyCode + "'";
        //2.use this command to execute python program
        String[] cmd={"/bin/sh","-c",command+" ./do*"};
        CallPython.log.info("执行的python转移比对文件的命令为:" + command);
        try {
            Process process = Runtime.getRuntime().exec(cmd);
            // Read the output from the command
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            //make the log to be a log file
            while ((line = reader.readLine()) != null) {
                CallPython.log.info(line);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }



    public  void executeLatestFIleTransfer(String studyCode){
        //1.make a format system command
        String command ="mc cp "+SASOnlieConstant.PREFIX_PRO_MINIO+"/raw/"+studyCode+"_sas.zip "+SASOnlieConstant.PREFIX_PRO_MINIO+"/sascomp/data/newdata/" + studyCode +  "_sas.zip";
        //2.use this command to execute python program
        String[] cmd={"/bin/sh","-c",command+" ./do*"};
        CallPython.log.info("执行的python转移比对文件的命令为:" + command);
        try {
            Process process = Runtime.getRuntime().exec(cmd);
            // Read the output from the command
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            //make the log to be a log file
            while ((line = reader.readLine()) != null) {
                CallPython.log.info(line);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static String executeLatestFill(String command){
        StringBuilder result = new StringBuilder();
        //1.make a format system command
        //2.use this command to execute python program
        String[] cmd={"/bin/sh","-c",command+" ./do*"};
        CallPython.log.info("执行的CRF填写指南填充python的命令为:" + command);
        try {
            Process process = Runtime.getRuntime().exec(cmd);
            // Read the output from the command
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            //make the log to be a log file
            while ((line = reader.readLine()) != null) {
                result.append(line);
                CallPython.log.info(line);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        CallPython.log.info("执行Python脚本的结果是:"+result.toString());
        return result.toString();
    }


    public static void runPy( String pythonCommand, String scriptPath,String sourceFile,String outputFile,String sample_file,String lang) {
        // Define the command and arguments

        // Create the ProcessBuilder
        ProcessBuilder processBuilder = new ProcessBuilder(pythonCommand, scriptPath,
                "--source_file=" + sourceFile,
                "--sample_file=" + sample_file,
                "--output_file=" + outputFile,
                "--lang=" + lang);

        // Redirect error stream to the standard output stream
        processBuilder.redirectErrorStream(true);

        try {
            // Start the process
            Process process = processBuilder.start();

            // Read the output from the command
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.info(line); // Print the output from the Python script
                }
            }

            // Wait for the process to complete and get the exit code
            int exitCode = process.waitFor();
            log.info("Process exited with code: " + exitCode);
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
    }



    public static void runPyBak( String pythonCommand, String scriptPath,String sourceFile,String outputFile,String shetName) {
        // Define the command and arguments

        // Create the ProcessBuilder
        ProcessBuilder processBuilder = new ProcessBuilder(pythonCommand, scriptPath,
                "--source_file=" + sourceFile,
                "--target_file=" + outputFile,
                "--sheet_name=" + shetName);

        // Redirect error stream to the standard output stream
        processBuilder.redirectErrorStream(true);

        try {
            // Start the process
            Process process = processBuilder.start();

            // Read the output from the command
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.info(line); // Print the output from the Python script
                }
            }

            // Wait for the process to complete and get the exit code
            int exitCode = process.waitFor();
            log.info("Process exited with code: " + exitCode);
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
    }

}
