package com.hengrui.blind_back.sas_check_content.controller;

import cn.hutool.json.JSONObject;
import com.hengrui.blind_back.sas_check_content.service.SASCheckContentService;
import com.hengrui.blind_back.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName SASCheckContentController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/9/23 15:30
 * @Version 1.0
 **/
@RestController
@Slf4j
public class SASCheckContentController {
    //sas核查内容，测试版，根据核查内容测试节点程序是否正常

    @Autowired
    SASCheckContentService sasCheckContentService;


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/testSASCheckContent")
    @ResponseBody
    public Map<String, Object> configSchedule(String taskId,
                                              String server,
                                              String projectId) {
        String results = sasCheckContentService.testSASCheckContent(taskId, projectId);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        return result;
    }



    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getIRCData")
    @ResponseBody
    public Map<String, Object> getIRCData(String taskId,
                                              String server,
                                              String projectId) {
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        String results = sasCheckContentService.getIRCData(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", results);
        return result;
    }

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getIRCDataBack")
    @ResponseBody
    public Map<String, Object> getIRCDataBack(String taskId,
                                          String server,
                                          String projectId) {
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        String results = sasCheckContentService.getIRCDataBack(taskId, projectId);
        if(results.equals("success")){
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("code", 200);
            result.put("msg", "success");
            return result;
        }else{
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("code", 404);
            result.put("msg", results);
            return result;
        }

    }

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getLAB-AE")
    @ResponseBody
    public Map<String, Object> getLABAE(String taskId,
                                              String server,
                                              String projectId) {
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> results = sasCheckContentService.getLABAE(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", results.get("dataIsTwoDays"));
        return result;
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getPro-LAB-AE")
    @ResponseBody
    public Map<String, Object> getRealLAB(String taskId,
                                        String server,
                                        String projectId) {
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> results = sasCheckContentService.getRealLABAE(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", results.get("dataIsTwoDays"));
        return result;
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getRecist1.1")
    @ResponseBody
    public Map<String, Object> getRecist(String taskId,
                                        String server,
                                        String projectId) {
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> results = sasCheckContentService.getRecist(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", results.get("dataIsTwoDays"));
        return result;
    }

}
