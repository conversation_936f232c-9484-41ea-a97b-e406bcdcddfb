package com.hengrui.blind_back.medcoding_plan.controller;

import com.hengrui.blind_back.medcoding_plan.service.MedCodingPlanService;
import com.hengrui.blind_back.utils.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName MedCodingPlanController
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/2/20 15:53
 * @Version 1.0
 **/

@RestController
public class MedCodingPlanController {

    @Autowired
    MedCodingPlanService medCodingPlanService;

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/updateCodingHis")
    @ResponseBody
    public Map<String, Object> updateCodingHis(String taskId,
                                              String server,
                                              String projectId) {
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        String results = medCodingPlanService.updateCodingHis(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", results);
        return result;
    }




    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getMedCodingUTR")
    @ResponseBody
    public Map<String, Object> getMedCodingUTR(String taskId,
                                               String server,
                                               String projectId) {
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        String results = medCodingPlanService.getMedCodingUTR(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", results);
        return result;
    }




}
