package com.hengrui.blind_back.parse_excel_toDB.entity;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName FillData
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/19 16:41
 * @Version 1.0
 **/
@Getter
@Setter
@EqualsAndHashCode
public class FillData {
    private String caseNum;

    private String title;

    private String caseVersionNum;

    private String caseVersionDate;

    private String applier;

    private String applyTableVersion;

    private String applyTableDate;

    private String applyTableType;

    private String accountName;

    private String accountManager;
}
