<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengrui.blind_back.blind.mapper.BlindBackMapper">
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO ex_csv_store_data (
        id,
        file_id,
        var1,
        var2,
        var3,
        var4,
        var5,
        var6,
        var7,
        var8,
        var9,
        var10,
        var11,
        var12,
        var13,
        var14,
        var15,
        var16,
        var17,
        var18,
        var19,
        var20,
        var21,
        var22,
        var23,
        var24,
        var25,
        var26,
        var27,
        var28,
        var29,
        var30,
        var31,
        var32,
        var33,
        var34,
        var35,
        var36,
        var37,
        var38,
        var39,
        var40,
        var41,
        var42,
        var43,
        var44,
        var45,
        var46,
        var47,
        var48,
        var49,
        var50,
        create_time,
        row_seq
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.fileId}, #{item.var1}, #{item.var2}, #{item.var3}, #{item.var4}, #{item.var5}, #{item.var6}, #{item.var7}, #{item.var8}, #{item.var9}, #{item.var10}, #{item.var11}, #{item.var12}, #{item.var13}, #{item.var14}, #{item.var15}, #{item.var16}, #{item.var17}, #{item.var18}, #{item.var19}, #{item.var20}, #{item.var21}, #{item.var22}, #{item.var23}, #{item.var24}, #{item.var25}, #{item.var26}, #{item.var27}, #{item.var28}, #{item.var29}, #{item.var30}, #{item.var31}, #{item.var32}, #{item.var33}, #{item.var34}, #{item.var35}, #{item.var36}, #{item.var37}, #{item.var38}, #{item.var39}, #{item.var40}, #{item.var41}, #{item.var42}, #{item.var43}, #{item.var44}, #{item.var45}, #{item.var46}, #{item.var47}, #{item.var48}, #{item.var49}, #{item.var50},current_timestamp,#{item.rowSeq})
        </foreach>
    </insert>
    <insert id="insertColMap" parameterType="com.hengrui.blind_back.blind.entity.CSVMappingEntity">
        INSERT INTO ex_csv_col
        (id, file_id, map_seq, map_val,seq, create_time)
        VALUES
        (#{id},#{fileId}, #{mapSequence}, #{mapValue},#{seq}, current_timestamp)
    </insert>

    <select id="getCSVCol" parameterType="string" resultType="com.hengrui.blind_back.blind.entity.CSVMappingEntity">
        SELECT
            id,
            file_id,
            map_seq as mapSequence,
            map_val as mapValue
        FROM
            ex_csv_col
        WHERE
            file_id = #{fileId}
        order by seq ASC
    </select>
    <select id="getCSVMeta" parameterType="string" resultType="com.hengrui.blind_back.blind.entity.CSVMetaEntity">
        SELECT
            *
        FROM
            ex_csv_store_data
        WHERE
            file_id =  #{fileId}
        order by row_seq ASC
    </select>
    <insert id="insertBlindRecords" parameterType="string">
        INSERT INTO ex_blind_operation_record
        (id,file_id, blind_records, user_id,project_name,file_name, create_time,role,ex_data_id,data_type,operate_name,demand,project_member,blind_member_with_mail)
        values
        (#{id},'',#{jsonData},#{userName},#{projectName},#{fileName},current_timestamp,#{role},#{exDataId},#{dataType},#{blindOperateName},#{blindDemand},#{blindMember},#{blindMemberWithMail})
    </insert>

    <select id="getBlindRecords" parameterType="string" resultType="string">
        SELECT file_id FROM ex_blind_operation_record WHERE project_name=#{projectName} and file_name=#{fileName}
        ORDER BY create_time desc limit 1
    </select>
    <select id="getBlindIsApprove" parameterType="string" resultType="string">
        SELECT is_approve FROM ex_blind_operation_record WHERE file_id=#{fileId} order by create_time desc limit 1
    </select>

    <select id="getQCStatus" parameterType="string" resultType="string">
        SELECT type FROM ex_blind_email_record  WHERE file_id=#{fileId} order by create_time desc limit 1
    </select>
    <select id="getBlindHistory" parameterType="string" resultType="map">
        SELECT
            any_value ( id ) AS value,
	        any_value ( operate_name ) AS label
        FROM
            ex_blind_operation_record
        WHERE
            project_name = #{projectName}  and operate_name is not null and data_type=#{fileName} and is_approve='1' and  blind_status='Y' GROUP BY
            blind_records
        ORDER BY
            label DESC
    </select>

    <select id="getBlindOperate" parameterType="string" resultType="string">
        SELECT  blind_records FROM ex_blind_operation_record WHERE file_id=#{historyId}
    </select>
    
    <select id="getDemandAndName" parameterType="string" resultType="map">
        SELECT demand,project_member as member,operate_name as name FROM ex_blind_operation_record WHERE id =#{historyId}
    </select>

    <select id="getJsonFileName" parameterType="string" resultType="string">
        SELECT
            DATE_FORMAT(create_time, '%Y%m%d%H%i%s') AS formatted_date
        FROM
            ex_blind_operation_record
        WHERE project_name=#{projectName} and file_name=#{fileName}
        ORDER BY
            create_time
                DESC
            LIMIT 1
    </select>
    <update id="updateFileID" parameterType="string">
        UPDATE ex_blind_operation_record SET file_id=#{fileID},original_file_id=#{originalFileId},file_path=#{localPath},sas_log_path=#{sasLogPath},update_time=current_timestamp,blind_status=#{isSuccess},compare_file_path=#{compareFilePath} WHERE id=#{id}
    </update>
    <update id="updateBlindStatus" parameterType="string">
        UPDATE ex_blind_operation_record SET is_approve=#{isApprove},qc_user_id=#{userName},update_time=current_timestamp WHERE file_id=#{fileId}
    </update>
    <insert id="insertOriginalFileRecord" parameterType="string">
        INSERT INTO ex_blind_original_file_record
        (id, project_name,file_name, file_path,create_time,update_time)
        values
        (#{fileId},#{projectName},#{fileName},#{filePath},current_timestamp,current_timestamp)
    </insert>


    <insert id="insertDataSetPassword" parameterType="string">
        INSERT INTO data_set_pass
        (id,token,user_name,role,project_id,password,remark)
        values
        (#{id},#{token},#{userName},#{role},#{projectName},#{password},'Rave')
    </insert>


    <insert id="insertRaveDataUploadRecord" parameterType="string">
        INSERT INTO rave_data_set_upload_record
            (id,file_name,user_name,role,project_id,file_path,minio_path)
        values
            (#{id},#{originalFilename},#{userName},#{userName},#{studyId},#{fileLocalPath},#{fileMinioPath})
    </insert>



    <select id="unBlindFileId" parameterType="string" resultType="string">
        SELECT id FROM ex_blind_original_file_record WHERE file_name=#{fileName} and project_name=#{projectName} and is_approve='0'
        order by create_time desc limit 1
    </select>
    <select id="getOriginalFileId" parameterType="string" resultType="string">
        SELECT original_file_id FROM ex_blind_operation_record WHERE file_id=#{fileId}
    </select>

    <update id="updateOriginalFileStatus" parameterType="string">
        UPDATE ex_blind_original_file_record SET is_approve=#{isApprove},update_time=current_timestamp where id=#{originalFileId}
    </update>
    <select id="getBlindFilePath" parameterType="string" resultType="string">
        SELECT
            file_path
        FROM
            ex_blind_operation_record  WHERE file_id=#{fileId}
    </select>
    <insert id="insertEmailRecords" parameterType="string">
        INSERT INTO ex_blind_email_record(id,file_id,original_file_id,user_name,project_name,file_name,receive_emails,create_time,type,ex_data_id,qc_comments)
        values
        (#{id},#{fileId},#{originalFileId},#{userName},#{projectName},#{fileName},#{edmMail},current_timestamp,#{type},#{exDataId},#{comments})
    </insert>

    <select id="getApprovedFileId" parameterType="string" resultType="string">
        SELECT
            file_id
        FROM
            ex_blind_operation_record
        WHERE
            file_name = #{fileName}
          AND project_name = #{projectName}
          AND is_approve = '1'
          AND blind_status='Y'
        ORDER BY
            update_time DESC
            LIMIT 1
    </select>

    <select id="getMailIsSend" parameterType="string" resultType="int">
        SELECT
            count(1) as result
        FROM
            ex_blind_email_record
        WHERE
            file_id = #{fileId} and type='2'
    </select>
    <select id="getApprovedFilePath" parameterType="string" resultType="string">
        SELECT
            file_path
        FROM
            ex_blind_operation_record
        WHERE
            file_id = #{fileId}
          AND is_approve = '1' AND blind_status='Y'
    </select>
    <select id="getFileIsSendCount" parameterType="string" resultType="int">
        SELECT
            count(1) as result
        FROM
            ex_blind_original_file_record
                LEFT JOIN ex_blind_email_record ON ex_blind_original_file_record.id = ex_blind_email_record.original_file_id
        WHERE
                ex_blind_original_file_record.file_name=#{fileName}
          and ex_blind_original_file_record.project_name=#{projectName}
          AND ex_blind_original_file_record.is_approve = '1'
          AND ex_blind_email_record.type = '1'
    </select>
    <select id="judgeIsBlinded" parameterType="string" resultType="int">
        SELECT count(1) as result FROM ex_blind_operation_record WHERE original_file_id = #{fileId}
    </select>
    <select id="getOperateAndEmailHistory" parameterType="string" resultType="map">
        WITH result AS (
            SELECT
                any_value ( blind_records ) AS blind_records,
                max(file_id) as file_id,
                max(id) as operate_record_id,
                any_value ( user_id ) AS user_id,
                any_value ( user_name ) AS user_name,
                any_value ( blind_member_with_mail ) AS blind_member_with_mail,
                max( receive_emails ) AS receive_emails,
                any_value ( create_time ) AS create_time,
                any_value ( project_name ) AS project_name,
                any_value ( file_name ) AS file_name,
                any_value ( qc_time ) AS qc_time,
                max( send_time ) AS send_time,
                any_value ( type ) AS type,
                any_value ( role ) AS role,
                any_value ( is_approve ) AS is_approve,
                any_value ( file_path ) AS file_path,
                any_value ( qc_user_id ) AS qc_user_id,
                any_value ( blind_status ) AS blind_status,
                max( qc_comments ) AS qc_comments,
                any_value ( operate_name ) AS operate_name,
                max( compare_file_path ) AS preview_url,
                max( demand ) AS demand,
                max( project_member ) AS project_member
            FROM
                (
                    SELECT
                        ex_blind_operation_record.ex_data_id,
                        ex_blind_operation_record.id,
                        ex_blind_operation_record.file_id ,
                        blind_records,
                        user_id,
                        user_name,
                        blind_member_with_mail,
                        CASE
                            type
                            WHEN '2' THEN
                                ex_blind_email_record.receive_emails ELSE ''
                            END AS receive_emails,
                        DATE_FORMAT( ex_blind_operation_record.create_time, '%Y-%m-%dT%H:%i:%s.000+00:00' ) AS create_time,
                        ex_blind_operation_record.project_name,
                        ex_blind_operation_record.file_name,
                        DATE_FORMAT( ex_blind_operation_record.update_time, '%Y-%m-%dT%H:%i:%s.000+00:00' ) AS qc_time,
                        CASE
                            type
                            WHEN '2' THEN
                                DATE_FORMAT( ex_blind_email_record.create_time, '%Y-%m-%dT%H:%i:%s.000+00:00' ) ELSE ''
                            END AS send_time,
                        CASE
                            type
                            WHEN '0' THEN
                                '通知EDM(QC)'
                            WHEN '1' THEN
                                '通知EDM'
                            WHEN '2' THEN
                                '通知项目组' ELSE ''
                            END AS type,
                        role,
                        CASE
                            is_approve
                            WHEN '0' THEN
                                '未通过QC'
                            WHEN '1' THEN
                                '通过QC' ELSE '未知'
                            END AS is_approve,
                        file_path,
                        qc_user_id,
                        CASE

                            WHEN type = '0' THEN
                                '0'
                            WHEN type = '2' THEN
                                '1'
                            WHEN type = '1'
                                AND is_approve = '0' THEN
                                '0'
                            WHEN type = '1'
                                AND is_approve = '1' THEN
                                '1' ELSE '-1'
                            END AS blind_status,
                        qc_comments,
                        operate_name,
                        compare_file_path,
                        demand,
                        project_member
                    FROM
                        ex_blind_operation_record
                            LEFT JOIN ex_blind_email_record ON ex_blind_operation_record.file_id = ex_blind_email_record.file_id
                            AND ex_blind_operation_record.ex_data_id = ex_blind_email_record.ex_data_id
                ) AS result
            WHERE
                ex_data_id = #{exDataId}
            GROUP BY
                blind_records,
                create_time,
                qc_user_id,
                qc_time
        ) select
              file_id,
              operate_record_id,
              blind_records,
              user_id,
              user_name,
              blind_member_with_mail,
              receive_emails,
              create_time,
              project_name,
              file_name,
              CASE

                  WHEN qc_user_id IS NULL THEN
                      '' ELSE qc_time
                  END AS qc_time,
              send_time,
              type,
              role,
              CASE

                  WHEN qc_user_id IS NULL THEN
                      '' ELSE is_approve
                  END AS is_approve,
              file_path,
              qc_user_id,
              blind_status,
              qc_comments,
              operate_name,
              preview_url,
              demand,
              project_member
        FROM
            result
    </select>
    <select id="getOriginalIdAndStatus" parameterType="string" resultType="map">
        SELECT
            id,is_approve
        FROM
            ex_blind_original_file_record
        WHERE
            project_name = #{projectName}
          and file_name=#{fileName}
        ORDER BY
            update_time DESC
            LIMIT 1
    </select>
    <select id="getUnApprovedFileIdByOriginalId" parameterType="string" resultType="string">
        SELECT
            file_id
        FROM
            ex_blind_operation_record
        WHERE
            original_file_id = #{originalFileId}
          AND is_approve = '0' AND blind_status='Y'
        ORDER BY
            create_time DESC
            LIMIT 1
    </select>
    <select id="getCompareFileUrl" parameterType="string" resultType="string">
        SELECT
            compare_file_path
        FROM
            ex_blind_operation_record
        WHERE
            file_id = #{fileId}
    </select>

    <select id="getQCFileInfo" parameterType="string" resultType="map">
        SELECT
            file_id as id,
            compare_file_path as compareUrl
        FROM
            ex_blind_operation_record
        WHERE
            project_name = #{projectName}
          AND file_name = #{fileName}
          and operate_name is not null and file_id is not null and compare_file_path is not null
        ORDER BY update_time desc  LIMIT 1
    </select>
    <select id="checkTokenIsExist" parameterType="string" resultType="int">
        SELECT count(1) FROM cdtms_sso WHERE token=#{token} and is_expired='0';
    </select>
    <insert id="insertTokenRecord" parameterType="string">
        INSERT INTO cdtms_sso (token,is_expired) VALUES (#{token},'0');
    </insert>

    <select id="getBlindedFile" parameterType="string" resultType="string">
        select  file_path  from  ex_blind_operation_record where  file_id  = #{fileId} order by create_time  desc limit 1
    </select>
    <select id="getMemberInfoByFileId" parameterType="string" resultType="string">
        SELECT
            project_member
        FROM
            ex_blind_operation_record

        WHERE
            file_id = #{fileId}

    </select>

    <select id="getBlindMemberWithMail" parameterType="string" resultType="string">
        select blind_member_with_mail FROM  ex_blind_operation_record where  file_id  = #{fileId}
    </select>

    <select id="getDataSetPassword" resultType="map">
        SELECT
            id,
            token,
            user_name,
            role,
            project_id,
            PASSWORD,
            create_time,
            update_time
        FROM
            data_set_pass;
    </select>

    <select id="getDataSetPass" resultType="string">
        SELECT
            PASSWORD
        FROM
            data_set_pass
        where project_id =#{studyId}
        order by update_time desc limit 1;
    </select>


    <insert id="addScheduleRecord" parameterType="string">
        INSERT INTO schedule_node_info (id,user_id,node_name,version,studyId,cron,param,remark) VALUES (#{id},#{userId},#{nodeName},#{version},#{studyId},#{batchrun},#{jsonParam},#{remark});
    </insert>

    <select id="getScheduleReviewRecord" resultType="map">
        SELECT
            user_id,
            node_name,
            param,
            version,
            studyId,
            remark
        FROM
            schedule_node_info
        WHERE
            node_name LIKE '%定期审核-账号审核%' order by create_time limit 1;
    </select>

    <select id="getQueryReviewRecord" resultType="map">
        SELECT
            user_id,
            node_name,
            param,
            version,
            studyId,
            remark
        FROM
            schedule_node_info
        WHERE
            node_name LIKE '%定期审核-质疑管理%' order by create_time limit 1;
    </select>

    <select id="getTrailReviewRecord" resultType="map">
        SELECT
            user_id,
            node_name,
            param,
            version,
            studyId,
            remark
        FROM
            schedule_node_info
        WHERE
            node_name LIKE '%定期审核-稽查轨迹%' order by create_time limit 1;
    </select>
</mapper>
