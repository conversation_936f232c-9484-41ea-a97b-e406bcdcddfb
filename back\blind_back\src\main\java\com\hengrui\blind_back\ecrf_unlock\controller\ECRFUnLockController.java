package com.hengrui.blind_back.ecrf_unlock.controller;
import com.hengrui.blind_back.blind.entity.CSVTableDataEntity;
import com.hengrui.blind_back.blind.utils.ResponseResult;
import com.hengrui.blind_back.ecrf_unlock.service.ECRFUnLockService;
import com.hengrui.blind_back.ecrf_unlock.utils.FileUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description // ecrf文件解锁报告
 * @Date 10:22 2024/4/10
 * @Param
 * @return null
 **/
@RestController
public class ECRFUnLockController {


    @Autowired
    private ECRFUnLockService ecrfUnLockService;


    /**
     * 获取CSV表格数据
     *
     * @param fileId
     * @return
     */
    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getCurrentData")

    public ResponseResult<?> getCurrentData(String fileId) {
        CSVTableDataEntity results = ecrfUnLockService.getCurrentData(fileId);
        return new ResponseResult<>(200, "OK", results);
    }

    /*
     * <AUTHOR>
     * @Description //提交ecrf 页面参数,SAS程序运行所需
     * @Date 13:27 2024/4/11
     **/

    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/submitToSASECRF")

    public ResponseResult<?> submitToSASECRF(@RequestBody String param,
                                             String studyId,
                                             String userName,
                                             String submitDate,
                                             String taskId) {
        Map<String, String> results = ecrfUnLockService.submitToSASECRF(param, studyId, userName, submitDate, taskId);
        return new ResponseResult<>(200, "OK", results);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/downloadSnapshotData")

    public void downloadSnapshotData(String tableId, String token, String ufn, HttpServletResponse response) throws IOException {
        FileUtil.downloadSnapshotData(tableId, token, ufn, response);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/uploadUnLockData")
    public ResponseResult<?> uploadUnLockData(String taskId, String projectId, String fid) {
        String results = ecrfUnLockService.uploadUnLockData(taskId, projectId, fid);
        return new ResponseResult<>(200, "OK", results);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getEDCName")
    public ResponseResult<?> getEDCName(String taskId,String projectId) {
        String results = ecrfUnLockService.getEDCName(taskId,projectId);
        return new ResponseResult<>(200, "OK", results);
    }


}
