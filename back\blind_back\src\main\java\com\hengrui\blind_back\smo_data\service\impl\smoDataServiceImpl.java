package com.hengrui.blind_back.smo_data.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.smo_data.entity.SearchEntity;
import com.hengrui.blind_back.smo_data.mapper.SMODataMapper;
import com.hengrui.blind_back.smo_data.service.SMODataService;
import com.hengrui.blind_back.smo_data.util.SMOUtil;
import com.hengrui.blind_back.utils.CDTMSAPI;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.ObjectUtils;

import javax.mail.internet.MimeUtility;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * @ClassName smoDataServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/6 9:26
 * @Version 1.0
 **/
@Service
@Slf4j
public class smoDataServiceImpl  implements SMODataService {
    @Autowired
    SMODataMapper smoDataMapper;

    @Autowired
    SMOUtil smoUtil;

    @Override
    public String getSMODataBySite(SearchEntity entity, HttpServletResponse response) {
        ServletOutputStream os = null;
        FileInputStream fis = null;
        BufferedInputStream buff = null;
        File file =  smoUtil.exportCSV(SASOnlieConstant.SMO_MINIO_URL+URLEncoder.encode(entity.getFileName()),entity);
        if(!ObjectUtils.isEmpty(file)){
            try {
                byte[] uft8bom={(byte)0xef,(byte)0xbb,(byte)0xbf};
                os = response.getOutputStream();
                os.write(uft8bom);

                OutputStreamWriter writer = new OutputStreamWriter(os,"UTF-8");
                //下载文件,使用spring框架中的FileCopyUtils工具
                response.setCharacterEncoding("UTF-8");
                response.setContentType("application/octet-stream;charset=UTF-8");
                // 设置响应头
                String encodedFileName = new String(entity.getFileName().getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
                response.addHeader("Content-Disposition", "attachment;filename=\"" + encodedFileName + "\"");
                fis = new FileInputStream(file);
                buff = new BufferedInputStream(fis);
                FileCopyUtils.copy(buff, os);
            } catch (IOException e) {
                throw new RuntimeException(e);
            } finally {
                try {
                    if (null != fis) {
                        fis.close();
                    }
                    if (null != buff) {
                        buff.close();
                    }
                } catch (IOException e) {
                    log.error("流关闭异常");
                }


            }
            return "success";
        }
       return "no data";
    }

    @Override
    public List<Map<String, String>> getSMODataTst(SearchEntity entity) {
        List<Map<String, String>> labTableHeader = smoDataMapper.getTableContent(SASOnlieConstant.SMO_MINIO_URL+URLEncoder.encode("SHR0302-303_访视缺失.csv"),entity.getSiteNum());
        return labTableHeader;
    }

    @Override
    public String getSMOProjectLan(String studyId) {
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "xsht", "obj.studyid='" + studyId + "'", "", "");
        JSONArray objects = JSONObject.parseArray(dataListInfo);
        if(objects.size()>0){
            JSONObject object = JSONObject.parseObject(objects.get(0).toString());
            if(object.get("used_language").toString().equals("中文")){
                return  "CH";
            }else if(object.get("used_language").toString().equals("英文")){
               return "EN" ;
            }else{
                return "";
            }
        }else if(studyId.equals("RGL-2102T-101")){
            return  "CH";
        }else{
            return "notFound";
        }
    }


}
