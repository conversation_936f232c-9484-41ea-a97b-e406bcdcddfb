package com.hengrui.blind_back.blind.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hengrui.blind_back.blind.entity.CSVTableDataEntity;
import com.hengrui.blind_back.blind.service.BlindFunctionService;
import com.hengrui.blind_back.blind.utils.ResponseResult;
import com.hengrui.blind_back.ecrf_transfer.service.ECRFTransferService;
import com.hengrui.blind_back.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;



@RestController
@Slf4j
public class BlindFunctionController {
    @Autowired
    BlindFunctionService blindFunctionService;


    @Autowired
    ECRFTransferService ecrfTransferService;

    //获取csv解析数据入库
    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getCSVTable")
    @ResponseBody
    public ResponseResult<?> getCSVTable(String fileId) {
        CSVTableDataEntity results = blindFunctionService.getCSVTable(fileId);
        return new ResponseResult<>(200, "OK", results);
    }

    //提交编盲

    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/submitJsonParams")
    @ResponseBody
    public ResponseResult<?> submitJsonParams(@RequestBody String param, String blindDemand, String blindMember, String fileName, String projectName, String userName, String role,
                                              String originalFileId, String exDataId,
                                              String token, String dataType, String blindOperateName,
                                              String blindMemberWithMail) {
        String results = blindFunctionService.submitJsonParams(param, blindDemand, blindMember, fileName, projectName, userName, role, originalFileId, exDataId, token, dataType, blindOperateName,
                blindMemberWithMail);
        return new ResponseResult<>(200, "OK", results);
    }


    //获取csv解析数据入库
    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getCSVFile")
    @ResponseBody
    public ResponseResult<?> getCSVFile(String projectName, String fileName, String token) {
        String results = blindFunctionService.getCSVFile(projectName, fileName, token);
        return new ResponseResult<>(200, "OK", results);
    }

    //获取项目文件对应的遮盲后的数据集
    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getQCFileInfo")
    @ResponseBody
    public ResponseResult<?> getQCFileInfo(String fileName, String projectName) {
        Map<String, String> results = blindFunctionService.getQCFileInfo(fileName, projectName);
        return new ResponseResult<>(200, "OK", results);
    }

    //获取最近的编盲记录
    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getBlindHistory")
    @ResponseBody
    public ResponseResult<?> getBlindHistory(String projectName, String fileName) {
        List<Map<String, String>> results = blindFunctionService.getBlindHistory(projectName, fileName);
        return new ResponseResult<>(200, "OK", results);
    }


    //查询历史编盲记录详情
    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getBlindOperate")
    @ResponseBody
    public ResponseResult<?> getBlindOperate(String historyId) {
        JSON results = blindFunctionService.getBlindOperate(historyId);
        return new ResponseResult<>(200, "OK", results);
    }

    //查询历史编盲记录的需求对象和名称
    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getDemandAndName")
    @ResponseBody
    public ResponseResult<?> getDemandAndName(String historyId) {
        Map<String, String> result = blindFunctionService.getDemandAndName(historyId);
        return new ResponseResult<>(200, "OK", result);
    }

    //上传QC记录并发送
    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/upload")
    @ResponseBody
    public ResponseResult<?> fileUpload(HttpServletRequest req, String fileName, String projectName, String userName, String isApprove, String fileId, String url, String exDataId, String comments) {
        List<Map<String, Object>> results = blindFunctionService.fileUpload(req, fileName, projectName, userName, isApprove, fileId, url, exDataId, comments);
        return new ResponseResult<>(200, "OK", results);
    }

    //查询未审核通过的原始文件id

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getUnBlindOriginFileId")
    @ResponseBody
    public ResponseResult<?> getUnBlindOriginFileId(String fileName, String projectName) {
        String result = blindFunctionService.getUnBlindOriginFileId(fileName, projectName);
        return new ResponseResult<>(200, "OK", result);
    }

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getProjectMembersInfo")
    @ResponseBody
    public ResponseResult<?> getProjectMembersInfo(String projectName) {
        List<Map<String, Object>> result = blindFunctionService.getProjectMembersInfo(projectName);
        return new ResponseResult<>(200, "OK", result);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/sendFinalData")
    @ResponseBody
    public ResponseResult<?> sendFinalData(@RequestParam("emails") List<String> emails, @RequestParam("fileName") String fileName,
                                           @RequestParam("projectName") String projectName, @RequestParam("userName") String userName,
                                           @RequestParam("exDataId") String exDataId) {
        List<Map<String, Object>> results = blindFunctionService.sendFinalData(emails, fileName, projectName, userName, exDataId);
        return new ResponseResult<>(200, "OK", results);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getFileIsSendCount")
    @ResponseBody
    public ResponseResult<?> getFileIsSendCount(@RequestParam("fileName") String fileName, @RequestParam("projectName") String projectName) {
        boolean result = blindFunctionService.getFileIsSendCount(fileName, projectName);
        return new ResponseResult<>(200, "OK", result);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/judgeIsBlinded")
    @ResponseBody
    public ResponseResult<?> judgeIsBlinded(@RequestParam("fileId") String fileId) {
        boolean result = blindFunctionService.judgeIsBlinded(fileId);
        return new ResponseResult<>(200, "OK", result);
    }

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/checkLogin")
    @ResponseBody
    public ResponseResult<?> checkLogin(@RequestParam("token") String token, String isLogin) {
        JSONObject result = blindFunctionService.checkLogin(token, isLogin);
        return new ResponseResult<>(200, "OK", result);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/OperateAndEmailRecords")
    @ResponseBody
    public ResponseResult<?> OperateAndEmailRecords(@RequestParam("id") String id) {
        List<Map<String, Object>> result = blindFunctionService.OperateAndEmailRecords(id);
        if (result.size() > 0) {
            return new ResponseResult<>(200, "OK", result);
        } else {
            return new ResponseResult<>(400, "error", result);
        }

    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getCompareFileUrl")
    @ResponseBody
    public ResponseResult<?> getCompareFileUrl(@RequestParam("fileId") String fileId) {
        String result = blindFunctionService.getCompareFileUrl(fileId);
        return new ResponseResult<>(200, "OK", result);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getBlindedFile")
    @ResponseBody
    public ResponseResult<?> getBlindedFile(@RequestParam("fileId") String fileId, HttpServletResponse response) {
        String result = blindFunctionService.getBlindedFile(fileId, response);
//        return new ResponseResult<>(200, "OK", result);
        return null;
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getMemberInfoByFileId")
    @ResponseBody
    public ResponseResult<?> getMemberInfoByFileId(String fileId) {
        String result = blindFunctionService.getMemberInfoByFileId(fileId);
        if (!ObjectUtils.isEmpty(result) && !result.isEmpty()) {
            return new ResponseResult<>(200, "OK", result);
        } else {
            return new ResponseResult<>(404, "resource not found", result);
        }

    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getMemberWithMailByFileId")
    @ResponseBody
    public ResponseResult<?> getMemberWithMailByFileId(String fileId) {
        String result = blindFunctionService.getMemberWithMailByFileId(fileId);
        if (!ObjectUtils.isEmpty(result) && !result.isEmpty()) {
            return new ResponseResult<>(200, "OK", result);
        } else {
            return new ResponseResult<>(404, "resource not found", result);
        }

    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/setDataSetPass")
    @ResponseBody
    public ResponseResult<?> setDataSetPass(@RequestParam("password") String password, @RequestParam("userName") String userName, @RequestParam("studyId") String studyId
            , @RequestParam("taskId") String taskId) {
        String results = blindFunctionService.setDataSetPass(password, userName, studyId, taskId);
        return new ResponseResult<>(200, "OK", results);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getDataSetPass")
    @ResponseBody
    public ResponseResult<?> getDataSetPass(String studyId) {
        String result = blindFunctionService.getDataSetPass(studyId);
        return new ResponseResult<>(200, "OK", result);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getLoginInfo")
    @ResponseBody
    public ResponseResult<?> getLoginInfo(@RequestParam("taskId") String taskId, String projectId) {
        JSONObject result = blindFunctionService.getLoginInfo(taskId, projectId);
        return new ResponseResult<>(200, "OK", result);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping(value = "/testSchedule", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public ResponseResult<?> testSchedule(@RequestParam("token") String token, @RequestBody String paramB) {
        Map<String, Object> result = ecrfTransferService.getECRFTransferFile(token, paramB);
        return new ResponseResult<>(200, "OK", result);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/uploadRaveData")
    @ResponseBody
    public ResponseResult<?> setDataSetPass(@RequestParam("file") MultipartFile file, @RequestParam("userName") String userName, @RequestParam("studyId") String studyId,
                                            @RequestParam("taskId") String taskId, @RequestParam("server") String server) {
        log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        try {
            // 调用服务层方法处理文件
            blindFunctionService.processUploadedFile(file, userName, studyId, taskId, server);
            return new ResponseResult<>(200, "OK", ResponseEntity.ok("File uploaded and processed successfully."));
        } catch (Exception e) {
            return new ResponseResult<>(500, "FAil", ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("File upload failed: " + e.getMessage()));
        }

    }

}
