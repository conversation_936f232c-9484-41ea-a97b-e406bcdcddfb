package com.hengrui.blind_back.blind.utils;

import com.hengrui.blind_back.blind.constant.BlindConstant;
import com.hengrui.blind_back.blind.mapper.BlindBackMapper;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.sas.iom.SAS.ILanguageService;
import com.sas.iom.SAS.ILanguageServicePackage.CarriageControlSeqHolder;
import com.sas.iom.SAS.ILanguageServicePackage.LineTypeSeqHolder;
import com.sas.iom.SAS.IWorkspace;
import com.sas.iom.SAS.IWorkspaceHelper;
import com.sas.iom.SASIOMDefs.GenericError;
import com.sas.iom.SASIOMDefs.StringSeqHolder;
import com.sas.services.connection.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;


@Component
public class SASTrigger {

    @Autowired
    SASIOM sasiom;

    @Autowired
    BlindBackMapper blindBackMapper;



    public Map<String, Object> runCreateSAS(String codePath, String paramPath, String CSVPath, String projectName) throws IOException, GenericError, ConnectionFactoryException, ConnectionFactoryException, GenericError {
        String logPath = "";
        Boolean isSuccess = true;
        Map<String, Object> result = new HashMap<>();
        String classID = Server.CLSID_SAS;
        String host = BlindConstant.SAS_SERVER_URL;
        int port = 8591;
        Server server = new BridgeServer(classID, host, port);

        // make a connection factory configuration with the server
        ConnectionFactoryConfiguration cxfConfig =
                new ManualConnectionFactoryConfiguration(server);

        // get a connection factory manager
        ConnectionFactoryManager cxfManager = new ConnectionFactoryManager();

        // get a connection factory that matches the configuration
        ConnectionFactoryInterface cxf = cxfManager.getFactory(cxfConfig);

        // get the administrator interface
        ConnectionFactoryAdminInterface admin = cxf.getAdminInterface();

        // get a connection
        String userName = SASOnlieConstant.SAS_USER;
        String password = SASOnlieConstant.SAS_PASS;
        try {
            ConnectionInterface cx = cxf.getConnection(userName, password);
            try {
                // Narrow the connection from the server.
                org.omg.CORBA.Object obj = cx.getObject();
                IWorkspace iWorkspace = IWorkspaceHelper.narrow(obj);

                //insert iWorkspace workspace usage code here
                ILanguageService iLanguageService = iWorkspace.LanguageService();
                iLanguageService.Submit(sasiom.readSAS(codePath, paramPath, CSVPath, projectName));

                //sas log 获取
                CarriageControlSeqHolder logCarriageControlHldr = new CarriageControlSeqHolder();
                LineTypeSeqHolder logLineTypeHldr = new LineTypeSeqHolder();
                StringSeqHolder logHldr = new StringSeqHolder();
                iLanguageService.FlushLogLines(Integer.MAX_VALUE, logCarriageControlHldr, logLineTypeHldr, logHldr);
                //创建文件存放的地址
                File logFolder = new File(BlindConstant.SAS_BLIND_LOG_PATH + projectName);
                if (logFolder.isDirectory() || !logFolder.exists()) {
                    logFolder.mkdirs();
                }
                String logFilePrefix = paramPath.split(".json")[0];
                logPath = logFolder.getAbsolutePath() + BlindConstant.FILE_SEPARATOR + logFilePrefix + "_log.txt";
                String[] logLines = logHldr.value;
                try (BufferedWriter writer = new BufferedWriter(new FileWriter(logPath, "UTF-8".isEmpty()))) {
                    for (String line : logLines) {
                        //解析sas log文件，查询关键词 ERROR:  找到调用失败，找不到调用成功
                        writer.write(line);
                        writer.newLine();
                        if (line.contains("ERROR:")) {
                            isSuccess = false;
                        }
                    }
                } catch (IOException e) {
                    // Handle the exception
                    isSuccess = false;
                }
            } finally {
                cx.close();
            }
        } catch (Exception e) {
            blindBackMapper.updateFileID("", "", "", "", "SAS TRIGGER ERROR !!!", "N", "");
        }
        result.put("logPath", logPath);
        result.put("isSuccess", isSuccess);
        return result;
    }


}
