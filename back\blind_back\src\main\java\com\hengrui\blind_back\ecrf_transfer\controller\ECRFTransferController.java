package com.hengrui.blind_back.ecrf_transfer.controller;

import com.hengrui.blind_back.ecrf_transfer.service.ECRFTransferService;
import com.hengrui.blind_back.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName ECRFTransferController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/6 16:39
 * @Version 1.0
 **/
@RestController
@Slf4j
public class ECRFTransferController {

    @Autowired
    ECRFTransferService ecrfTransferService;

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getECRFTransferFile")

    public Map<String, Object> getECRFTransferFile(String taskId,
                                                   String server,
                                                   String projectId) {
        ECRFTransferController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, Object> results = ecrfTransferService.getECRFTransferFile(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", results);
        return result;

    }

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/ECRFOnlineApproval")
    public Map<String, Object> ECRFOnlineApproval(String taskId,
                                                   String server,
                                                   String projectId) {
        ECRFTransferController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> results = ecrfTransferService.ECRFOnlineApproval(taskId,projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
   /*     result.put("data", results);*/
        return result;
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/ECRFMatcheByList")
    public Map<String, Object> ECRFMatcheByList(String taskId,
                                                  String server,
                                                  String projectId) {
        ECRFTransferController.log.info("server is :" + server);
        Map<String, String> results = ecrfTransferService.ECRFMatcheByList(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        /*     result.put("data", results);*/
        return result;
    }
}
