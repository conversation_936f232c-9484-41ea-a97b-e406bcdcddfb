package com.hengrui.blind_back.parse_excel_toDB.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.fastjson.JSON;
import com.hengrui.blind_back.blind.utils.MinioUtil;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.parse_excel_toDB.entity.DBDefineEntity;
import com.hengrui.blind_back.parse_excel_toDB.mapper.ParseExcelToDBMapper;
import com.hengrui.blind_back.parse_excel_toDB.service.ParseExcelToDBService;
import com.hengrui.blind_back.utils.CDTMSAPI;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ParseExcelToDBServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/2 14:39
 * @Version 1.0
 **/
@Service
@Slf4j
public class ParseExcelToDBServiceImpl implements ParseExcelToDBService {
    @Autowired
    private MinioUtil minioUtil;
    @Autowired
    ParseExcelToDBMapper parseExcelToDBMapper;


    @Override
    public Map<String, String> parseDBDefineDatatoDB(String taskId, String projectId) {
        //return result
        Map<String, String> result = new HashMap<>();
        //1.get studyId from CDTMS API
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId="";
        String tableId="";
        if(!ObjectUtils.isEmpty(formInfo.get("recordId"))){
            recordId= formInfo.get("recordId");
            tableId=formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id",recordId);
            object.put("formData",formData);
            projectId=object.toJSONString();
        }else{
            tableId=projectId;
            recordId=taskId;
        }
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");
        String studyId = formInfo.get("studyId");
        String dataVersion = formInfo.get("dataVersion").split("V")[1];
        studyId = studyId.replace("Ⅲ", "III").replace("Ⅱ", "II").replace("Ⅰ", "I");
        //2.get DBDefine file from minio edcserver bucket
        String fileName = studyId + "_PRO.xlsx";
        String filePath = "/home/<USER>/8087/dbs/" + studyId + "_PRO.xlsx";
        boolean isSuccess = minioUtil.downloadObjectWithPath("edcserver", "dbs/" + fileName, "/home/<USER>/8087/dbs");
        //3.parse Local excel file into dataBase
        if (isSuccess) {
            try (ExcelReader excelReader = EasyExcel.read(filePath).build()) {
                List<DBDefineEntity> dbDefineDataList = EasyExcel.read(filePath).head(DBDefineEntity.class).sheet(1).doReadSync();
                //4.set uuid and studyId into dbDefineDataList
                for (DBDefineEntity dbDefineEntity : dbDefineDataList) {
                    String uuid = ULIDGenerator.generateULID();
                    dbDefineEntity.setUuId(uuid);
                    dbDefineEntity.setStudyid(studyId);
                    dbDefineEntity.setVersionv(dataVersion);
                    ParseExcelToDBServiceImpl.log.info("------------------------------I just read this file : {}", JSON.toJSONString(dbDefineEntity));
                }
//                parseExcelToDBMapper.insertDatatoECRFDB(dbDefineDataList);
                //set the dbDefineDataList into usersyn/dataSave
                String response = CDTMSAPI.usersyndataSave(token, "ecrf", formId, "<EMAIL>", "list", JSON.toJSONString(dbDefineDataList));
                ParseExcelToDBServiceImpl.log.info("----------------------------------the array size is : {}", dbDefineDataList.size());
                ParseExcelToDBServiceImpl.log.info("------------------------------call usersyn datasave response is : {}", response);
                result.put("count", String.valueOf(dbDefineDataList.size()));
            }
        }
        result.put("status", "success");
        return result;
    }
}
