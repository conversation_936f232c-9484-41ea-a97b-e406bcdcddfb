package com.hengrui.blind_back.ecrf_transfer.service.impl;

import cn.hutool.json.JSONObject;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.fastjson.JSON;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.ecrf_transfer.entity.ECRFEnity;
import com.hengrui.blind_back.ecrf_transfer.service.ECRFTransferService;
import com.hengrui.blind_back.ecrf_unlock.utils.FileUtil;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.hengrui.blind_back.utils.CallPython;
import com.hengrui.blind_back.utils.SFTPFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName ECRFTransferServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/6 16:40
 * @Version 1.0
 **/
@Service
@Slf4j
public class ECRFTransferServiceImpl implements ECRFTransferService {

    @Autowired
    CallPython callPython;

    @Override
    public Map<String, Object> getECRFTransferFile(String taskId, String projectId) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String tableId = "";
        String recordId = "";
        //1.1 get id from formInfoByTaskId
        String data = formInfo.get("param");
        String dataId = "";
        if (!data.isEmpty()) {
            JSONObject formInfoData = new JSONObject(data);
            dataId = formInfoData.get("id").toString();
        }

        if (!ObjectUtils.isEmpty(formInfo.get("recordId"))) {
            recordId = formInfo.get("recordId");
            tableId = formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }
        tableId = formInfo.get("tableId");

        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        //获取传输目的
        //1.1 get id from formInfoByTaskId
        String formData = formInfo.get("param");
        String transferReason = "";
        String subFolderStr="";
        String subFolder="";
        if (!formData.isEmpty()) {
            JSONObject formInfoData = new JSONObject(formData);
            if (tableId.equals("crf_handover")) {
                transferReason = formInfoData.get("transfer_reason").toString();
            } else if (tableId.equals("data_submission")) {
                transferReason = formInfoData.get("nda_or").toString();
                subFolderStr= formInfoData.get("subfolder_path1").toString();
                subFolder=String.join("/", subFolderStr.split("-"));
            }

        }
        ECRFTransferServiceImpl.log.info("------------获取到的表单名称是是：" + tableId + "------------");
        ECRFTransferServiceImpl.log.info("------------获取到的formId是：" + formId + "------------");
        ECRFTransferServiceImpl.log.info("------------获取到的studyId是：" + studyId + "------------");
        ECRFTransferServiceImpl.log.info("------------获取到的子文件路径是：" + subFolder + "------------");
        Map<String, Object> result = new HashMap<>();
        List<Map<String, String>> filesFromEDC = new ArrayList<>();
        Map<String, String> fileObject = new HashMap<>();
        if (tableId.equals("crf_handover")) {
            fileObject.put("fid", "file");
        } else if (tableId.equals("data_submission")) {
            fileObject.put("fid", "subject_crf_audit_trial");
        }

        fileObject.put("fileType", ".zip");
        filesFromEDC.add(fileObject);

        String uuid = ULIDGenerator.generateULID();
        Map<String, String> ENVInfo = new HashMap<>();
        if (!transferReason.isEmpty() && (transferReason.equals("项目锁库后") || transferReason.equals("否"))) {
            if(transferReason.equals("否")){
                ENVInfo.put("ftp", "/Projects/"+studyId+"/EDC/"+subFolder+"/");
            }else if (transferReason.equals("项目锁库后")){
                ENVInfo.put("ftp",  "/Projects/CDTMS手册优化测试项目/0/");
            }

        }
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("ENV", "PRO");
        ENVInfo.put("data_type", "Subject_CRF");
        ENVInfo.put("studyId", studyId);
        //当定时调度时，这里的taskId为记录ID
        ENVInfo.put("taskId", dataId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", tableId);
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        ENVInfo.put("data_format", "zip");
        ENVInfo.put("fileSuffix", "a".toString());
        ENVInfo.put("isLatest", "YES");
        ENVInfo.put("fileNameSuffix", "_稽查历史");
        Map<String,String> pyResult=callPython.downloadEDCServerFile(ENVInfo, filesFromEDC);
        String resultsA = pyResult.get("original_name");


        //下载无稽查历史ecrf
        formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("isLatest", "NO");
        ENVInfo.put("fileNameSuffix", "_无稽查历史");
        List<Map<String, String>> filesFromEDCNoCheck = new ArrayList<>();
        Map<String, String> fileObjectNocheck = new HashMap<>();

        if (tableId.equals("crf_handover")) {
            fileObjectNocheck.put("fid", "file2");
        } else if (tableId.equals("data_submission")) {
            fileObjectNocheck.put("fid", "subject_crf");
        }


        fileObjectNocheck.put("fileType", ".zip");
        filesFromEDCNoCheck.add(fileObjectNocheck);
        pyResult=callPython.downloadEDCServerFile(ENVInfo, filesFromEDCNoCheck);
        String resultsB = pyResult.get("original_name");
        result.put("result", resultsA);
        //update crf file location
        if (!transferReason.isEmpty() && (transferReason.equals("项目锁库后") || transferReason.equals("否"))) {
            //1.call getInfo API
            Map<String, String> formInfoByTaskId = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
            if (!ObjectUtils.isEmpty(formInfoByTaskId.get("recordId"))) {
                recordId = formInfoByTaskId.get("recordId");
                tableId = formInfoByTaskId.get("tableId");
                com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
                com.alibaba.fastjson.JSONObject formDatas = object.getJSONObject("formData");
                formDatas.put("id", recordId);
                object.put("formData", formDatas);
                projectId = object.toJSONString();
            } else {
                tableId = projectId;
                recordId = taskId;
            }
            tableId = formInfoByTaskId.get("tableId");
            //1.1 get id from formInfoByTaskId
            data = formInfoByTaskId.get("param");
            dataId = "";
            if (!data.isEmpty()) {
                JSONObject formInfoData = new JSONObject(data);
                dataId = formInfoData.get("id").toString();
            }

            ECRFTransferServiceImpl.log.info("--------------查到的记录ID是：  " + dataId);
            //5.put id into data
            JSONObject temp = new JSONObject();
            JSONObject param = new JSONObject();
            temp.put("id", dataId);

            if (tableId.equals("crf_handover")) {
                temp.put("server", "FTP");
                temp.put("file_position", "/Projects/CDTMS手册优化测试项目/0/");
            }
            if (!data.isEmpty()) {
                JSONObject formInfos = new JSONObject(data);
                if (tableId.equals("data_submission") && tableId.equals("data_submission") && formInfos.get("data_version").toString().equals("Submitted") && formInfos.get("mode").toString().equals("sFTP")) {
                    temp.put("server", "FTP");
                    temp.put("path", "/Projects/"+studyId+"/EDC/"+subFolder+"/");
                }
            }


            param.put("data", temp);
            ECRFTransferServiceImpl.log.info("call dataSave params is :" + param.toString());
            String newFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
            param.put("formId", newFormId);
            param.put("taskId", taskId);
            param.put("projectId", projectId);
            ECRFTransferServiceImpl.log.info("最新的formId是 :" + newFormId);
            CDTMSAPI.dataSave(param);
        }
        //调用清单筛选方法
        ECRFMatcheByList(taskId, projectId);

        return result;
    }

    @Override
    public Map<String, String> ECRFOnlineApproval(String taskId, String projectId) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String tableId = "";
        String recordId = "";
        if (!ObjectUtils.isEmpty(formInfo.get("recordId"))) {
            recordId = formInfo.get("recordId");
            tableId = formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        ECRFTransferServiceImpl.log.info("------------获取到的formId是：" + formId + "------------");
        ECRFTransferServiceImpl.log.info("------------获取到的studyId是：" + studyId + "------------");
        Map<String, String> result = new HashMap<>();
        List<Map<String, String>> filesFromEDC = new ArrayList<>();
        Map<String, String> fileObject = new HashMap<>();
        fileObject.put("fid", "xmsjkdybg");
        fileObject.put("fileType", ".xlsx");
        filesFromEDC.add(fileObject);


        String uuid = ULIDGenerator.generateULID();
        String customUUID = "4A8D087A05E54B378493EE56F3C1AF74";
        Map<String, String> ENVInfo = new HashMap<>();
        ENVInfo.put("uuid", customUUID);
        ENVInfo.put("ENV", "UAT");
        ENVInfo.put("data_type", "DB_Definition");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", recordId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", tableId);
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        ENVInfo.put("data_format", "Excel");
        ENVInfo.put("fileSuffix", "a".toString());
        ENVInfo.put("isLatest", "");
        //call python program
        Map<String,String> pyResult=callPython.downloadEDCServerFile(ENVInfo, filesFromEDC);
        String results = pyResult.get("original_name");
        result.put("result", results);

         result = new HashMap<>();

         //上传配置比对报告
        List<Map<String, String>> filesFromEDCNew = new ArrayList<>();
        Map<String, String> fileObjectNew = new HashMap<>();
        fileObjectNew.put("fid", "compare_report");
        fileObjectNew.put("fileType", ".zip");
        filesFromEDCNew.add(fileObjectNew);

        formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
         uuid = ULIDGenerator.generateULID();
        Map<String, String> ccrENVInfo = new HashMap<>();
        ccrENVInfo.put("uuid", uuid);
        ccrENVInfo.put("ENV", "UAT");
        ccrENVInfo.put("data_format", "zip");
        ccrENVInfo.put("data_type", "config_compare_report");
        ccrENVInfo.put("studyId", studyId);
        ccrENVInfo.put("taskId", recordId);
        ccrENVInfo.put("formId", formId);
        ccrENVInfo.put("projectId", tableId);
        ccrENVInfo.put("fileSuffix", "a".toString());
        ccrENVInfo.put("isLatest", "");
        ccrENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
         pyResult=callPython.downloadEDCServerFile(ccrENVInfo, filesFromEDCNew);
         results = pyResult.get("original_name");
        result.put("result", results);

        return result;
    }

    @Override
    public Map<String, String> ECRFMatcheByList(String taskId, String projectId) {
        //1.获取表单信息，包括单选的传输目的值，根据传输目分支是两种数据集匹配方法
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId = "";
        String tableId = "";
        if (!ObjectUtils.isEmpty(formInfo.get("recordId"))) {
            recordId = formInfo.get("recordId");
            tableId = formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }
        tableId = formInfo.get("tableId");
        String param = formInfo.get("param");
        cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(param);
        String studyId = formInfoData.get("studyid").toString();
        String data = formInfo.get("param");
        String dataId = "";
        if (!data.isEmpty()) {
            JSONObject formInfoDatas = new JSONObject(param);
            dataId = formInfoDatas.get("id").toString();
        }


        String transferReason = "";
        String subFolderStr = "";
        String subFolder = "";
        if (tableId.equals("crf_handover")) {
            transferReason = formInfoData.get("transfer_reason").toString();
        } else if (tableId.equals("data_submission")) {
            transferReason = formInfoData.get("nda_or").toString();
            subFolderStr= formInfoData.get("subfolder_path1").toString();
            subFolder=String.join("/", subFolderStr.split("-"));
        }
        //2.数据集下载，本地解压缩，返回文件夹父级目录位置
        //2.1 get token
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, tableId);
        String id = formInfoData.get("id").toString();
        String dataListInfo = "";
        if (tableId.equals("crf_handover")) {
            dataListInfo = CDTMSAPI.getDataListInfo(token, "crf_handover", "obj.id='" + id + "'", "edit", "");
        } else if (tableId.equals("data_submission")) {
            dataListInfo = CDTMSAPI.getDataListInfo(token, "data_submission", "obj.id='" + id + "'", "edit", "");
        }


        ECRFTransferServiceImpl.log.info(dataListInfo);
        String temp = JSON.parseArray(dataListInfo).get(0).toString();
        String ecrf = "";
        String ecrfList = "";
        if (tableId.equals("crf_handover")) {
            ecrf = JSON.parseObject(temp).get("file2").toString();
        } else if (tableId.equals("data_submission")) {
            ecrf = JSON.parseObject(temp).get("subject_crf").toString();
        }
        ecrfList = JSON.parseObject(temp).get("subject_list").toString();


        int matcheFileSize = 0;
        int listSize = 0;
        //判断ecrf字段和ecrfList字段是否为空，非空执行清单筛选逻辑，否则，不执行
        if (!ecrf.isEmpty() && !ecrfList.isEmpty()) {
            ECRFTransferServiceImpl.log.info("---------------------------------------------执行了清单筛选的逻辑---------------------------------------------");
            String dateStr = "";
            String[] split = ecrf.split("\\*");
            String partOne = split[0];
            String[] s = partOne.split("_");
            dateStr = s[3];
            ECRFTransferServiceImpl.log.info(dateStr);
            //受试者crf下载
            String zipPath = CDTMSAPI.queryFileDownload(ecrf, token, "zip", tableId);

            //3.受试者清单下载，并提取sheet1-B列 数组
            String xlsxPath = CDTMSAPI.queryFileDownload(ecrfList, token, "xlsx", tableId);

            //4.根据受试者清单，匹配数据集，并返回匹配结果，遍历获取数据集父级目录下的所有pdf文件
            try (ExcelReader excelReader = EasyExcel.read(xlsxPath).build()) {
                List<ECRFEnity> dbDefineDataList = EasyExcel.read(xlsxPath).head(ECRFEnity.class).sheet(0).doReadSync();
                // Extract only the ecrfNum values
                List<String> ecrfNumList = dbDefineDataList.stream().map(ECRFEnity::getEcrfNum).collect(Collectors.toList());
                ECRFTransferServiceImpl.log.info(ecrfNumList.toString());
                listSize = ecrfNumList.size();
                //4.1 解压下载好的zip文件，将zip中的pdf文件提取到指定的文件夹
                try {
                    ECRFTransferServiceImpl.log.info("---------------------待解压缩文件的路径为:" + zipPath.toString() + "压缩好的文件存放地址为:" + SASOnlieConstant.ECRF_LIST_FOLDER + "    xlsx文件路径为:" + xlsxPath);
                    com.hengrui.blind_back.utils.FileUtils.unzip(zipPath, SASOnlieConstant.ECRF_LIST_FOLDER);
                    //5.将匹配后的文件列表，压缩到指定位置
                    matcheFileSize = com.hengrui.blind_back.utils.FileUtils.findAndZipMatchingFiles(SASOnlieConstant.ECRF_LIST_FOLDER, ecrfNumList, SASOnlieConstant.ECRF_LIST_FOLDER + File.separator + studyId + "_death_ae_disc_受试者CRF_" + dateStr + ".zip");

                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            //6.上传压缩包到cdtms对应表单的"list_subject_crf" 字段
            String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
            boolean zipFileEmpty = com.hengrui.blind_back.utils.FileUtils.isZipFileEmpty(SASOnlieConstant.ECRF_LIST_FOLDER + File.separator + studyId + "_death_ae_disc_受试者CRF_" + dateStr + ".zip");
            if (!zipFileEmpty) {
                File uploadFile = new File(SASOnlieConstant.ECRF_LIST_FOLDER + File.separator + studyId + "_death_ae_disc_受试者CRF_" + dateStr + ".zip");
                if (tableId.equals("crf_handover")) {
                    FileUtil.uploadSASOutputFile(dataId, formId, "list_subject_crf", SASOnlieConstant.ECRF_LIST_FOLDER + File.separator + studyId + "_death_ae_disc_受试者CRF_" + dateStr + ".zip", tableId, SASOnlieConstant.REMOTE_SERVER_API_PREFIX, uploadFile.getName(), "zip");
                } else if (tableId.equals("data_submission")) {
                    FileUtil.uploadSASOutputFile(dataId, formId, "subject_list_2", SASOnlieConstant.ECRF_LIST_FOLDER + File.separator + studyId + "_death_ae_disc_受试者CRF_" + dateStr + ".zip", tableId, SASOnlieConstant.REMOTE_SERVER_API_PREFIX, uploadFile.getName(), "zip");
                }

                if (!transferReason.isEmpty() && (transferReason.equals("NDA递交") || transferReason.equals("是")) && uploadFile.exists()) {
                    //1.上传文件到ftp
                    if (tableId.equals("crf_handover")) {
                        SFTPFile.uploadFileToSFTP(uploadFile.getAbsolutePath(), "/Projects/CDTMS手册优化测试项目/1/", "clinical-ftp.hengrui.com", 22357, SASOnlieConstant.RTSM_API_USER, SASOnlieConstant.RTSM_API_PASS, uploadFile.getName());
                    } else if (tableId.equals("data_submission")) {
                        SFTPFile.uploadFileToSFTP(uploadFile.getAbsolutePath(), "/Projects/"+studyId+"/EDC/"+subFolder+"/", "clinical-ftp.hengrui.com", 22357, SASOnlieConstant.RTSM_API_USER, SASOnlieConstant.RTSM_API_PASS, uploadFile.getName());
                        JSONObject temps = new JSONObject();
                        JSONObject paramt = new JSONObject();
                        if (!data.isEmpty()) {
                            //5.put id into data
                            JSONObject formInfos = new JSONObject(data);
                            if (tableId.equals("data_submission") && tableId.equals("data_submission") && formInfos.get("data_version").toString().equals("Submitted") && formInfos.get("mode").toString().equals("sFTP")) {
                                temps.put("server", "FTP");
                                temps.put("path", "/Projects/"+studyId+"/EDC/"+subFolder+"/"+uploadFile.getName());
                            }
                        }


                        paramt.put("data", temps);
                        ECRFTransferServiceImpl.log.info("call dataSave params is :" + paramt.toString());
                        String newFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
                        paramt.put("formId", newFormId);
                        paramt.put("taskId", taskId);
                        paramt.put("projectId", projectId);
                        ECRFTransferServiceImpl.log.info("最新的formId是 :" + newFormId);
                        CDTMSAPI.dataSave(paramt);
                    }

                    ECRFTransferServiceImpl.log.info("---------------------------------------------执行了CRF文件上传到FTP的逻辑---------------------------------------------");
                    //1.call getInfo API
                    Map<String, String> formInfoByTaskId = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
                    if (!ObjectUtils.isEmpty(formInfoByTaskId.get("recordId"))) {
                        recordId = formInfoByTaskId.get("recordId");
                        tableId = formInfoByTaskId.get("tableId");
                        com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
                        com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
                        formData.put("id", recordId);
                        object.put("formData", formData);
                        projectId = object.toJSONString();
                    } else {
                        tableId = projectId;
                        recordId = taskId;
                    }
                    tableId = formInfoByTaskId.get("tableId");
                    //1.1 get id from formInfoByTaskId
                    data = formInfoByTaskId.get("param");
                    dataId = "";
                    if (!data.isEmpty()) {
                        JSONObject formInfos = new JSONObject(data);
                        dataId = formInfos.get("id").toString();
                    }
                    //5.put id into data
                    JSONObject temps = new JSONObject();
                    JSONObject params = new JSONObject();
                    String remark = "清单中" + listSize + "个受试者，生成的文件有" + matcheFileSize + "个受试者";

                    if (tableId.equals("crf_handover")) {
                        temps.put("notes", remark);
                        temps.put("file_position", "/Projects/CDTMS手册优化测试项目/1/");
                    }

                    temps.put("id", dataId);

                    if (!data.isEmpty()) {
                        JSONObject formInfos = new JSONObject(data);
                        if (tableId.equals("data_submission") && formInfos.get("data_version").toString().equals("Submitted") && formInfos.get("mode").toString().equals("sFTP")) {
                            temps.put("server", "FTP");
                            temps.put("bz", remark);
                            temps.put("path", "/Projects/CDTMS手册优化测试项目/1/");
                        }
                    }

                    params.put("data", temps);
                    ECRFTransferServiceImpl.log.info("call dataSave params is :" + params.toString());
                    String newFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
                    params.put("formId", newFormId);
                    params.put("taskId", taskId);
                    params.put("projectId", projectId);
                    ECRFTransferServiceImpl.log.info("最新的formId是 :" + newFormId);
                    CDTMSAPI.dataSave(params);
                    File directory = new File(SASOnlieConstant.ECRF_LIST_FOLDER);
                    File[] files = directory.listFiles();
                    //从目录中删除每个文件
                    for (File file : files) {
                        file.delete();
                    }
                } else if (!transferReason.isEmpty() && (transferReason.equals("项目锁库后") || transferReason.equals("否")) && uploadFile.exists()) {
                    //1.上传文件到ftp
                    if (tableId.equals("crf_handover")) {
                        SFTPFile.uploadFileToSFTP(uploadFile.getAbsolutePath(), "/Projects/CDTMS手册优化测试项目/1/", "clinical-ftp.hengrui.com", 22357, SASOnlieConstant.RTSM_API_USER, SASOnlieConstant.RTSM_API_PASS, uploadFile.getName());
                    } else if (tableId.equals("data_submission")) {
                        SFTPFile.uploadFileToSFTP(uploadFile.getAbsolutePath(), "/Projects/"+studyId+"/EDC/"+subFolder+"/", "clinical-ftp.hengrui.com", 22357, SASOnlieConstant.RTSM_API_USER, SASOnlieConstant.RTSM_API_PASS, uploadFile.getName());
                        JSONObject temps = new JSONObject();
                        JSONObject paramt = new JSONObject();
                        if (!data.isEmpty()) {
                            //5.put id into data
                            JSONObject formInfos = new JSONObject(data);
                            if (tableId.equals("data_submission") && tableId.equals("data_submission") && formInfos.get("data_version").toString().equals("Submitted") && formInfos.get("mode").toString().equals("sFTP")) {
                                temps.put("server", "FTP");
                                temps.put("path", "/Projects/"+studyId+"/EDC/"+subFolder+"/"+uploadFile.getName());
                            }
                        }


                        paramt.put("data", temps);
                        ECRFTransferServiceImpl.log.info("call dataSave params is :" + paramt.toString());
                        String newFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
                        paramt.put("formId", newFormId);
                        paramt.put("taskId", taskId);
                        paramt.put("projectId", projectId);
                        ECRFTransferServiceImpl.log.info("最新的formId是 :" + newFormId);
                        CDTMSAPI.dataSave(paramt);
                    }
                    ECRFTransferServiceImpl.log.info("---------------------------------------------执行了CRF文件上传到FTP的逻辑---------------------------------------------");
                }
            } else {
                ECRFTransferServiceImpl.log.info("---------------------------------------受试者CRF或受试者清单为空，不执行筛选逻辑！---------------------------------------");
            }
        }
        return null;
    }


}
