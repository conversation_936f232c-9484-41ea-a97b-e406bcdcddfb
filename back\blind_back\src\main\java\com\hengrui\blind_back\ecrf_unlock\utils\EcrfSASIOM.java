package com.hengrui.blind_back.ecrf_unlock.utils;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.*;

@Component
@Slf4j
public class EcrfSASIOM {
    public String readSAS(String codePath, String jsonPath,String studyId,String language) throws IOException {
        log.info("SAS运行传递的参数为：{},{},{},{}",codePath, jsonPath, studyId, language);
        // 参数校验
        String replaceStr1 = "&jsonPath.";
        String replaceStr2 = "&studyid.";
        String replaceStr3 = "&lang.";
        File file = new File(codePath);
        InputStreamReader in = new InputStreamReader(new FileInputStream(file), "UTF-8");
        BufferedReader bufIn = new BufferedReader(in);
        // 替换
        String line = "";
        StringBuilder stringBuffer = new StringBuilder();
        while ((line = bufIn.readLine()) != null) {
            if(ObjectUtil.isEmpty(language)){
                line = line.replaceAll(replaceStr1, jsonPath).replaceAll(replaceStr2, studyId).replaceAll("\uFEFF","").replaceAll("诺","");
            }else{
                line = line.replaceAll(replaceStr1, jsonPath).replaceAll(replaceStr2, studyId).replaceAll(replaceStr3, language).replaceAll("\uFEFF","").replaceAll("诺","");
            }

            stringBuffer.append(line);
        }
        EcrfSASIOM.log.info("final submit SAS code is:"+stringBuffer.toString());
        return stringBuffer.toString();
    }
}
