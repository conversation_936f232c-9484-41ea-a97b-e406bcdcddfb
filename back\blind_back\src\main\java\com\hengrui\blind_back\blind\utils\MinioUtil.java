package com.hengrui.blind_back.blind.utils;


import com.hengrui.blind_back.blind.config.MinioConfig;
import com.hengrui.blind_back.blind.constant.BlindConstant;
import com.hengrui.blind_back.blind.mapper.EDMCDTMSInfoMapper;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.utils.CallPython;
import com.hengrui.blind_back.utils.FileUtils;
import io.minio.*;
import io.minio.errors.MinioException;
import io.minio.messages.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.util.ObjectUtils;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;

@Slf4j
@Component
public class MinioUtil {

    private MinioClient minioClient;
    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    EDMCDTMSInfoMapper edmcdtmsInfoMapper;

    public MinioUtil() {


        // Configure the builder
        MinioClient.Builder builder = MinioClient.builder();
//        builder.endpoint("http://10.10.5.75:9000");
        builder.endpoint(SASOnlieConstant.MINIO_URL);
        builder.credentials("minioadmin", "minioadmin");

        // Init the builder
        this.minioClient = builder.build();

    }

    @SneakyThrows
    private boolean isBucketExist(String bucketName) {

        // Set the builder
        BucketExistsArgs.Builder builder = BucketExistsArgs.builder();
        builder.bucket(bucketName);

        BucketExistsArgs bucketExistsArgs = builder.build();

        boolean isExist = this.minioClient.bucketExists(bucketExistsArgs);

        if (isExist) {
            System.out.println("The bucket is exist! ");
            return true;
        } else {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
            System.out.println("Create bucket is success! ");
            return true;
        }
    }

    @SneakyThrows
    public String uploadObject(File file, String fileMd5, String studyId) {
        MinioUtil.log.info("---------------------------上传到minio接收到的参数为: file: "+file.getName()+"| fileMd5:"+fileMd5+" |studyId:"+studyId+"--------------------------------------------------" );
        //check the edcName if TAU then objectName is
        String edcName = edmcdtmsInfoMapper.getEDCName(studyId);
        MinioUtil.log.info("---------------------------查到的edcName是: "+edcName+"--------------------------------------------------" );
        String EDCName = "";
        String objectName = "";
        if (file.getName().contains(".json")) {
            objectName = "json/" + file.getName();
        } else if (file.getName().contains(".txt")) {
            objectName = "/" + "SHR-2004-102" + "/log/" + file.getName();
        } else if (file.getName().contains(".zip")) {
            if (edcName.equals("TAU")) {
                objectName = file.getName();
                EDCName = "HRTAU";
            } else if (edcName.equals("Rave")) {
                objectName = file.getName();
                EDCName = "RAVE";
            }

        }else if(file.getName().contains(".xlsx")|| file.getName().contains(".docx")||file.getName().contains(".csv")){
            objectName = "doc/" + file.getName();
        }
        MinioUtil.log.info("---------------------------上传到objectName 是: "+objectName+"--------------------------------------------------" );
        if (file.getName().contains(".zip")) {
            minioConfig.setBucketName("submit");
        }else if(file.getName().contains(".xlsx")||file.getName().contains("rtsmedc")||file.getName().contains(".csv")){
            minioConfig.setBucketName("sascomp");
        } else if(file.getName().contains(".docx")){
            minioConfig.setBucketName("protocolchart");
        }else if(file.getName().contains("rave_pass")){
            minioConfig.setBucketName("raw");
        }else if(file.getName().contains("Recist1.1")||file.getName().contains("AE-EX-EOT")||file.getName().contains("LAB-AE")||file.getName().contains("IRCDataTrans")){
            minioConfig.setBucketName("sdv");
        }else if(file.getName().contains("PPC")){
            minioConfig.setBucketName("protocolchart");
        }else {
            minioConfig.setBucketName("uat");
        }
        MinioUtil.log.info("---------------------------上传到bucketName 是: "+minioConfig.getBucketName()+"--------------------------------------------------" );
        if (studyId.equals("CDTMS手册优化测试项目")) {
            studyId = "SHR-2004-102";
        }
        //标签定义
        Map<String, String> map = new HashMap<>();
        if (!EDCName.equals("")) {
            map.put("key1", EDCName);
        }else{
            map.put("key1", "test");
        }



        map.put("key2", studyId);
        map.put("key3", ULIDGenerator.getMinioTagsCurrentTimes());
//        map.put("key3","noob");
        MinioUtil.log.info("tags是这样的:" + map.toString());
        // 1. 获取object的metadata
        if (isObjectExist(objectName, minioConfig)) {
            //存在同名文件
            StatObjectResponse stat = minioConfig.getMinioClient().statObject(
                    StatObjectArgs.builder().bucket(minioConfig.getBucketName()).object(objectName).build());
            //2.比较minio上的etag和本地文件的md5是否一致
            MinioUtil.log.info("本地文件的MD5码为:" + fileMd5 + "\n" + "minio上的MD5码为:" + stat.etag());
            //判断是否为大文件
            Boolean isBigFile = false;
            String bigFileMd5 = "";
            if (stat.etag().split("-").length > 1) {
                //是大文件
                isBigFile = true;
                //判断是否存在相同文件
                if (ObjectUtils.isEmpty(bigFileMd5)) {
                    //新增该条记录

                }
            }
            //非大文件直接比较md5
            if (!stat.etag().equals(fileMd5) && !isBigFile) {
                PutObjectArgs args = PutObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .stream(new FileInputStream(file), new FileInputStream(file).available(), -1)
                        .contentType("application/json")
                        .build();
                minioConfig.getMinioClient().putObject(args);
                //加标签
                SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .tags(map)
                        .build();
                minioConfig.getMinioClient().setObjectTags(setObjectTagsArgs);
                MinioUtil.log.info("文件：" + file.getName() + "已经上传至minio");
            } else if (!stat.etag().equals(fileMd5) && isBigFile && ObjectUtils.isEmpty(bigFileMd5)) {
                //MD5码不同，且是大文件，且没有记录过MD5码
                PutObjectArgs args = PutObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .stream(new FileInputStream(file), new FileInputStream(file).available(), -1)
                        .contentType("application/json")
                        .build();
                minioConfig.getMinioClient().putObject(args);
                //加标签
                SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .tags(map)
                        .build();
                minioConfig.getMinioClient().setObjectTags(setObjectTagsArgs);
                MinioUtil.log.info("大文件：" + file.getName() + "已经上传至minio");
            } else {
                MinioUtil.log.info("文件：" + file.getName() + "在minio上已经存在相同文件！");
            }

        } else {
            MinioUtil.log.info("文件：" + file.getName() + "在minio上没有相同文件，直接上传");
            //直接上传
            PutObjectArgs args = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .stream(new FileInputStream(file), new FileInputStream(file).available(), -1)
                    .contentType("application/json")
                    .build();
            minioConfig.getMinioClient().putObject(args);

            //加标签
            SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .tags(map)
                    .build();
            minioConfig.getMinioClient().setObjectTags(setObjectTagsArgs);

            MinioUtil.log.info("文件：" + file.getName() + "已经上传至minio");
        }
        return minioConfig.getUrl() + "/" + minioConfig.getBucketName() + "/" + file.getName();
    }

    @SneakyThrows
    public boolean removeObject() {
        String bucketName = "mybucket";
        String filename = "my_data1.png";
        RemoveObjectArgs.Builder builder = RemoveObjectArgs.builder();
        builder.bucket(bucketName);
        builder.object(filename);
        RemoveObjectArgs removeObjectArgs = builder.build();
        this.minioClient.removeObject(removeObjectArgs);
        return true;
    }

    @SneakyThrows
    public boolean downloadObject(String objectName, String filename) {
        DownloadObjectArgs.Builder builder = DownloadObjectArgs.builder();
        builder.bucket("externalfile");
        builder.object(objectName);
        builder.filename(filename);
        DownloadObjectArgs downloadObjectArgs = builder.build();
        this.minioClient.downloadObject(downloadObjectArgs);
        return true;
    }

    @SneakyThrows
    public boolean isExist(String objectName,String bucketName){
        minioConfig.setBucketName(bucketName);
        return  isObjectExist(objectName, minioConfig);
    }


    @SneakyThrows
    public boolean downloadGetObject(String bucketName, String filename) {
        if (!this.isBucketExist(bucketName)) {
            return false;
        }
        GetObjectArgs.Builder builder = GetObjectArgs.builder();
        builder.bucket(bucketName);
        builder.object(filename);
        GetObjectArgs objectArgs = builder.build();
        try{
            GetObjectResponse response = minioClient.getObject(objectArgs);
            // Read the byte stream of data
            byte[] buff = new byte[1024];
            FastByteArrayOutputStream fbaos = new FastByteArrayOutputStream();
            int len = 0;
            while ((len = response.read(buff)) != -1) {
                fbaos.write(buff, 0, len);
            }
            fbaos.flush();

            // Create the file
            if(filename.contains("/")){
                if(filename.split("/").length==2){
                    filename=filename.split("/")[1];
                }else if(filename.split("/").length==3){
                    filename=filename.split("/")[2];
                }

                MinioUtil.log.info("---------------after split the fileName is " + filename+"-----------------------------------");
            }
            File file = new File(SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + filename);
            if (!file.exists()) {
                file.createNewFile();
            }
            // Write the file
            OutputStream outputStream = new FileOutputStream(file);
            outputStream.write(fbaos.toByteArray());
            outputStream.flush();
            fbaos.close();
            MinioUtil.log.info(filename + "download success and filePath is:" + file.getAbsolutePath());
            return true;

        }
        catch(io.minio.errors.ErrorResponseException e){
            MinioUtil.log.info("-------------------------minio上没查到"+objectArgs.toString()+"对应的文件!!!");
            return false;
        }



    }


    @SneakyThrows
    public boolean downloadObjectWithPath(String bucketName, String filename,String filePath) {
        if (!this.isBucketExist(bucketName)) {
            return false;
        }
        GetObjectArgs.Builder builder = GetObjectArgs.builder();
        builder.bucket(bucketName);
        builder.object(filename);
        GetObjectArgs objectArgs = builder.build();
        GetObjectResponse response = minioClient.getObject(objectArgs);

        // Read the byte stream of data
        byte[] buff = new byte[1024];
        FastByteArrayOutputStream fbaos = new FastByteArrayOutputStream();
        int len = 0;
        while ((len = response.read(buff)) != -1) {
            fbaos.write(buff, 0, len);
        }
        fbaos.flush();

        // Create the file
        if(filename.contains("/")){
            filename=filename.split("/")[1];
            MinioUtil.log.info("---------------after split the fileName is " + filename+"-----------------------------------");
        }
        File file = new File(filePath + System.getProperty("file.separator") + filename);
        if (!file.exists()) {
            file.createNewFile();
        }
        // Write the file
        OutputStream outputStream = new FileOutputStream(file);
        outputStream.write(fbaos.toByteArray());
        outputStream.flush();
        fbaos.close();
        MinioUtil.log.info(filename + "download success and filePath is:" + file.getAbsolutePath());
        return true;
    }

    @SneakyThrows
    public void deletMultiObject() {
        List<DeleteObject> objects = new LinkedList<>();
        objects.add(new DeleteObject("my_data.png"));
        objects.add(new DeleteObject("my_data1.png"));

        RemoveObjectsArgs.Builder builder = RemoveObjectsArgs.builder();
        builder.bucket("mybucket");
        builder.objects(objects);
        RemoveObjectsArgs removeObjectsArgs = builder.build();

        Iterable<Result<DeleteError>> results = this.minioClient.removeObjects(removeObjectsArgs);
        for (Result<DeleteError> result : results) {
            DeleteError error = result.get();
            System.out.println("Error in deleting object " + error.objectName() + "; " + error.message());
        }
    }

    public boolean isObjectExist(String objectName, MinioConfig minioConfig) {
        boolean exist = true;
        try {
            StatObjectResponse stat = minioConfig.getMinioClient().statObject(
                    StatObjectArgs.builder().bucket(minioConfig.getBucketName()).object(objectName).build());
        } catch (Exception e) {
            exist = false;
        }
        return exist;
    }


    @SneakyThrows
    public boolean downloadECRFObject(String objectName, String filename) {
        DownloadObjectArgs.Builder builder = DownloadObjectArgs.builder();
        builder.bucket("dmreview");
        builder.object(objectName);
        builder.filename(filename);
        DownloadObjectArgs downloadObjectArgs = builder.build();
        this.minioClient.downloadObject(downloadObjectArgs);
        return true;
    }


    @SneakyThrows
    public VersioningConfiguration getVersion(String bucketName) {
        VersioningConfiguration config =
                minioClient.getBucketVersioning(
                        GetBucketVersioningArgs.builder().bucket(bucketName).build());
        return config;
    }


    @SneakyThrows
    public Iterable<Result<Item>> listObjects() {
        Iterable<Result<Item>> results = minioClient.listObjects(
                ListObjectsArgs.builder()
                        .bucket("raw")
                        .includeVersions(true)
                        .build());
        return results;
    }


    @SneakyThrows
    public String downloadFileWithTags(String bucketName, String fileName, Path downloadPath, String cutDate) {
        String filePrefix = fileName.split("-")[0];
        try {
            MinioClient minioClient = MinioClient.builder()
                    .endpoint(SASOnlieConstant.MINIO_URL)
                    .credentials("minioadmin", "minioadmin")
                    .build();
            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket("raw")
                            .prefix(fileName)
                            .maxKeys(1000)
                            .includeVersions(true)
                            .build());


            for (Result<Item> result : results) {
                Item item = result.get();
                String lastModifyTime = result.get().lastModified().toString().split("T")[0];
                String versionId = result.get().versionId();
                if (item.objectName().equals(fileName)) {
                    MinioUtil.log.info("find this file !!!");
                }
                // Check if the item matches the filename and tags.
                if (item.objectName().equals(fileName) && cutDate.equals(lastModifyTime)) {
                    // Download the object.
                    try (
                            InputStream stream = minioClient.getObject(
                                    GetObjectArgs.builder()
                                            .bucket(bucketName)
                                            .object(fileName)
                                            .versionId(versionId)
                                            .build());
                    ) {
                        Files.copy(stream, downloadPath.resolve(item.objectName()), StandardCopyOption.REPLACE_EXISTING);
                        MinioUtil.log.info("Downloaded " + item.objectName() + " to " + downloadPath);
                        return downloadPath.resolve(item.objectName()).toString();
                    }
                }
            }
        } catch (MinioException e) {
            MinioUtil.log.info("Error occurred: " + e);
            MinioUtil.log.info("HTTP trace: " + e.httpTrace());
        } catch (Exception e) {
            MinioUtil.log.info("Error occurred: " + e.getMessage());
        }
        return null;
    }


    @SneakyThrows
    public void setObjectTags(String bucketName, String objectName, Map<String, String> tagsMap) {
        try {
            // Check if the bucket exists
            if (!this.isBucketExist(bucketName)) {
                log.warn("Bucket " + bucketName + " does not exist.");
                return;
            }

            // Convert the Map<String, String> to Tags
            Tags tags = Tags.newObjectTags(tagsMap);

            // Build the SetObjectTagsArgs
            SetObjectTagsArgs args = SetObjectTagsArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .tags(tags)
                    .build();

            // Set the tags for the object
            minioClient.setObjectTags(args);

            log.info("Tags successfully set for object " + objectName + " in bucket " + bucketName);
        } catch (MinioException e) {
            log.error("Error setting tags for object " + objectName + " in bucket " + bucketName, e);
        }
    }



    @SneakyThrows
    public Map<String, String> getObjectTags(String bucketName, String objectName) {
        try {
            // Check if the bucket exists
            if (!this.isBucketExist(bucketName)) {
                MinioUtil.log.warn("Bucket " + bucketName + " does not exist.");
                return null;
            }

            // Build the GetObjectTagsArgs
            GetObjectTagsArgs args = GetObjectTagsArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build();

            // Get the tags
            Tags tags = minioClient.getObjectTags(args);

            // Convert Tags to a Map<String, String>
            return tags.get();
        } catch (Exception e) {
            MinioUtil.log.error("Error getting tags for object " + objectName + " in bucket " + bucketName, e);
            return null;
        }
    }




    @SneakyThrows
    public String uploadObjectWithTag(File file, String fileMd5, String studyId,String tag,String bucket,String folder) {
        String env = edmcdtmsInfoMapper.getEDCName(studyId);
        if(!ObjectUtils.isEmpty(env)&&!tag.isEmpty()){
            if (env.equals("TAU")) {
                env= "HRTAU";
            } else if (env.equals("Rave")) {
                env= "RAVE";
            }
        }


        MinioUtil.log.info("---------------------------上传到minio接收到的参数为: file: "+file.getName()+"| fileMd5:"+fileMd5+" |studyId:"+studyId+"--------------------------------------------------" );
        //check the edcName if TAU then objectName is
        String edcName = edmcdtmsInfoMapper.getEDCName(studyId);
        MinioUtil.log.info("---------------------------查到的edcName是: "+edcName+"--------------------------------------------------" );
        String objectName = "";


        minioConfig.setBucketName(bucket);
        MinioUtil.log.info("---------------------------上传到bucketName 是: "+minioConfig.getBucketName()+"--------------------------------------------------" );
        //标签定义
        Map<String, String> map = new HashMap<>();
        if(bucket.equals("datamanagement")){
            map.put("key1", tag);
            map.put("key2", studyId);
            map.put("key3", ULIDGenerator.getMinioTagsCurrentTimes());
            objectName=folder+"/"+studyId+"_PRO_进展报告.xlsx";
            MinioUtil.log.info("---------------------------上传到objectName 是: "+objectName+"--------------------------------------------------" );
        }else if(bucket.equals("raw")&&file.getName().contains("xlsx")){
            map.put("env", tag);
            map.put("key1", env);
            map.put("key2", studyId);
            map.put("key3", ULIDGenerator.getMinioTagsCurrentTimes());
            objectName=folder+"/"+studyId+"_PRO_进展报告.xlsx";
            MinioUtil.log.info("---------------------------上传到objectName 是: "+objectName+"--------------------------------------------------" );
        }else{
            map.put("env", tag);
            map.put("key1", env);
            map.put("key2", studyId);
            map.put("key3", ULIDGenerator.getMinioTagsCurrentTimes());
            objectName=folder+"/"+file.getName();
            MinioUtil.log.info("---------------------------上传到objectName 是: "+objectName+"--------------------------------------------------" );
        }

        MinioUtil.log.info("tags是这样的:" + map.toString());
        // 1. 获取object的metadata
        if (isObjectExist(objectName, minioConfig)) {
            //存在同名文件
            StatObjectResponse stat = minioConfig.getMinioClient().statObject(
                    StatObjectArgs.builder().bucket(minioConfig.getBucketName()).object(objectName).build());
            //2.比较minio上的etag和本地文件的md5是否一致
            MinioUtil.log.info("本地文件的MD5码为:" + fileMd5 + "\n" + "minio上的MD5码为:" + stat.etag());
            //判断是否为大文件
            Boolean isBigFile = false;
            String bigFileMd5 = "";
            if (stat.etag().split("-").length > 1) {
                //是大文件
                isBigFile = true;
                //判断是否存在相同文件
                if (ObjectUtils.isEmpty(bigFileMd5)) {
                    //新增该条记录

                }
            }
            //非大文件直接比较md5
            if (!stat.etag().equals(fileMd5) && !isBigFile) {
                PutObjectArgs args = PutObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .stream(new FileInputStream(file), new FileInputStream(file).available(), -1)
                        .contentType("application/json")
                        .build();
                minioConfig.getMinioClient().putObject(args);
                //加标签
                SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .tags(map)
                        .build();
                minioConfig.getMinioClient().setObjectTags(setObjectTagsArgs);
                MinioUtil.log.info("文件：" + file.getName() + "已经上传至minio");
            } else if (!stat.etag().equals(fileMd5) && isBigFile && ObjectUtils.isEmpty(bigFileMd5)) {
                //MD5码不同，且是大文件，且没有记录过MD5码
                PutObjectArgs args = PutObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .stream(new FileInputStream(file), new FileInputStream(file).available(), -1)
                        .contentType("application/json")
                        .build();
                minioConfig.getMinioClient().putObject(args);
                //加标签
                SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .tags(map)
                        .build();
                minioConfig.getMinioClient().setObjectTags(setObjectTagsArgs);
                MinioUtil.log.info("大文件：" + file.getName() + "已经上传至minio");
            } else {
                MinioUtil.log.info("文件：" + file.getName() + "在minio上已经存在相同文件！");
            }

        } else {
            MinioUtil.log.info("文件：" + file.getName() + "在minio上没有相同文件，直接上传");
            //直接上传
            PutObjectArgs args = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .stream(new FileInputStream(file), new FileInputStream(file).available(), -1)
                    .contentType("application/json")
                    .build();
            minioConfig.getMinioClient().putObject(args);

            //加标签
            SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .tags(map)
                    .build();
            minioConfig.getMinioClient().setObjectTags(setObjectTagsArgs);

            MinioUtil.log.info("文件：" + file.getName() + "已经上传至minio");
        }
        return minioConfig.getUrl() + "/" + minioConfig.getBucketName() + "/" + file.getName();
    }





    @SneakyThrows
    public String uploadRaveDataSet(File file, String fileMd5, String studyId,String tag,String bucket) {
        MinioUtil.log.info("---------------------------上传到minio接收到的参数为: file: "+file.getName()+"| fileMd5:"+fileMd5+" |studyId:"+studyId+"--------------------------------------------------" );

        String objectName = "";


        minioConfig.setBucketName(bucket);
        MinioUtil.log.info("---------------------------上传到bucketName 是: "+minioConfig.getBucketName()+"--------------------------------------------------" );
        //标签定义
        Map<String, String> map = new HashMap<>();
            map.put("env", tag);
            map.put("key1", "RAVE");
            map.put("key2", studyId);
            map.put("key3", ULIDGenerator.getMinioTagsCurrentTimes());
            objectName=studyId+"_sas.zip";
            MinioUtil.log.info("---------------------------上传到objectName 是: "+objectName+"--------------------------------------------------" );


        MinioUtil.log.info("tags是这样的:" + map.toString());
        // 1. 获取object的metadata
        if (isObjectExist(objectName, minioConfig)) {
            //存在同名文件
            StatObjectResponse stat = minioConfig.getMinioClient().statObject(
                    StatObjectArgs.builder().bucket(minioConfig.getBucketName()).object(objectName).build());
            //2.比较minio上的etag和本地文件的md5是否一致
            MinioUtil.log.info("本地文件的MD5码为:" + fileMd5 + "\n" + "minio上的MD5码为:" + stat.etag());
            //判断是否为大文件
            Boolean isBigFile = false;
            String bigFileMd5 = "";
            if (stat.etag().split("-").length > 1) {
                //是大文件
                isBigFile = true;
                //判断是否存在相同文件
                if (ObjectUtils.isEmpty(bigFileMd5)) {
                    //新增该条记录

                }
            }
            //非大文件直接比较md5
            if (!stat.etag().equals(fileMd5) && !isBigFile) {
                PutObjectArgs args = PutObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .stream(new FileInputStream(file), new FileInputStream(file).available(), -1)
                        .contentType("application/json")
                        .build();
                minioConfig.getMinioClient().putObject(args);
                //加标签
                SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .tags(map)
                        .build();
                minioConfig.getMinioClient().setObjectTags(setObjectTagsArgs);
                MinioUtil.log.info("文件：" + file.getName() + "已经上传至minio");
            } else if (!stat.etag().equals(fileMd5) && isBigFile && ObjectUtils.isEmpty(bigFileMd5)) {
                //MD5码不同，且是大文件，且没有记录过MD5码
                PutObjectArgs args = PutObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .stream(new FileInputStream(file), new FileInputStream(file).available(), -1)
                        .contentType("application/json")
                        .build();
                minioConfig.getMinioClient().putObject(args);
                //加标签
                SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .tags(map)
                        .build();
                minioConfig.getMinioClient().setObjectTags(setObjectTagsArgs);
                MinioUtil.log.info("大文件：" + file.getName() + "已经上传至minio");
            } else {
                MinioUtil.log.info("文件：" + file.getName() + "在minio上已经存在相同文件！");
            }

        } else {
            MinioUtil.log.info("文件：" + file.getName() + "在minio上没有相同文件，直接上传");
            //直接上传
            PutObjectArgs args = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .stream(new FileInputStream(file), new FileInputStream(file).available(), -1)
                    .contentType("application/json")
                    .build();
            minioConfig.getMinioClient().putObject(args);

            //加标签
            SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .tags(map)
                    .build();
            minioConfig.getMinioClient().setObjectTags(setObjectTagsArgs);

            MinioUtil.log.info("文件：" + file.getName() + "已经上传至minio");
        }
        return minioConfig.getUrl() + "/" + minioConfig.getBucketName() + "/" + file.getName();
    }



    @SneakyThrows
    public String uploadNormalFile(File file, String fileMd5, String studyId,String tag,String bucket,String newName) {
        MinioUtil.log.info("---------------------------上传到minio接收到的参数为: file: "+file.getName()+"| fileMd5:"+fileMd5+" |studyId:"+studyId+"--------------------------------------------------" );

        String objectName = "";


        minioConfig.setBucketName(bucket);
        MinioUtil.log.info("---------------------------上传到bucketName 是: "+minioConfig.getBucketName()+"--------------------------------------------------" );
        //标签定义
        Map<String, String> map = new HashMap<>();
        map.put("key1", tag);
        map.put("key2", studyId);
        map.put("key3", ULIDGenerator.getMinioTagsCurrentTimes());
        objectName=newName;
        MinioUtil.log.info("---------------------------上传到objectName 是: "+objectName+"--------------------------------------------------" );


        MinioUtil.log.info("tags是这样的:" + map.toString());
        // 1. 获取object的metadata
        if (isObjectExist(objectName, minioConfig)) {
            //存在同名文件
            StatObjectResponse stat = minioConfig.getMinioClient().statObject(
                    StatObjectArgs.builder().bucket(minioConfig.getBucketName()).object(objectName).build());
            //2.比较minio上的etag和本地文件的md5是否一致
            MinioUtil.log.info("本地文件的MD5码为:" + fileMd5 + "\n" + "minio上的MD5码为:" + stat.etag());
            //判断是否为大文件
            Boolean isBigFile = false;
            String bigFileMd5 = "";
            if (stat.etag().split("-").length > 1) {
                //是大文件
                isBigFile = true;
                //判断是否存在相同文件
                if (ObjectUtils.isEmpty(bigFileMd5)) {
                    //新增该条记录

                }
            }
            //非大文件直接比较md5
            if (!stat.etag().equals(fileMd5) && !isBigFile) {
                PutObjectArgs args = PutObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .stream(new FileInputStream(file), new FileInputStream(file).available(), -1)
                        .contentType("application/json")
                        .build();
                minioConfig.getMinioClient().putObject(args);
                //加标签
                SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .tags(map)
                        .build();
                minioConfig.getMinioClient().setObjectTags(setObjectTagsArgs);
                MinioUtil.log.info("文件：" + file.getName() + "已经上传至minio");
            } else if (!stat.etag().equals(fileMd5) && isBigFile && ObjectUtils.isEmpty(bigFileMd5)) {
                //MD5码不同，且是大文件，且没有记录过MD5码
                PutObjectArgs args = PutObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .stream(new FileInputStream(file), new FileInputStream(file).available(), -1)
                        .contentType("application/json")
                        .build();
                minioConfig.getMinioClient().putObject(args);
                //加标签
                SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .tags(map)
                        .build();
                minioConfig.getMinioClient().setObjectTags(setObjectTagsArgs);
                MinioUtil.log.info("大文件：" + file.getName() + "已经上传至minio");
            } else {
                MinioUtil.log.info("文件：" + file.getName() + "在minio上已经存在相同文件！");
            }

        } else {
            MinioUtil.log.info("文件：" + file.getName() + "在minio上没有相同文件，直接上传");
            //直接上传
            PutObjectArgs args = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .stream(new FileInputStream(file), new FileInputStream(file).available(), -1)
                    .contentType("application/json")
                    .build();
            minioConfig.getMinioClient().putObject(args);

            //加标签
            SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .tags(map)
                    .build();
            minioConfig.getMinioClient().setObjectTags(setObjectTagsArgs);

            MinioUtil.log.info("文件：" + file.getName() + "已经上传至minio");
        }
        return minioConfig.getUrl() + "/" + minioConfig.getBucketName() + "/" + file.getName();
    }


    @Autowired
    CallPython callPython;
    @Autowired
    Decode64Util decode64Util;

    public String uploadALSUATFile(Map<String, String> ENVInfo, String studyId, String recordId, String formId, String tableId){
        //获取als文件，上传到minio doc目录下
        String uid = ULIDGenerator.generateULID();
        ENVInfo.put("uuid", uid);
        ENVInfo.put("ENV", "UAT");
        ENVInfo.put("data_format", "Excel");
        ENVInfo.put("data_type", "DB_Definition");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", recordId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", tableId);
        ENVInfo.put("fileSuffix", "a".toString());
        //4.1.upload the latest report and second latest report to cdtms API
        //4.2 upload second latest report to cdtms
        ENVInfo.put("isLatest", "");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        List<Map<String, String>> alsfilesFromEDC = new ArrayList<>();
        Map<String, String> alsResult = callPython.downloadEDCServerFile(ENVInfo, alsfilesFromEDC);
        String DBDName = alsResult.get("original_name");
        if(!DBDName.isEmpty()){
            String  localDBD = SASOnlieConstant.EDC_DATA_LOCAL_FOLDER + BlindConstant.FILE_SEPARATOR + ENVInfo.get("studyId") + "_" + ENVInfo.get("ENV") + "_" + ENVInfo.get("uuid") + "_" + ENVInfo.get("fileSuffix") + ".xlsx";
            //上传到minio
            String localDBDName = studyId + "_als.xlsx";
            File oldFile = new File(localDBD);
            Path newPath = Paths.get(oldFile.getParent(), localDBDName);
            try {
                // 检查目标路径是否存在文件，如果存在则删除
                if (Files.exists(newPath)) {
                    Files.delete(newPath);
                }
                // 重命名文件
                Path newFilePath = Files.move(oldFile.toPath(), newPath);
                File newFile = newFilePath.toFile();

                // 继续处理新文件
                String md5 = decode64Util.getMd5(newFile);
                //上传到minio doc 目录下
                uploadObjectWithTag(newFile, md5, studyId, "uat", "doc", "");
            } catch (IOException e) {
                throw new RuntimeException("Failed to rename file: " + e.getMessage(), e);
            }
            return "success";
        }else{
            return "fail";
        }
    }



    public void uploadALSPROFile(Map<String, String> ENVInfo, String studyId, String recordId, String formId, String tableId){
        //获取als文件，上传到minio doc目录下
        String uid = ULIDGenerator.generateULID();
        ENVInfo.put("uuid", uid);
        ENVInfo.put("ENV", "PRO");
        ENVInfo.put("data_format", "Excel");
        ENVInfo.put("data_type", "DB_Definition");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", recordId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", tableId);
        ENVInfo.put("fileSuffix", "a".toString());
        //4.1.upload the latest report and second latest report to cdtms API
        //4.2 upload second latest report to cdtms
        ENVInfo.put("isLatest", "");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        List<Map<String, String>> alsfilesFromEDC = new ArrayList<>();
        Map<String, String> alsResult = callPython.downloadEDCServerFile(ENVInfo, alsfilesFromEDC);
        String DBDName = alsResult.get("original_name");
        if(!DBDName.isEmpty()){
            String  localDBD = SASOnlieConstant.EDC_DATA_LOCAL_FOLDER + BlindConstant.FILE_SEPARATOR + ENVInfo.get("studyId") + "_" + ENVInfo.get("ENV") + "_" + ENVInfo.get("uuid") + "_" + ENVInfo.get("fileSuffix") + ".xlsx";
            //上传到minio
            String localDBDName = studyId + "_als.xlsx";
            File oldFile = new File(localDBD);
            Path newPath = Paths.get(oldFile.getParent(), localDBDName);
            try {
                // 检查目标路径是否存在文件，如果存在则删除
                if (Files.exists(newPath)) {
                    Files.delete(newPath);
                }
                // 重命名文件
                Path newFilePath = Files.move(oldFile.toPath(), newPath);
                File newFile = newFilePath.toFile();

                // 继续处理新文件
                String md5 = decode64Util.getMd5(newFile);
                //上传到minio doc 目录下
                uploadObjectWithTag(newFile, md5, studyId, "pro", "doc", "");
            } catch (IOException e) {
                throw new RuntimeException("Failed to rename file: " + e.getMessage(), e);
            }
        }
    }



}
