package com.hengrui.blind_back.database_change_report.service.impl;

import com.alibaba.fastjson.JSON;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.database_change_report.service.DBChangeReportService;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.hengrui.blind_back.utils.CallPython;
import com.hengrui.blind_back.utils.SubmitSAS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName DBChangeReportServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/27 17:20
 * @Version 1.0
 **/
@Service
@Slf4j
public class DBChangeReportServiceImpl implements DBChangeReportService {
    @Autowired
    private CallPython callPython;

    @Autowired
    private SubmitSAS submitSAS;

    @Override
    public Map<String, String> submitToDBChangeReportSAS(String taskId, String projectId) {
        //1.call python get the latest report and second latest report

        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId="";
        String tableId="";
        if(!ObjectUtils.isEmpty(formInfo.get("recordId"))){
            recordId= formInfo.get("recordId");
            tableId=formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id",recordId);
            object.put("formData",formData);
            projectId=object.toJSONString();
        }else{
            tableId=projectId;
            recordId=taskId;
        }
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        DBChangeReportServiceImpl.log.info("------------获取到的formId是：" + formId + "------------");
        DBChangeReportServiceImpl.log.info("------------获取到的studyId是：" + studyId + "------------");


        List<Map<String, String>> filesFromEDC = new ArrayList<>();
        Map<String, String> fileObjectA = new HashMap<>();
        fileObjectA.put("fid", "priordataset");
        fileObjectA.put("fileType", ".zip");
        Map<String, String> fileObjectB = new HashMap<>();
        fileObjectB.put("fid", "postamend");
        fileObjectB.put("fileType", ".zip");
        filesFromEDC.add(fileObjectA);



        String uuid = ULIDGenerator.generateULID();
        Map<String, String> ENVInfo = new HashMap<>();
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("ENV", "PRO");
        ENVInfo.put("data_format", "SAS");
        ENVInfo.put("data_type", "data_set");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", recordId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", tableId);
        ENVInfo.put("fileSuffix", "a".toString());
        //2.upload the latest report and second latest report to cdtms API
        //2.1 upload second latest report to cdtms
        ENVInfo.put("isLatest", "YES".toString());
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        callPython.downloadEDCServerFile(ENVInfo, filesFromEDC);
        //2.2 upload latest report to cdtms
        List<Map<String, String>> filesFromLatest = new ArrayList<>();
        filesFromLatest.add(fileObjectB);
        ENVInfo.put("isLatest", "NO".toString());
        formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        ENVInfo.put("formId", formId);
        callPython.downloadEDCServerFile(ENVInfo, filesFromLatest);

        //3.call python to move file from minio/raw to  minio/compsas/data/sas7bat
        callPython.executeFIleTransfer(studyId);
        //4.call sas program
        formInfo.put("uuid",uuid);
        String paramFileName = formInfo.get("studyId").toString() + "_UAT_" + uuid + ".json";
        //4.1 submit to sas via json params and define the json path on the minio storage platform
        formInfo.put("jsonMinioPath",   SASOnlieConstant.PREFIX_PRO_MINIO+"/uat/json/" + paramFileName);
        formInfo.put("sasCodePath", SASOnlieConstant.RTSM_SAS_COMP_CODE_UAT_PATH);
        formInfo.put("paramFileName", paramFileName);

        //5. define the output report name on minio
        String outputName = "output/"+formInfo.get("studyId").toString() + SASOnlieConstant.COMP_SUFFIX;
        List<Map<String,String>> sasOutputFilesInfo = new ArrayList<>();
        formInfo.put("outputName", outputName);
        formInfo.put("bucket", "sascomp");


        //6.upload the  sas output report to cdtms API
        Map<String,String> sasOutputFile=new HashMap<>();
        sasOutputFile.put("fid","compreport");
        sasOutputFile.put("outputName",outputName);
        sasOutputFilesInfo.add(sasOutputFile);
        //7. call sas program
        formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        ENVInfo.put("formId", formId);
        return submitSAS.submitToSAS( ENVInfo, filesFromEDC,formInfo,sasOutputFilesInfo);
    }
}
