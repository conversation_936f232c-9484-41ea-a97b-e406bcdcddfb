package com.hengrui.blind_back.config;

import com.hengrui.blind_back.blind.config.PermissionInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.util.UrlPathHelper;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

@Slf4j
@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Resource
    private PermissionInterceptor permissionInterceptor;

    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        UrlPathHelper urlPathHelper = new UrlPathHelper();
        urlPathHelper.setUrlDecode(true);
        urlPathHelper.setDefaultEncoding(StandardCharsets.UTF_8.name());
        configurer.setUrlPathHelper(urlPathHelper);
    }


    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        //文件磁盘图片url 映射
        //配置server虚拟路径，handler为前台访问的目录，locations为files相对应的本地路径,本处映射的地址方便操作记录预览文件使用
        registry.addResourceHandler("/files/**").addResourceLocations("file:/home/<USER>/8087/onlyOfficeFile/");
        registry.addResourceHandler("/RTSMSignedFiles/**").addResourceLocations("file:/home/<USER>/8087/rtsm_file/");
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")  // Allow all origins, or specify domains like "https://example.com"
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .maxAge(3600); // Cache preflight request for 1 hour
    }
}
