package com.hengrui.blind_back.ecrf_unlock.utils;

import com.hengrui.blind_back.blind.constant.BlindConstant;
import com.hengrui.blind_back.blind.utils.Decode64Util;
import com.hengrui.blind_back.blind.utils.MinioUtil;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.sas.iom.SAS.ILanguageService;
import com.sas.iom.SAS.ILanguageServicePackage.CarriageControlSeqHolder;
import com.sas.iom.SAS.ILanguageServicePackage.LineTypeSeqHolder;
import com.sas.iom.SAS.IWorkspace;
import com.sas.iom.SAS.IWorkspaceHelper;
import com.sas.iom.SASIOMDefs.GenericError;
import com.sas.iom.SASIOMDefs.StringSeqHolder;
import com.sas.services.connection.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class EcrfSASTrigger {

    @Autowired
    EcrfSASIOM sasiom;
    @Autowired
    MinioUtil minioUtil;

    @Autowired
    FileUtil fileUtil;
    @Autowired
    Decode64Util decode64Util;

    public Map<String, Object> runCreateSAS(String codePath,Map<String, String> formInfo ) {
        String studyId=formInfo.get("studyId");
        String jsonPath=formInfo.get("jsonMinioPath");
        String language=formInfo.get("language");
        EcrfSASTrigger.log.info("调用SAS的参数为：codePath: " + codePath + "| studyId: " + studyId + "| jsonPath: " + jsonPath+"| language: " + language);
        //set a counter for ppl who want to know the total status of SAS process
        int errorAndWarningCount = 0;
        String SASLogSuffix = ULIDGenerator.getMinioCurrentTimes();


        String logPath = "";
        Boolean isSuccess = false;
        Map<String, Object> result = new HashMap<>();
        String classID = Server.CLSID_SAS;
//        String host = "sas-hrsh-node1.hengrui.com";
        String host = "sas-hrsh.hengrui.com";
        int port = 8591;
        Server server = new BridgeServer(classID, host, port);
        // make a connection factory configuration with the server
        ConnectionFactoryConfiguration cxfConfig = new ManualConnectionFactoryConfiguration(server);
        // get a connection factory manager
        ConnectionFactoryManager cxfManager = new ConnectionFactoryManager();
        // get a connection factory that matches the configuration
        ConnectionFactoryInterface cxf = null;
        try {

            cxf = cxfManager.getFactory(cxfConfig);

        // get the administrator interface

            ConnectionFactoryAdminInterface admin = cxf.getAdminInterface();

        // get a connection
        String userName = SASOnlieConstant.SAS_USER;
        String password = SASOnlieConstant.SAS_PASS;
        String sasLogPath = "";


            EcrfSASTrigger.log.info("-------------------------建立sas连接前 -----------------------------------------------------");
            EcrfSASTrigger.log.info("-------------------------账号是:{},密码是:{} -----------------------------------------------------", userName, password);
            ConnectionInterface cx = cxf.getConnection(userName, password);
            log.info("连接的内容是:"+cx.toString());
            EcrfSASTrigger.log.info("-------------------------建立sas连接后 -----------------------------------------------------");
            try {
                // Narrow the connection from the server.
                org.omg.CORBA.Object obj = cx.getObject();
                IWorkspace iWorkspace = IWorkspaceHelper.narrow(obj);
                //insert iWorkspace workspace usage code here
                ILanguageService iLanguageService = iWorkspace.LanguageService();
                //替换参数为ecrf所需的参数
                iLanguageService.Submit(sasiom.readSAS(codePath, jsonPath, studyId,language));
                //sas log 获取
                CarriageControlSeqHolder logCarriageControlHldr = new CarriageControlSeqHolder();
                LineTypeSeqHolder logLineTypeHldr = new LineTypeSeqHolder();
                StringSeqHolder logHldr = new StringSeqHolder();
                iLanguageService.FlushLogLines(Integer.MAX_VALUE, logCarriageControlHldr, logLineTypeHldr, logHldr);
                //创建文件存放的地址
                File logFolder = new File(BlindConstant.SAS_BLIND_LOG_PATH);
                if (logFolder.isDirectory() || !logFolder.exists()) {
                    logFolder.mkdirs();
                }

                logPath = logFolder.getAbsolutePath() + BlindConstant.FILE_SEPARATOR + studyId + "_" + SASLogSuffix + "_SAS_log.txt";
                EcrfSASTrigger.log.info("-------------------------sas日志的存放位置是： " + logPath);
                String[] logLines = logHldr.value;
                try (BufferedWriter writer = new BufferedWriter(new FileWriter(logPath, "UTF-8".isEmpty()))) {
                 //   EcrfSASTrigger.log.info("-------------------------sas日志内容如下： ---------------------------------------------------");
                    for (String line : logLines) {

                        //解析sas log文件，查询关键词 ERROR:  找到调用失败，找不到调用成功
                        EcrfSASTrigger.log.info(line);
                        writer.write(line);
                        writer.newLine();
                        if (line.contains("ERROR:") || line.contains("WARNING:")) {
                            isSuccess = false;
                            errorAndWarningCount++;
                        }
                    }
                    EcrfSASTrigger.log.info("-------------------------sas日志内容 结束位置 do not go gentle into that good night !!!： ---------------------------------------------------");
                } catch (IOException e) {
                    // Handle the exception
                    isSuccess = false;
                }
                //upload SAS log to minio
                File logFile = new File(logPath);
                fileUtil.prependLineToFile(logFile, "the SAS LOG contains ERROR and WARNING count is " + errorAndWarningCount);
                String md5 = decode64Util.getMd5(logFile);
                log.info("logFile path is " + logFile.getPath());
                logPath = logFile.getPath();
                EcrfSASTrigger.log.info("-------------------------sas调用完成 ！！！开始上传日志----------------------------------------");
                sasLogPath = minioUtil.uploadObject(logFile, md5, studyId);
                iWorkspace.Close();
                result.put("logPath", logPath);
                //this just records SAS ERROR and WARNING count
                result.put("isSuccess", isSuccess);
                result.put("sasLogPath", sasLogPath);
                EcrfSASTrigger.log.info("------------------------------------sas程序返回的调用回执是:" + result.toString() + "-----------------------------------");
                return result;
            } catch (GenericError e) {
                throw new RuntimeException(e);
            } catch (IOException e) {
                throw new RuntimeException(e);
            } finally {
                cx.close();
            }
        } catch (ConnectionFactoryException e) {
            throw new RuntimeException(e);
        }



    }


}
