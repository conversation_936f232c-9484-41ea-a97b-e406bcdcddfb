package com.hengrui.blind_back.utils;

import java.io.FileInputStream;
import java.io.IOException;

import com.hengrui.blind_back.constant.SASOnlieConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.vfs2.auth.StaticUserAuthenticator;
import org.apache.commons.vfs2.impl.DefaultFileSystemConfigBuilder;
import org.springframework.stereotype.Component;
import org.apache.commons.vfs2.*;
import org.apache.commons.vfs2.provider.sftp.SftpFileSystemConfigBuilder;

import java.io.File;
@Component
@Slf4j
public class FTPUploadExample {

    public static  void upload() {
        String server = "clinical-ftp.hengrui.com";
        int port = 22357;
        String user = SASOnlieConstant.RTSM_API_USER;
        String pass =SASOnlieConstant.RTSM_API_PASS;

        FTPClient ftpClient = new FTPClient();
        try {
            ftpClient.connect(server, port);
            ftpClient.login(user, pass);
            ftpClient.enterLocalPassiveMode();

            String localFilePath = "C:\\Users\\<USER>\\Desktop\\output.png";
            String remoteFilePath = "/Projects/CDTMS手册优化测试项目/EDM_Unblind/PD/output.png";

            FileInputStream inputStream = new FileInputStream(localFilePath);

            System.out.println("Uploading file...");
            boolean done = ftpClient.storeFile(remoteFilePath, inputStream);
            inputStream.close();

            if (done) {
                System.out.println("File uploaded successfully.");
            } else {
                System.out.println("File upload failed.");
            }

        } catch (IOException ex) {
            System.out.println("Error: " + ex.getMessage());
            ex.printStackTrace();
        } finally {
            try {
                if (ftpClient.isConnected()) {
                    ftpClient.logout();
                    ftpClient.disconnect();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
    }


    public static  void VFSUpload() {
        String hostname = "clinical-ftp.hengrui.com";
        int port = 22357; // Default SFTP port
        String username = SASOnlieConstant.RTSM_API_USER;
        String password = SASOnlieConstant.RTSM_API_PASS;

        String remoteFilePath ="/Projects/CDTMS手册优化测试项目/EDM_Unblind/PD/";
        String localFilePath = "C:\\Users\\<USER>\\Desktop\\output.png";

        try {
            // Create file system manager
            FileSystemManager fsManager = VFS.getManager();

            // Configure SFTP
            FileSystemOptions opts = new FileSystemOptions();
            SftpFileSystemConfigBuilder.getInstance().setStrictHostKeyChecking(opts, "no");
            SftpFileSystemConfigBuilder.getInstance().setUserDirIsRoot(opts, false);
            // Explicitly set to use password authentication and disable key authentication
            SftpFileSystemConfigBuilder.getInstance().setPreferredAuthentications(opts, "password");
            SftpFileSystemConfigBuilder.getInstance().setSessionTimeoutMillis(opts, 30000);
            SftpFileSystemConfigBuilder.getInstance().setKeyExchangeAlgorithm(opts, "diffie-hellman-group1-sha1,diffie-hellman-group14-sha1,diffie-hellman-group-exchange-sha1,diffie-hellman-group-exchange-sha256");
            // Set the user authentication data separately
            UserAuthenticator auth = new StaticUserAuthenticator(null, username, password);
            DefaultFileSystemConfigBuilder.getInstance().setUserAuthenticator(opts, auth);
            // Create remote file object
            String remoteUrl = String.format("sftp://%s:%d%s", hostname, port, remoteFilePath);
            FileObject remoteFile = fsManager.resolveFile(remoteUrl, opts);

            // Create local file object
            FileObject localFile = fsManager.resolveFile(new File(localFilePath).getAbsolutePath());

            // Copy local file to remote file
            remoteFile.copyFrom(localFile, Selectors.SELECT_SELF);

            System.out.println("File uploaded successfully!");

            // Close file objects
            remoteFile.close();
            localFile.close();

        } catch (FileSystemException e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
