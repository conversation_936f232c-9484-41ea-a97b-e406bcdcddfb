package com.hengrui.blind_back.smo_data.controller;

import com.hengrui.blind_back.constant.ResponseResult;
import com.hengrui.blind_back.smo_data.entity.SearchEntity;
import com.hengrui.blind_back.smo_data.service.SMODataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName SMODataController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/6 9:23
 * @Version 1.0
 **/

@RestController
@Slf4j
public class SMODataController {

    @Autowired
    SMODataService smoDataService;


    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/getSMODataBySite")
    @ResponseBody
    public ResponseResult<?> getSMODataBySite(@RequestBody SearchEntity entity, HttpServletResponse response) {
        String result = smoDataService.getSMODataBySite(entity, response);
        if(result.equals("success")){
            return new ResponseResult<>(200, "OK",result );
        }else{
            return new ResponseResult<>(404, "FAIL",result );
        }

    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/getSMODataTst")
    @ResponseBody
    public List<Map<String, String>> getSMODataTst(@RequestBody SearchEntity entity) {
        List<Map<String, String>> results = smoDataService.getSMODataTst(entity);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        return results;
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getSMOProjectLan")
    @ResponseBody
    public ResponseResult<?> getSMOProjectLan(String studyId) {
        String result=smoDataService.getSMOProjectLan(studyId);
        if(!result.equals("notFound")){
            return new ResponseResult<>(200, "OK", result);
        }else{
            return new ResponseResult<>(404, "fail", result);
        }

    }
}
