package com.hengrui.blind_back.onlyOffice.service;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public interface OnlyOfficeFileService {
     void saveOnlyOfficeFile(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException;

    void saveAuditApprovalFile(HttpServletRequest request, HttpServletResponse response) throws IOException;
}
