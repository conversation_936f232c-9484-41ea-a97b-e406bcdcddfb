package com.hengrui.blind_back.blind.utils;

import cn.hutool.extra.mail.MailAccount;
import cn.hutool.extra.mail.MailException;
import cn.hutool.extra.mail.MailUtil;
import com.hengrui.blind_back.blind.constant.BlindConstant;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.util.Zip4jConstants;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import java.io.*;
import net.lingala.zip4j.core.ZipFile;
import javax.crypto.*;
import java.net.MalformedURLException;
import java.net.ProtocolException;
import java.net.URL;
import java.net.URLEncoder;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
public class MailSendUtil {

    @Transactional
    public String batchSendMails(File[] files, ArrayList<String> tos, String qcComments, String fileName, String projectName, String sendType, String isApprove, String md5, String url,String signName,String phoneNumber) {
        String sendTime = "";
        //发送邮件
        MailAccount account = new MailAccount();
        account.setHost(BlindConstant.MAIL_HOST);
        account.setPort(BlindConstant.MAIL_PORT);
        account.setAuth(BlindConstant.MAIL_AUTH);
        account.setFrom(BlindConstant.MAIL_FROM);
        account.setUser(BlindConstant.MAIL_ACCOUNT);
        account.setPass(BlindConstant.MAIL_PASSWORD);
        List<String> from = new ArrayList<>();
        from.add(BlindConstant.MAIL_ACCOUNT);
        account.setSocketFactoryPort(BlindConstant.MAIL_PORT);

//        //校验邮箱地址的有效性质
        tos.add("<EMAIL>");
        ArrayList<String> validMails = validEmailsAddress(tos);
        try {
            LocalDate date = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
            String currentDate = date.format(formatter);


            //邮件群发动作
//            send(mailAccount, false, tos, ccs, bccs, subject, content, (Map)null, isHtml, files)
//            customMailUtil.send(account,false, validMails, from, validMails, UtilConstant.EMAIL_HEADER_PART_ONE + compound + UtilConstant.EMAIL_HEADER_PART_TWO + currentDate,
//                    UtilConstant.EMAIL_CONTENT_PART_ONE + compound + UtilConstant.EMAIL_CONTENT_PART_TWO, (Map)null,false, files);
            //判断发送类型
            if (!ObjectUtils.isEmpty(sendType) && !sendType.isEmpty()) {
                //0代表发送给edm qc人员
                if (sendType.equals("0")) {
                    if (!ObjectUtils.isEmpty(url) && !url.isEmpty()) {
                        //给地址
                        MailUtil.send(account, from, from, validMails, BlindConstant.MAIL_TITLE_PARTONE + projectName + BlindConstant.EDM_MAIL_TITLE_PARTTWO + currentDate,
                                BlindConstant.EDM_EMAIL_CONTENT_PART_ONE + projectName + BlindConstant.EDM_EMAIL_CONTENT_PART_TWO + fileName + "<br />"+"链接："
                                        + url+"<br /><br />"+"祝好"+"<br />"+signName+BlindConstant.SIGN_PART_CONTENT+phoneNumber, true, files);
                    } else {
                        //不给地址
                        MailUtil.send(account, from, from, validMails, BlindConstant.MAIL_TITLE_PARTONE + projectName + BlindConstant.EDM_MAIL_TITLE_PARTTWO + currentDate,
                                BlindConstant.EDM_EMAIL_CONTENT_PART_ONE + projectName + BlindConstant.EDM_EMAIL_CONTENT_PART_TWO + fileName+"<br /><br />"+"祝好"+"<br />"+signName+BlindConstant.SIGN_PART_CONTENT+phoneNumber, true, files);
                    }

                    MailSendUtil.log.info(projectName + "上传的发送成功！！！");
                } else if (sendType.equals("1")) {
                    if (!ObjectUtils.isEmpty(url) && !url.isEmpty()) {

                        if(isApprove.equals("通过")){
                            //给地址
                            MailUtil.send(account, from, from, validMails, BlindConstant.MAIL_TITLE_PARTONE + projectName + BlindConstant.EDMQC_MAIL_TITLE_PARTTWO + currentDate,
                                    BlindConstant.EDMQC_EMAIL_CONTENT_PART_ONE + projectName + BlindConstant.EDMQC_EMAIL_CONTENT_PART_TWO + fileName + BlindConstant.EDMQC_EMAIL_CONTENT_PART_THREE + "<b>" + isApprove + "</b>"+"<br />"+"链接："
                                            + url+"<br /><br />"+"祝好"+"<br />"+signName+BlindConstant.SIGN_PART_CONTENT+phoneNumber, true, files);
                        }else if(isApprove.equals("未通过")){
                            //给地址
                            MailUtil.send(account, from, from, validMails, BlindConstant.MAIL_TITLE_PARTONE + projectName + BlindConstant.EDMQC_MAIL_TITLE_PARTTWO + currentDate,
                                    BlindConstant.EDMQC_EMAIL_CONTENT_PART_ONE + projectName + BlindConstant.EDMQC_EMAIL_CONTENT_PART_TWO + fileName + BlindConstant.EDMQC_EMAIL_CONTENT_PART_THREE + "<b>" + isApprove + "</b>"+"<br />"+"QC Finding:" +"<br />"+qcComments+"<br />"+"链接："
                                            + url+"<br /><br />"+"祝好"+"<br />"+signName+BlindConstant.SIGN_PART_CONTENT+phoneNumber, true, files);
                        }

                    } else {
                        //不给地址
                        MailUtil.send(account, from, from, validMails, BlindConstant.MAIL_TITLE_PARTONE + projectName + BlindConstant.EDMQC_MAIL_TITLE_PARTTWO + currentDate,
                                BlindConstant.EDMQC_EMAIL_CONTENT_PART_ONE + projectName + BlindConstant.EDMQC_EMAIL_CONTENT_PART_TWO + fileName + BlindConstant.EDMQC_EMAIL_CONTENT_PART_THREE + "<b>" + isApprove + "<br />"+"QC Finding:" +"<br />"+qcComments+"</b>"+"<br /><br />"+"祝好"+"<br />"+signName+BlindConstant.SIGN_PART_CONTENT+phoneNumber, true, files);
                    }
                    //1 代表发送给edm 人员

                    MailSendUtil.log.info(projectName + "上传的发送成功！！！");
                } else if (sendType.equals("2")) {
                    //2 代表发送最终qc通过的文件
                    MailUtil.send(account, from, from, validMails, BlindConstant.MAIL_TITLE_PARTONE + projectName + BlindConstant.MEMBER_MAIL_TITLE_PARTTWO + currentDate,
                            BlindConstant.MEMBER_EMAIL_CONTENT_PART_ONE + projectName + BlindConstant.MEMBER_EMAIL_CONTENT_PART_TWO + fileName + BlindConstant.MEMBER_EMAIL_CONTENT_PART_THREE + md5+"<br /><br />"+"祝好"+"<br />"+signName+BlindConstant.SIGN_PART_CONTENT+phoneNumber, true, files);
                    MailSendUtil.log.info(projectName + "上传的发送成功！！！");
                }else if(sendType.equals("3")){
                    //2 代表发送最终qc通过的文件的密码
                    MailUtil.send(account, from, from, validMails, BlindConstant.MAIL_TITLE_PARTONE + projectName + BlindConstant.MEMBER_MAIL_TITLE_PARTTWO + currentDate,
                            BlindConstant.MEMBER_EMAIL_CONTENT_PART_ONE + projectName + BlindConstant.PASSWORD_EMAIL_CONTENT_PART_TWO + fileName +"<br /><br />"+"祝好"+"<br />"+signName+BlindConstant.SIGN_PART_CONTENT+phoneNumber, true, files);
                    MailSendUtil.log.info(projectName + "上传的发送成功！！！");
                }
            }


            //新增邮件的发送记录  fileName,  mailContent,  receiver,  filePath,  fileId


            //获取邮件发送的时间

            //获取邮件发送时间
//            sendTime = mail.getSendTime();
            //创建文件，转移并备份文件到相应的月度包,这里是多个邮件,需要传入的就是源文件夹，
//                    SMBUtils moveFile=SMBUtils.getInstance(folderPath);
//                    moveFile.createMothFolder(sendTime,folderPath);
//                    moveFile.close();
        } catch (MailException e) {
            MailSendUtil.log.info(e.getMessage());
            MailSendUtil.log.info(String.valueOf(e.getCause()));
            //新增发送失败记录

        }
        return sendTime;
    }


    public ArrayList<String> validEmailsAddress(ArrayList<String> tos) {
        ArrayList<String> validEmails = new ArrayList<>();
        ArrayList<String> invalidEmails = new ArrayList<>();
        String regex = "^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@"
                + "[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$";
//                String regex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        Pattern pattern = Pattern.compile(regex);
        for (String email : tos) {
            if (!StringUtils.isEmpty(email)) {
                Matcher matcher = pattern.matcher(email);
                if (matcher.matches()) {
                    validEmails.add(email);
                } else {
                    invalidEmails.add(email);
                }
            } else {
                invalidEmails.add(email);
            }

        }
        //接收者邮箱数量和有效邮箱数量不一致，说明存在无效邮箱
        if (tos.size() != validEmails.size()) {
            //无效的邮箱需要入库形成发送给失败的记录
//            entity.setReceiver(StringUtils.join(invalidEmails, ","));
//            entity.setStatus("N");
//            fileUploadMapper.addMailSendHistory(entity);
        }

        //返回有效的邮箱地址
        return validEmails;
    }

    /**
     * MultipartFile 转为File
     *
     * @param multiFile
     * @return
     */
    public static File MultipartFileToFile(MultipartFile multiFile) {
        // 获取文件名
        String fileName = multiFile.getOriginalFilename();
        // 若需要防止生成的临时文件重复,可以在文件名后添加随机码
        try {
            File file =new File(BlindConstant.Params_Path+fileName);
            if(file.isFile() && file.exists()){
                file.delete();
            }
            multiFile.transferTo(file);
            return file;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    //文件下载
    public static String downloadFile(String projectName, String fileName, String token) throws UnsupportedEncodingException, NoSuchAlgorithmException {
        String fileUrl = BlindConstant.EDM_DOWNLOAD_URL + projectName + "&filename=" + URLEncoder.encode(fileName, "UTF-8") + "&token=" + URLEncoder.encode(token, "UTF-8");
        String localFilePath = BlindConstant.EDM_UAP_File_Path + File.separator + fileName;
        //设置可通过ip地址访问https请求
        HttpsURLConnection.setDefaultHostnameVerifier(new NullHostNameVerifier());
        TrustManager[] tm = {new MyX509TrustManager()};
        SSLContext sslContext = SSLContext.getInstance("TLS");
        try {
            sslContext.init(null, tm, new java.security.SecureRandom());
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        }
        // 从上述SSLContext对象中得到SSLSocketFactory对象
        SSLSocketFactory ssf = sslContext.getSocketFactory();
        String urlStr = fileUrl;
        URL url = null;
        try {
            url = new URL(urlStr);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
        HttpsURLConnection con = null;
        try {
            con = (HttpsURLConnection) url.openConnection();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        con.setSSLSocketFactory(ssf);
        try {
            con.setRequestMethod("POST"); // 设置以POST方式提交数据
        } catch (ProtocolException e) {
            throw new RuntimeException(e);
        }
        con.setDoInput(true); // 打开输入流，以便从服务器获取数据
        con.setDoOutput(true);// 打开输出流，以便向服务器提交数据
        //设置发送参数
        PrintWriter out = null;
        try {
            out = new PrintWriter(new OutputStreamWriter(con.getOutputStream(), "UTF-8"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        out.flush();
        out.close();
        try {
            con.getInputStream();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        OutputStream os = null;
        try {
            String path = localFilePath;
            // 2、保存到临时文件
            // 1K的数据缓冲
            byte[] bs = new byte[1024];
            // 读取到的数据长度
            int len;
            // 输出的文件流保存到本地文件
            File tempFile = new File(path);
            os = new FileOutputStream(tempFile.getPath());
            // 开始读取
            while ((len = con.getInputStream().read(bs)) != -1) {
                os.write(bs, 0, len);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 完毕，关闭所有链接
            try {
                os.close();
                con.getInputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return localFilePath;
    }

    public static String checkLogin(String token) {
        String urlStr = SASOnlieConstant.CDTMS_MED_CHECK_LOGIN_API + token;
        //设置可通过ip地址访问https请求
        HttpsURLConnection.setDefaultHostnameVerifier(new NullHostNameVerifier());
        TrustManager[] tm = {new MyX509TrustManager()};
        SSLContext sslContext = null;
        try {
            sslContext = SSLContext.getInstance("TLS");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        try {
            sslContext.init(null, tm, new java.security.SecureRandom());
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        }
        // 从上述SSLContext对象中得到SSLSocketFactory对象
        SSLSocketFactory ssf = sslContext.getSocketFactory();
        URL url = null;
        try {
            url = new URL(urlStr);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
        HttpsURLConnection con = null;
        try {
            con = (HttpsURLConnection) url.openConnection();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        con.setSSLSocketFactory(ssf);
        try {
            con.setRequestMethod("GET"); // 设置以POST方式提交数据
        } catch (ProtocolException e) {
            throw new RuntimeException(e);
        }
        con.setDoInput(true); // 打开输入流，以便从服务器获取数据


        // 获取请求的返回值
        BufferedReader in = null;
        try {
            in = new BufferedReader(new InputStreamReader(con.getInputStream()));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        String inputLine;
        StringBuffer response = new StringBuffer();
        while (true) {
            try {
                if (!((inputLine = in.readLine()) != null)) break;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            response.append(inputLine);
        }
        try {
            in.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        String result = response.toString();
// 打印返回值
        return result;
    }



    //文件压缩加密
   public static File packageFolderWithPassword(String folder,String target,String password) throws IOException, ZipException {
        ZipFile zipFile = new ZipFile(target);
        ZipParameters parameters = new ZipParameters();
        parameters.setCompressionMethod(Zip4jConstants.COMP_DEFLATE);
        //加密
       if(!password.isEmpty()){
           parameters.setEncryptFiles(true);
           parameters.setEncryptionMethod(Zip4jConstants.ENC_METHOD_STANDARD);
           parameters.setPassword(password);
       }
       zipFile.addFile(new File(folder), parameters);
       return new File(target);
   }

   public static void createFolder(String folderPath){
       File destFile = new File(folderPath);
       if (destFile.isDirectory() || !destFile.exists()){
           destFile.mkdirs();
       }
   }


}
