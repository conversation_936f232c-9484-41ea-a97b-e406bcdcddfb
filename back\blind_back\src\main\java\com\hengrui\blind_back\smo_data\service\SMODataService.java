package com.hengrui.blind_back.smo_data.service;

import com.hengrui.blind_back.smo_data.entity.SearchEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface SMODataService {
    String getSMODataBySite(SearchEntity entity, HttpServletResponse response);

    List<Map<String, String>> getSMODataTst(SearchEntity entity);

    String getSMOProjectLan(String studyId);
}
