package com.hengrui.blind_back.rtsm.service;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.hengrui.blind_back.blind.utils.ResponseResult;
import com.hengrui.blind_back.rtsm.entity.EsignEntity;

import java.util.List;
import java.util.Map;

public interface RTSMService {
    Map<String, String> submitToRTSMSAS(String taskId, String projectId);

    Map<String, String> submitToRTSMSASBack(String taskId, String projectId);

    Map<String, String> getApplyInfo(String studyId,String batchNum);

    Map<String, String> sendReviewEmail(String param,String studyId,String batchNum);

    String accountApprove(String studyId, String fileKey, String userName);

    String makeRTSMTemplate(String studyId,String accountVersion, String batchNum, String accountManagerName, int fileType, int applyType, String fileName, JSONArray accountList,String applyType2);

    String addBatchRecord(String studyId, String userName,String applyDate,String param);

    List<Map<String, String>> getStudyBatchRecords(String studyId);

    String checkValidCode(String studyId, String batchNum,String code);

    Map<String, String> getValidCode(String studyId, String batchNum,String rtsmAccountEmail,String accountName);

    String updateApplyDate(String studyId, String batchNum, String applyDate);

    Map<String, Object> getRTSMFileNames(String taskId,String projectId);

    void getRTSMFile(String taskId, String projectId);


    void getRTSMMonitorReport(String taskId, String projectId);

    String editFormInfo(String param, String studyId, String batchNum,String userName);

    String deleteBatchRecord(String studyId, String batchNum,String userName);

    String forceSave(String fileKey, String userId);

    String saveAsRecord(String studyId, String userName, String filePath);

    String RTSMESignCallBack(JSONObject param);

    ResponseResult<?> sendSign(String param,String studyId, String fileKey, String userName);

    ResponseResult<?> checkSignPass(String email, String pass,String taskId);

    ResponseResult<?> getEsignInfo(EsignEntity param);

    ResponseResult<?> resign(EsignEntity param);

    ResponseResult<?> setSignPass(String email, String pass);

    ResponseResult<?> checkFirstSign(String email);

    ResponseResult<?> addSigners(EsignEntity param);

    ResponseResult<?> cancelSignTask(EsignEntity param);

    String getApproveStatusByName(String studyId, String batchNum, String userName);

    void checkExpiringSignTasks();

    ResponseResult<?> deleteSigner(EsignEntity param);

    ResponseResult<?> resendNotification(String taskId, String email, String userName);

    String addAuditAndApprovalTable(String studyId, String userName,  String param);

    ResponseResult<?> sendAuditAndApprovalSign(String param, String studyId, String fileKey, String userName);

    Map<String, String> getAuditApprovalInfo(String studyId, String batchNum);

    List<Map<String, String>> getAABatchRecords(String studyId);

    ResponseResult<?> sendAccountSign(String param, String studyId);

    Map<String, String> getAASignFilePathByTaskId(String taskId);
}
