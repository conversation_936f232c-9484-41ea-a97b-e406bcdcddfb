package com.hengrui.blind_back.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hengrui.blind_back.blind.utils.MyX509TrustManager;
import com.hengrui.blind_back.blind.utils.NullHostNameVerifier;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.itextpdf.text.*;
import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.*;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.util.Zip4jConstants;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xssf.usermodel.*;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.stereotype.Component;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.swing.*;
import java.io.*;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.ProtocolException;
import java.net.URL;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * @ClassName FileUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/8/20 14:42
 * @Version 1.0
 **/
@Slf4j
@Component
public class FileUtils {

    public static void unzip(String zipFilePath, String destDirectory) throws IOException {
        File destDir = new File(destDirectory);
        if (!destDir.exists()) {
            destDir.mkdir();
        }

        try (ZipInputStream zipIn = new ZipInputStream(new FileInputStream(zipFilePath), Charset.forName("GBK"))) {
            ZipEntry entry = zipIn.getNextEntry();
            while (entry != null) {
                String fileName = Paths.get(entry.getName()).getFileName().toString();
                String filePath = destDirectory + File.separator + fileName;
                if (!entry.isDirectory()) {
                    log.info("Extracting file: " + filePath);
                    extractFile(zipIn, filePath);
                } else {
                    log.info("Creating directory: " + filePath);
                    File dir = new File(filePath);
                    dir.mkdir();
                }
                zipIn.closeEntry();
                entry = zipIn.getNextEntry();
            }
        }
    }


    public static boolean isZipFileEmpty(String zipFilePath) {
        try (ZipFile zipFile = new ZipFile(zipFilePath)) {
            return zipFile.size() == 0;
        } catch (IOException e) {
            e.printStackTrace();
            // Depending on your error handling strategy, you might want to:
            // throw new RuntimeException("Error checking zip file", e);
            return true; // or false, depending on how you want to handle errors
        }
    }

    private static void extractFile(ZipInputStream zipIn, String filePath) throws IOException {
        File file = new File(filePath);
        try (BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(file.getPath()))) {
            byte[] bytesIn = new byte[4096];
            int read;
            while ((read = zipIn.read(bytesIn)) != -1) {
                bos.write(bytesIn, 0, read);
            }
        }
    }


    public static void compareFilesWithEcrfNums(String folderPath, List<String> ecrfNumList) throws IOException {
        Set<String> ecrfNumSet = new HashSet<>(ecrfNumList);

        try (Stream<Path> paths = Files.walk(Paths.get(folderPath))) {
            paths.filter(Files::isRegularFile).forEach(path -> {
                String fileName = path.getFileName().toString();
                ecrfNumSet.stream().filter(ecrfNum -> fileName.contains(ecrfNum)).forEach(ecrfNum -> FileUtils.log.info("File: " + fileName + " contains ecrfNum: " + ecrfNum));
            });
        }
    }


    public static int findAndZipMatchingFiles(String folderPath, List<String> ecrfNumList, String outputZipPath) throws IOException {
        Set<String> ecrfNumSet = new HashSet<>(ecrfNumList);
        List<Path> matchingFiles = new ArrayList<>();

        // Find matching files
        try (Stream<Path> paths = Files.walk(Paths.get(folderPath))) {
            matchingFiles = paths.filter(Files::isRegularFile).filter(path -> ecrfNumSet.stream().anyMatch(ecrfNum -> path.getFileName().toString().toLowerCase().contains(ecrfNum.toLowerCase()))).collect(Collectors.toList());
        }

        // Zip matching files
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(outputZipPath))) {
            for (Path file : matchingFiles) {
                ZipEntry zipEntry = new ZipEntry(file.getFileName().toString());
                zos.putNextEntry(zipEntry);
                Files.copy(file, zos);
                zos.closeEntry();
                FileUtils.log.info("Added to zip: " + file.getFileName());
            }
        }

        FileUtils.log.info("Zip file created at: " + outputZipPath);
        return matchingFiles.size();
    }


    public static void replaceVariables(String inputFile, String outputFile, Map<String, String> variables) throws IOException, InvalidFormatException {

        try (XWPFDocument doc = new XWPFDocument(new FileInputStream(inputFile))) {

            // Replace in paragraphs
            for (XWPFParagraph paragraph : doc.getParagraphs()) {
                replaceParagraphText(paragraph, variables);
            }

            // Replace in tables
            for (XWPFTable table : doc.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            replaceParagraphText(paragraph, variables);
                        }
                    }
                }
            }

            // Write the modified document
            try (FileOutputStream out = new FileOutputStream(outputFile)) {
                doc.write(out);
            }
        }
    }


    private static void replaceParagraphText(XWPFParagraph paragraph, Map<String, String> params) {
        String paragraphText = paragraph.getText();

        for (Map.Entry<String, String> entry : params.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            if (paragraphText.contains(key)) {
                // 处理多行文本
                if (value.contains("\n")) {
                    // 清除原有的runs
                    while (paragraph.getRuns().size() > 0) {
                        paragraph.removeRun(0);
                    }

                    // 分行处理
                    String[] lines = value.split("\n");
                    for (int i = 0; i < lines.length; i++) {
                        XWPFRun run = paragraph.createRun();
                        run.setText(lines[i]);
                        if (i < lines.length - 1) {
                            run.addBreak(); // 添加换行符
                        }
                    }
                } else {
                    // 普通文本替换
                    List<XWPFRun> runs = paragraph.getRuns();
                    TextSegment found = paragraph.searchText(key, new PositionInParagraph());
                    if (found != null) {
                        // 替换文本
                        if (runs.size() > 0) {
                            XWPFRun run = runs.get(found.getBeginRun());
                            run.setText(value, 0);

                            // 如果文本跨多个run，清除其他run
                            if (found.getBeginRun() != found.getEndRun()) {
                                for (int runPos = found.getBeginRun() + 1; runPos <= found.getEndRun(); runPos++) {
                                    paragraph.removeRun(runPos);
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    private static void replaceParagraph(XWPFParagraph paragraph, Map<String, String> variables) {
        String text = paragraph.getText();
        for (Map.Entry<String, String> entry : variables.entrySet()) {
            if (text.contains(entry.getKey())) {
                List<XWPFRun> runs = paragraph.getRuns();
                for (XWPFRun run : runs) {
                    String runText = run.getText(0);
                    if (runText != null && runText.contains(entry.getKey())) {
                        runText = runText.replace(entry.getKey(), entry.getValue());
                        run.setText(runText, 0);
                    }
                }
            }
        }
    }


    public static void toZip(List<File> srcFiles, OutputStream out) throws RuntimeException {

        long start = System.currentTimeMillis();

        ZipOutputStream zos = null;

        try {

            zos = new ZipOutputStream(out);

            for (File srcFile : srcFiles) {

                byte[] buf = new byte[2048];

                zos.putNextEntry(new ZipEntry(srcFile.getName()));

                int len;

                FileInputStream in = new FileInputStream(srcFile);

                while ((len = in.read(buf)) != -1) {

                    zos.write(buf, 0, len);

                }

                zos.closeEntry();

                in.close();

            }

            long end = System.currentTimeMillis();

            log.info("压缩完成，耗时：" + (end - start) + " ms");

        } catch (Exception e) {

            throw new RuntimeException("zip error from ZipUtils", e);

        } finally {

            if (zos != null) {

                try {

                    zos.close();

                } catch (IOException e) {

                    e.printStackTrace();

                }

            }

        }

    }


    public static File packageFolderWithPassword(List<String> folders, String target, String password) throws IOException, ZipException {
        net.lingala.zip4j.core.ZipFile zipFile = new net.lingala.zip4j.core.ZipFile(target);
        ZipParameters parameters = new ZipParameters();
        parameters.setCompressionMethod(Zip4jConstants.COMP_DEFLATE);
        //加密
        if (!password.isEmpty()) {
            parameters.setEncryptFiles(true);
            parameters.setEncryptionMethod(Zip4jConstants.ENC_METHOD_STANDARD);
            parameters.setPassword(password);
        }
        for (String folder : folders) {
            zipFile.addFile(new File(folder), parameters);
        }

        return new File(target);
    }


    /**
     * 将Word文档转换为PDF
     *
     * @param docxPath Word文档路径
     * @param pdfPath  输出PDF路径
     */
    public static void convertDocxToPdf(String docxPath, String pdfPath) throws Exception {
        // 加载Word文档
        XWPFDocument document = new XWPFDocument(new FileInputStream(docxPath));

        // 创建PDF文档
        Document pdfDocument = new Document(PageSize.A4);
        PdfWriter.getInstance(pdfDocument, new FileOutputStream(pdfPath));
        pdfDocument.open();

        // 转换段落
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            Paragraph pdfParagraph = new Paragraph(paragraph.getText());
            pdfDocument.add(pdfParagraph);
        }

        // 转换表格
        for (XWPFTable table : document.getTables()) {
            PdfPTable pdfTable = new PdfPTable(table.getRows().get(0).getTableCells().size());

            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    PdfPCell pdfCell = new PdfPCell(new Phrase(cell.getText()));
                    pdfTable.addCell(pdfCell);
                }
            }

            pdfDocument.add(pdfTable);
        }

        pdfDocument.close();
        document.close();
    }


    public static void copyAndPasteContent(String excelFile, int sheetIndex, int startRow, int endRow, int copyTimes) {
        if (copyTimes < 1) {
            copyTimes = 0; // 如果copyTimes小于1，则设置为0，表示不复制
        }
        try {
            // 1. 创建临时文件
            String tempFile = excelFile + ".temp";
            Files.copy(Paths.get(excelFile), Paths.get(tempFile), StandardCopyOption.REPLACE_EXISTING);

            // 2. 使用WorkBook处理以保留样式
            try (FileInputStream fis = new FileInputStream(excelFile); Workbook workbook = WorkbookFactory.create(fis)) {

                Sheet sheet = workbook.getSheetAt(sheetIndex);
                // 在处理行复制之前，先收集需要复制的合并区域信息
                List<CellRangeAddress> mergedRegionsToAdd = new ArrayList<>();
                // 存储原有的数据验证信息
                Map<Integer, List<DataValidationInfo>> rowValidations = new HashMap<>();
                for (DataValidation validation : sheet.getDataValidations()) {
                    CellRangeAddressList regions = validation.getRegions();
                    for (CellRangeAddress addr : regions.getCellRangeAddresses()) {
                        int row = addr.getFirstRow();
                        if (row > endRow) {  // 只保存需要移动的行的数据验证
                            rowValidations.computeIfAbsent(row, k -> new ArrayList<>()).add(new DataValidationInfo(addr.getFirstColumn(), validation.getValidationConstraint(), validation.getShowErrorBox(), validation.getShowPromptBox(), validation.getErrorBoxTitle(), validation.getErrorBoxText()));
                        }
                    }
                }


                // 获取最后一行的索引
                int lastRowNum = sheet.getLastRowNum();

                // 存储要复制的行
                List<Row> rowsToInsert = new ArrayList<>();
                for (int i = startRow; i <= endRow; i++) {
                    Row sourceRow = sheet.getRow(i);
                    if (sourceRow != null) {
                        rowsToInsert.add(sourceRow);
                    }
                }

                // 计算需要移动的行数
                int rowsToShift = rowsToInsert.size() * copyTimes;

                // 从下往上移动后续行
                sheet.shiftRows(endRow + 1, lastRowNum, rowsToShift, true, false);


                // 重新应用被移动行的数据验证
                for (Map.Entry<Integer, List<DataValidationInfo>> entry : rowValidations.entrySet()) {
                    int oldRow = entry.getKey();
                    int newRow = oldRow + rowsToShift;

                    for (DataValidationInfo validationInfo : entry.getValue()) {
                        CellRangeAddressList addressList = new CellRangeAddressList(newRow, newRow, validationInfo.column, validationInfo.column);

                        DataValidationHelper dvHelper = sheet.getDataValidationHelper();
                        DataValidationConstraint constraint = null;

                        if (validationInfo.constraint.getExplicitListValues() != null) {
                            constraint = dvHelper.createExplicitListConstraint(validationInfo.constraint.getExplicitListValues());
                        } else if (validationInfo.constraint.getFormula1() != null) {
                            // 调整公式引用以匹配新的行号
                            String adjustedFormula = adjustFormula(validationInfo.constraint.getFormula1(), rowsToShift);
                            constraint = dvHelper.createFormulaListConstraint(adjustedFormula);
                        }

                        if (constraint != null) {
                            DataValidation newValidation = dvHelper.createValidation(constraint, addressList);
                            newValidation.setShowErrorBox(validationInfo.showErrorBox);
                            newValidation.setShowPromptBox(validationInfo.showPromptBox);
                            if (validationInfo.errorBoxTitle != null && validationInfo.errorBoxText != null) {
                                newValidation.createErrorBox(validationInfo.errorBoxTitle, validationInfo.errorBoxText);
                            }
                            sheet.addValidationData(newValidation);
                        }
                    }
                }
                // 移除目标区域可能存在的合并区域
                //        removeMergedRegionsInTargetArea(sheet, endRow + 1, lastRowNum, rowsToShift);

                // 复制行及其样式
                int currentRowIndex = endRow + 1;
                for (int time = 0; time < copyTimes; time++) {
                    // 处理合并单元格
                    for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
                        CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
                        if (mergedRegion.getFirstRow() >= startRow && mergedRegion.getLastRow() <= endRow) {
                            // 计算新的合并区域位置
                            int rowShift = (endRow - startRow + 1) * time + (endRow + 1 - startRow);
                            CellRangeAddress newMergedRegion = new CellRangeAddress(mergedRegion.getFirstRow() + rowShift, mergedRegion.getLastRow() + rowShift, mergedRegion.getFirstColumn(), mergedRegion.getLastColumn());
                            mergedRegionsToAdd.add(newMergedRegion);
                        }
                    }
                    for (Row sourceRow : rowsToInsert) {
                        Row newRow = sheet.createRow(currentRowIndex);
                        // 复制每个单元格及其样式
                        for (int i = 0; i < sourceRow.getLastCellNum(); i++) {
                            Cell sourceCell = sourceRow.getCell(i);
                            if (sourceCell != null) {
                                Cell newCell = newRow.createCell(i);

                                // 复制单元格样式
                                newCell.setCellStyle(sourceCell.getCellStyle());

                                // 复制数据验证（下拉框及选项）
                                CellRangeAddressList addressList = new CellRangeAddressList(currentRowIndex, currentRowIndex, i, i);
                                DataValidation sourceValidation = null;
                                // 获取源单元格的数据验证
                                for (DataValidation dataValidation : sheet.getDataValidations()) {
                                    CellRangeAddressList sourceAddressList = dataValidation.getRegions();
                                    for (CellRangeAddress addr : sourceAddressList.getCellRangeAddresses()) {
                                        if (addr.getFirstRow() == sourceRow.getRowNum() && addr.getFirstColumn() == i) {
                                            sourceValidation = dataValidation;
                                            break;
                                        }
                                    }
                                }

                                // 如果源单元格有数据验证，则复制到新单元格
                                if (sourceValidation != null) {
                                    DataValidation newValidation = null;
                                    if (sourceValidation.getValidationConstraint() != null) {
                                        DataValidationHelper dvHelper = sheet.getDataValidationHelper();
                                        DataValidationConstraint constraint = null;

                                        if (sourceValidation.getValidationConstraint().getExplicitListValues() != null) {
                                            // 复制下拉列表选项
                                            constraint = dvHelper.createExplicitListConstraint(sourceValidation.getValidationConstraint().getExplicitListValues());
                                        } else if (sourceValidation.getValidationConstraint().getFormula1() != null) {
                                            // 复制公式类型的数据验证
                                            constraint = dvHelper.createFormulaListConstraint(sourceValidation.getValidationConstraint().getFormula1());
                                        }

                                        if (constraint != null) {
                                            newValidation = dvHelper.createValidation(constraint, addressList);
                                            // 复制错误框设置
                                            newValidation.setShowErrorBox(sourceValidation.getShowErrorBox());
                                            newValidation.setShowPromptBox(sourceValidation.getShowPromptBox());
                                            if (sourceValidation.getErrorBoxText() != null && sourceValidation.getErrorBoxTitle() != null) {
                                                newValidation.createErrorBox(sourceValidation.getErrorBoxTitle(), sourceValidation.getErrorBoxText());
                                            }
                                            sheet.addValidationData(newValidation);
                                        }
                                    }
                                }


                                // 复制单元格内容
                                switch (sourceCell.getCellType()) {
                                    case STRING:

                                        if (sourceCell.getStringCellValue().contains("队列名称")) {
                                            newCell.setCellValue("队列名称" + (time + 2));
                                        } else if (sourceCell.getStringCellValue().contains("药物号设计")) {
                                            newCell.setCellValue("药物号设计" + (time + 2));
                                        } else if (sourceCell.getStringCellValue().contains("子方案/队列")) {
                                            newCell.setCellValue("子方案/队列" + (time + 2));
                                        } else if (sourceCell.getStringCellValue().contains("二次随机号设计1")) {
                                            newCell.setCellValue("二次随机号设计" + (time + 2));
                                        } else {
                                            newCell.setCellValue(sourceCell.getStringCellValue());
                                        }

                                        break;
                                    case NUMERIC:
                                        if (DateUtil.isCellDateFormatted(sourceCell)) {
                                            newCell.setCellValue(sourceCell.getDateCellValue());
                                        } else {
                                            newCell.setCellValue(sourceCell.getNumericCellValue());
                                        }
                                        break;
                                    case BOOLEAN:
                                        newCell.setCellValue(sourceCell.getBooleanCellValue());
                                        break;
                                    case FORMULA:
                                        newCell.setCellFormula(sourceCell.getCellFormula());
                                        break;
                                    case BLANK:
                                        break;
                                    default:
                                        newCell.setCellValue(sourceCell.toString());
                                }
                            }
                        }

                        // 复制行高
                        newRow.setHeight(sourceRow.getHeight());
                        currentRowIndex++;
                    }
                }


                // 添加新的合并区域
                for (CellRangeAddress region : mergedRegionsToAdd) {
                    try {
                        sheet.addMergedRegion(region);
                    } catch (IllegalStateException e) {
                        log.warn("Failed to add merged region {}: {}", region, e.getMessage());
                    }
                }

                // 保存工作簿
                try (FileOutputStream fileOut = new FileOutputStream(tempFile)) {
                    workbook.write(fileOut);
                }
            }

            // 3. 替换原文件
            Files.delete(Paths.get(excelFile));
            Files.move(Paths.get(tempFile), Paths.get(excelFile));

            log.info("Successfully copied rows {} to {} ({} times) with styles in sheet {}", startRow, endRow, copyTimes, sheetIndex);

        } catch (Exception e) {
            log.error("Error copying rows with styles: ", e);
            // 清理临时文件
            try {
                Files.deleteIfExists(Paths.get(excelFile + ".temp"));
            } catch (IOException ioe) {
                log.error("Error cleaning up temp file", ioe);
            }
        }
    }

    //生成一个i位的随机数字符串
    public static String getRandomString(int i) {
        return RandomStringUtils.randomNumeric(i);
    }

    public static void downloadFileByUrl(String url, String savePath) {
        // 创建URL对象
        URL fileUrl = null;
        try {
            fileUrl = new URL(url);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }

        // 打开网络输入流
        try (InputStream in = fileUrl.openStream()) {
            // 创建目标路径对象
            Path targetPath = Paths.get(savePath);

            // 确保父目录存在
            Path parentDir = targetPath.getParent();
            if (parentDir != null) {
                Files.createDirectories(parentDir);
            }

            // 保存文件并覆盖已存在的文件
            Files.copy(in, targetPath, StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    // 用于存储数据验证信息的辅助类
    private static class DataValidationInfo {
        final int column;
        final DataValidationConstraint constraint;
        final boolean showErrorBox;
        final boolean showPromptBox;
        final String errorBoxTitle;
        final String errorBoxText;

        DataValidationInfo(int column, DataValidationConstraint constraint, boolean showErrorBox, boolean showPromptBox, String errorBoxTitle, String errorBoxText) {
            this.column = column;
            this.constraint = constraint;
            this.showErrorBox = showErrorBox;
            this.showPromptBox = showPromptBox;
            this.errorBoxTitle = errorBoxTitle;
            this.errorBoxText = errorBoxText;
        }
    }

    // 调整公式引用的辅助方法
    public static String adjustFormula(String formula, int shiftAmount) {
        // 使用Pattern和Matcher来处理正则表达式
        Pattern pattern = Pattern.compile("([A-Z]+)(\\d+):([A-Z]+)(\\d+)");
        Matcher matcher = pattern.matcher(formula);

        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            // 提取列和行
            String startCol = matcher.group(1);
            String startRow = matcher.group(2);
            String endCol = matcher.group(3);
            String endRow = matcher.group(4);

            // 调整行号
            int newStartRow = Integer.parseInt(startRow);
            int newEndRow = Integer.parseInt(endRow);

            if (!startCol.contains("$")) {
                newStartRow += shiftAmount;
            }
            if (!endCol.contains("$")) {
                newEndRow += shiftAmount;
            }

            // 构建新的引用
            String replacement = startCol + newStartRow + ":" + endCol + newEndRow;
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);

        return result.toString();
    }


    public static boolean renameFile(String oldPath, String newPath) {
        File oldFile = new File(oldPath);
        File newFile = new File(newPath);

        // Check if old file exists and new file doesn't
        if (!oldFile.exists()) {
            log.info("File does not exist: " + oldPath);
            return false;
        }
        if (newFile.exists()) {
            log.info("File already exists: " + newPath);
            return false;
        }

        // Attempt to rename
        boolean success = oldFile.renameTo(newFile);
        if (success) {
            log.info("File renamed successfully");
        } else {
            log.info("Failed to rename file");
        }
        return success;
    }

    public static String formatDate(String input) {
        // Handle single digits by padding with zeros
        String[] parts = input.split("/");
        String formattedInput = String.format("%s/%02d/%02d/%02d/%02d", parts[0],                    // year
                Integer.parseInt(parts[1]),  // month
                Integer.parseInt(parts[2]),  // day
                Integer.parseInt(parts[3]),  // hour
                Integer.parseInt(parts[4])   // minute
        );

        // Create formatter for input format
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd/HH/mm");
        // Create formatter for output format
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // Parse and format
        LocalDateTime date = LocalDateTime.parse(formattedInput, inputFormatter);
        return date.format(outputFormatter);
    }


    public static String extractFileName(String input) {
        int startIndex = adjustIndex(input);
        int endIndex = input.indexOf("*");
        return input.substring(startIndex, endIndex);
    }

    public static int adjustIndex(String input) {
        // 找到第一个下划线的位置
        int underscoreIndex = input.indexOf('_');

        // 提取第一个下划线前的字符串
        String prefix = input.substring(0, underscoreIndex);

        // 判断条件：是否包含"-"和数字
        if (prefix.contains("-") && prefix.matches(".*\\d.*")) {
            // 如果条件满足，从第一个下划线的位置+1开始
            return underscoreIndex + 1;
        } else {
            // 否则，从原来的位置开始
            return 0;
        }
    }

    public static JSONObject findPreviousVersionObject(JSONArray versionArray, String inputVersion) {
        // Find the matching object in array
        for (int i = 0; i < versionArray.size(); i++) {
            if (inputVersion.equals(versionArray.getJSONObject(i).get("bbh").toString())) {
                if ((i + 1 < versionArray.size()) && !versionArray.getJSONObject(i + 1).isEmpty()) {
                    return versionArray.getJSONObject(i + 1);
                }
            }
        }
        if (versionArray.size() > 0) {
            com.alibaba.fastjson.JSONObject obj = versionArray.getJSONObject(0);
            return obj;
        } else {
            return null;
        }

    }


    public static void getCDTMSAPI(String server) {
        if (server.contains("meduap-tst")) {
            SASOnlieConstant.setRemoteServerApi("https://meduap-tst.hengrui.com:8085/");
        } else if (server.contains("cdtms-val")) {
            SASOnlieConstant.setRemoteServerApi("https://cdtms-val.hengrui.com/");
        } else if (server.contains("cdtms-pilot")) {
            SASOnlieConstant.setRemoteServerApi("https://cdtms-pilot.hengrui.com/");
        } else if (server.contains("cdtms.hengrui.com")) {
            SASOnlieConstant.setRemoteServerApiPrefix("cdtms.hengrui.com/");
        }
    }

    /**
     * 处理文件名，移除版本号并添加当前日期
     *
     * @param originalFileName 原始文件名
     * @return 处理后的文件名
     */
    public static String processFileName(String originalFileName) {
        if (originalFileName == null || originalFileName.isEmpty()) {
            return originalFileName;
        }

        // 移除版本号 (Vx.x 或 Vxx.x 或 Vx.xx 等格式)
        String processedName = originalFileName.replaceAll("-V\\d+(\\.\\d+)?", "");

        // 如果版本号在文件名末尾而不是中间，也要处理
        processedName = processedName.replaceAll("V\\d+(\\.\\d+)?\\.", ".");

        // 检查是否已经包含日期格式 yyyy-MM-dd
        Pattern datePattern = Pattern.compile("-\\d{4}-\\d{2}-\\d{2}");
        
        int lastDotIndex = processedName.lastIndexOf(".");
        String nameToCheck = lastDotIndex > 0 ? processedName.substring(0, lastDotIndex) : processedName;
        
        // 如果已经包含日期格式，直接返回
        if (datePattern.matcher(nameToCheck).find()) {
            return processedName;
        }


        // 检查是否已经包含日期格式 yyyy-MM-dd
        Pattern datePattern1 = Pattern.compile("_\\d{4}-\\d{2}-\\d{2}");

         lastDotIndex = processedName.lastIndexOf(".");
         nameToCheck = lastDotIndex > 0 ? processedName.substring(0, lastDotIndex) : processedName;

        // 如果已经包含日期格式，直接返回
        if (datePattern1.matcher(nameToCheck).find()) {
            return processedName;
        }


        // 获取当前日期，格式为YYYY-MM-DD
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String currentDate = dateFormat.format(new Date());

        // 在文件扩展名前添加日期
        if (lastDotIndex > 0) {
            String nameWithoutExtension = processedName.substring(0, lastDotIndex);
            String extension = processedName.substring(lastDotIndex);
            return nameWithoutExtension + "_" + currentDate + extension;
        } else {
            // 如果没有扩展名，直接在末尾添加日期
            return processedName + "_" + currentDate;
        }
    }

    public static void getCDTMSAPIProjectId(String server) {
        if (server.contains("meduap-tst")) {
            SASOnlieConstant.setRemoteServerProjectid("cdtmsen_val");
        } else if (server.contains("cdtms-val")) {
            SASOnlieConstant.setRemoteServerProjectid("cdtmsen_val2");
        } else if (server.contains("cdtms") && !server.contains("cdtms-val") && !server.contains("cdtms-pilot")) {
            SASOnlieConstant.setCdtmsCheckLoginApi("https://cdtms.hengrui.com/extdatabind.checktoken.do?token=");
        } else if (server.contains("cdtms-pilot")) {
            SASOnlieConstant.setCdtmsCheckLoginApi("https://cdtms-pilot.hengrui.com/extdatabind.checktoken.do?token=");
        }
    }

    public static void getCDTMSAPIPre(String server) {
        if (server.contains("meduap-tst")) {
            SASOnlieConstant.setRemoteServerApiPrefix("meduap-tst.hengrui.com:8085/");
        } else if (server.contains("cdtms-val")) {
            SASOnlieConstant.setRemoteServerApiPrefix("cdtms-val.hengrui.com/");
        } else if (server.contains("cdtms-pilot")) {
            SASOnlieConstant.setRemoteServerApiPrefix("cdtms-pilot.hengrui.com/");
        } else if (server.contains("cdtms.hengrui.com")) {
            SASOnlieConstant.setRemoteServerApiPrefix("cdtms.hengrui.com/");
        }
    }

    public static void setEDCAPIUrl(String studyId, String Server) {
        String edcVersion = "";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "study_dvs");
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "edc_name", "obj.studyid='" + studyInt + "'", "edit", "");
            com.alibaba.fastjson.JSONArray objects = com.alibaba.fastjson.JSONArray.parseArray(result);
            if (objects.size() > 0) {
                //获取edc版本
                com.alibaba.fastjson.JSONObject object = JSON.parseObject(objects.get(0).toString());
                edcVersion = object.get("edc_version").toString();
                log.info("----------------------------获取到的项目的edc版本是: " + edcVersion);
                if (edcVersion.equals("V4.1.0") && (Server.contains("meduap-tst") || Server.contains("cdtms-val"))) {
                    SASOnlieConstant.setEDCApiPrefix("https://meduap-tst.hengrui.com:84/");
                    SASOnlieConstant.setRTSMApiPrefix("https://meduap-tst.hengrui.com:84/");
                } else if (edcVersion.equals("V4.1.8") && (Server.contains("meduap-tst") || Server.contains("cdtms-val"))) {
                    SASOnlieConstant.setEDCApiPrefix("https://meduap-tst.hengrui.com:88/");
                    SASOnlieConstant.setRTSMApiPrefix("https://meduap-tst.hengrui.com:88/");
                } else if (edcVersion.equals("V4.1.20") && (Server.contains("meduap-tst") || Server.contains("cdtms-val"))) {
                    SASOnlieConstant.setEDCApiPrefix("https://meduap-tst.hengrui.com:89/");
                    SASOnlieConstant.setRTSMApiPrefix("https://meduap-tst.hengrui.com:89/");
                } else if (edcVersion.equals("V4.1.0") && (Server.contains("cdtms") && !Server.contains("cdtms-val") && !Server.contains("meduap-tst"))) {
                    SASOnlieConstant.setEDCApiPrefix("https://clinical.hengruipharma.com:88/");
                    SASOnlieConstant.setRTSMApiPrefix("https://clinical.hengruipharma.com:88/");
                } else if (edcVersion.equals("V4.1.8") && (Server.contains("cdtms") && !Server.contains("cdtms-val") && !Server.contains("meduap-tst"))) {
                    SASOnlieConstant.setEDCApiPrefix("https://clinical.hengruipharma.com:89/");
                    SASOnlieConstant.setRTSMApiPrefix("https://clinical.hengruipharma.com:89/");
                } else if (edcVersion.equals("V4.1.20") && (Server.contains("cdtms") && !Server.contains("cdtms-val") && !Server.contains("meduap-tst"))) {
                    SASOnlieConstant.setEDCApiPrefix("https://clinical.hengruipharma.com:91/");
                    SASOnlieConstant.setRTSMApiPrefix("https://clinical.hengruipharma.com:91/");
                }
            }
        }
    }


    public static long getExpirationTime() {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 计算三天后的时间
        LocalDateTime threeDaysLater = now.plusDays(3);

        // 将 LocalDateTime 转换为时间戳（秒）
        long timestamp = threeDaysLater.atZone(ZoneId.systemDefault()).toEpochSecond();

        // 返回时间戳
        return timestamp * 1000;
    }


    /**
     * 设置指定范围行的显隐状态
     *
     * @param sheet         Excel工作表
     * @param startRowIndex 开始行索引（0-based）
     * @param endRowIndex   结束行索引
     * @param hidden        true表示隐藏，false表示显示
     */
    public static void setRowVisibility(Sheet sheet, int startRowIndex, int endRowIndex, boolean hidden) {
        for (int i = startRowIndex; i <= endRowIndex; i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                row.setZeroHeight(hidden);
            }
        }
    }

    public static void controlRowVisibility(String filePath, int startRowIndex, int endRowIndex) {
        try (FileInputStream fis = new FileInputStream(filePath); Workbook workbook = new XSSFWorkbook(fis)) {

            // 获取Sheet2
            Sheet sheet = workbook.getSheetAt(1);

            // 根据参数控制行的显隐
            deleteRows(sheet, startRowIndex, endRowIndex);

            // 保存更改
            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                workbook.write(fos);
                fis.close();
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public static void deleteRows(Sheet sheet, int startRowIndex, int endRowIndex) {
        unprotectSheet(sheet);

        // 从后往前删除，避免索引变化
        if (startRowIndex == endRowIndex) {
            Row row = sheet.getRow(startRowIndex); // 直接获取要删除的行
            if (row != null) {
                clearRowFormat(row, startRowIndex, endRowIndex);
                sheet.removeRow(row);
            }
        } else {
            for (int i = endRowIndex; i >= startRowIndex; i--) {
                Row row = sheet.getRow(i); // 直接获取要删除的行
                if (row != null) {
                    clearRowFormat(row, startRowIndex, endRowIndex);
                    sheet.removeRow(row);
                }
            }
        }


        // 移动后续行以填补空缺
        shiftRows(sheet, endRowIndex + 1, sheet.getLastRowNum(), startRowIndex - endRowIndex - 1);
    }

    public static void shiftRows(Sheet sheet, int startRow, int endRow, int shiftDistance) {
        try {
            if (startRow > endRow || shiftDistance == 0) {
                return; // 参数无效或无需移动
            }

            // 1. 保存要移动的行的数据和样式
            Map<Integer, RowData> rowDataMap = new HashMap<>();
            for (int i = startRow; i <= endRow; i++) {
                Row sourceRow = sheet.getRow(i);
                if (sourceRow != null) {
                    rowDataMap.put(i, captureRowData(sourceRow));
                }
            }

            // 2. 根据移动方向调整移动策略
            if (shiftDistance > 0) {
                // 向下移动：从后向前移动
                for (int i = sheet.getLastRowNum(); i > endRow; i--) {
                    moveRow(sheet, i, i + shiftDistance);
                }
            } else {
                // 向上移动：从前向后移动
                for (int i = startRow; i <= sheet.getLastRowNum(); i++) {
                    moveRow(sheet, i, i + shiftDistance);
                }
            }


            // 4. 处理合并单元格
            handleMergedRegions(sheet, startRow, endRow, shiftDistance);

            // 5. 处理数据验证
            handleDataValidations(sheet, startRow, endRow, shiftDistance);
            // 3. 恢复行数据和样式
            for (Map.Entry<Integer, RowData> entry : rowDataMap.entrySet()) {
                int newRowNum = entry.getKey() + shiftDistance;
                Row newRow = sheet.getRow(newRowNum);
                if (newRow == null) {
                    newRow = sheet.createRow(newRowNum);
                }
                restoreRowData(newRow, entry.getValue());
            }


        } catch (Exception e) {
            log.error("Error shifting rows: " + e.getMessage(), e);
            throw new RuntimeException("Failed to shift rows", e);
        }
    }

    private static class RowData {
        Map<Integer, CellData> cellDataMap = new HashMap<>();
        short height;
    }

    private static class CellData {
        CellType cellType;
        Object value;
        CellStyle style;
    }

    private static RowData captureRowData(Row row) {
        RowData rowData = new RowData();
        rowData.height = row.getHeight();

        for (Cell cell : row) {
            CellData cellData = new CellData();
            cellData.cellType = cell.getCellType();
            cellData.style = cell.getCellStyle();

            switch (cell.getCellType()) {
                case STRING:
                    cellData.value = cell.getStringCellValue();
                    break;
                case NUMERIC:
                    cellData.value = cell.getNumericCellValue();
                    break;
                case BOOLEAN:
                    cellData.value = cell.getBooleanCellValue();
                    break;
                case FORMULA:
                    cellData.value = cell.getCellFormula();
                    break;
                default:
                    cellData.value = null;
            }

            rowData.cellDataMap.put(cell.getColumnIndex(), cellData);
        }
        return rowData;
    }

    private static void restoreRowData(Row row, RowData rowData) {
        row.setHeight(rowData.height);

        for (Map.Entry<Integer, CellData> entry : rowData.cellDataMap.entrySet()) {
            Cell cell = row.createCell(entry.getKey());
            CellData cellData = entry.getValue();

            cell.setCellStyle(cellData.style);

            if (cellData.value != null) {
                switch (cellData.cellType) {
                    case STRING:
                        cell.setCellValue((String) cellData.value);
                        break;
                    case NUMERIC:
                        cell.setCellValue((Double) cellData.value);
                        break;
                    case BOOLEAN:
                        cell.setCellValue((Boolean) cellData.value);
                        break;
                    case FORMULA:
                        cell.setCellFormula((String) cellData.value);
                        break;
                }
            }
        }
    }

    private static void moveRow(Sheet sheet, int sourceRowNum, int targetRowNum) {
        Row sourceRow = sheet.getRow(sourceRowNum);
        if (sourceRow == null) {
            return;
        }

        Row targetRow = sheet.getRow(targetRowNum);
        if (targetRow != null) {
            sheet.removeRow(targetRow);
        }
        targetRow = sheet.createRow(targetRowNum);

        // 复制行属性和单元格
        targetRow.setHeight(sourceRow.getHeight());
        for (Cell sourceCell : sourceRow) {
            Cell targetCell = targetRow.createCell(sourceCell.getColumnIndex());
            copyCell(sourceCell, targetCell);
        }

        // 删除源行
        sheet.removeRow(sourceRow);
    }

    private static void copyCell(Cell source, Cell target) {
        target.setCellStyle(source.getCellStyle());

        switch (source.getCellType()) {
            case STRING:
                target.setCellValue(source.getStringCellValue());
                break;
            case NUMERIC:
                target.setCellValue(source.getNumericCellValue());
                break;
            case BOOLEAN:
                target.setCellValue(source.getBooleanCellValue());
                break;
            case FORMULA:
                target.setCellFormula(source.getCellFormula());
                break;
            default:
                target.setBlank();
        }
    }


    private static void handleDataValidations(Sheet sheet, int startRow, int endRow, int shiftDistance) {
        if (!(sheet instanceof XSSFSheet)) {
            return;
        }
        XSSFSheet xssfSheet = (XSSFSheet) sheet;

        // 收集和调整数据验证
        List<DataValidation> newValidations = new ArrayList<>();
        List<DataValidation> validationsToRemove = new ArrayList<>();

        for (DataValidation validation : xssfSheet.getDataValidations()) {
            CellRangeAddressList addressList = validation.getRegions();
            boolean needsAdjustment = false;

            for (CellRangeAddress addr : addressList.getCellRangeAddresses()) {
                if (isValidationInRange(addr, startRow, endRow)) {
                    needsAdjustment = true;
                    break;
                }
            }

            if (needsAdjustment) {
                validationsToRemove.add(validation);
                DataValidation newValidation = createAdjustedValidation(validation, shiftDistance, xssfSheet.getDataValidationHelper());
                if (newValidation != null) {
                    newValidations.add(newValidation);
                }
            }
        }

        // 移除旧的数据验证
        for (DataValidation validation : validationsToRemove) {
            xssfSheet.getDataValidations().remove(validation);
        }

        // 添加新的数据验证
        for (DataValidation newValidation : newValidations) {
            xssfSheet.addValidationData(newValidation);
        }
    }

    /**
     * 删除指定范围内的合并单元格
     */
    private static void removeMergedRegions(Sheet sheet, int startRow, int endRow) {
        List<Integer> regionsToRemove = new ArrayList<>();

        // 从后向前遍历，以便安全删除
        for (int i = sheet.getNumMergedRegions() - 1; i >= 0; i--) {
            CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
            if (isValidationInRange(mergedRegion, startRow, endRow)) {
                regionsToRemove.add(i);
            }
        }

        // 删除找到的合并区域
        for (int index : regionsToRemove) {
            sheet.removeMergedRegion(index);
        }
    }


    // 在复制合并单元格之前，移除目标区域可能存在的合并区域
    private static void removeMergedRegionsInTargetArea(Sheet sheet, int startRow, int endRow, int shiftDistance) {
        for (int i = sheet.getNumMergedRegions() - 1; i >= 0; i--) {
            CellRangeAddress region = sheet.getMergedRegion(i);
            if (isRegionInTargetArea(region, startRow + shiftDistance, endRow + shiftDistance)) {
                sheet.removeMergedRegion(i);
            }
        }
    }

    private static void handleMergedRegions(Sheet sheet, int startRow, int endRow, int shiftDistance) {
        try {
            // 1. 使用Set来跟踪已处理的区域，避免重复
            Set<String> processedRegions = new HashSet<>();
            List<CellRangeAddress> newMergedRegions = new ArrayList<>();

            // 2. 获取所有现有的合并区域
            int numMergedRegions = sheet.getNumMergedRegions();
            for (int i = numMergedRegions - 1; i >= 0; i--) {
                CellRangeAddress region = sheet.getMergedRegion(i);
                if (isValidationInRange(region, startRow, endRow)) {
                    // 移除受影响的合并区域
                    sheet.removeMergedRegion(i);

                    // 计算新的合并区域位置
                    CellRangeAddress newRegion = new CellRangeAddress(region.getFirstRow() + shiftDistance, region.getLastRow() + shiftDistance, region.getFirstColumn(), region.getLastColumn());

                    // 创建区域的唯一标识
                    String regionKey = String.format("%d:%d:%d:%d", newRegion.getFirstRow(), newRegion.getLastRow(), newRegion.getFirstColumn(), newRegion.getLastColumn());

                    // 如果这个区域还没有处理过，添加到待处理列表
                    if (!processedRegions.contains(regionKey)) {
                        processedRegions.add(regionKey);
                        newMergedRegions.add(newRegion);
                    }
                }
            }

            // 3. 检查并移除目标位置可能存在的合并区域
            for (int i = sheet.getNumMergedRegions() - 1; i >= 0; i--) {
                CellRangeAddress region = sheet.getMergedRegion(i);
                if (isRegionInTargetArea(region, startRow + shiftDistance, endRow + shiftDistance)) {
                    sheet.removeMergedRegion(i);
                }
            }

            // 4. 添加新的合并区域
            for (CellRangeAddress newRegion : newMergedRegions) {
                try {
                    // 验证新区域是否有效
                    if (isValidMergedRegion(sheet, newRegion)) {
                        sheet.addMergedRegion(newRegion);
                    }
                } catch (Exception e) {
                    log.warn("Failed to add merged region: " + newRegion.formatAsString());
                }
            }

        } catch (Exception e) {
            log.error("Error handling merged regions: " + e.getMessage(), e);
        }
    }

    /**
     * 检查区域是否在目标区域范围内
     */
    private static boolean isRegionInTargetArea(CellRangeAddress region, int targetStartRow, int targetEndRow) {
        return (region.getFirstRow() >= targetStartRow && region.getFirstRow() <= targetEndRow) || (region.getLastRow() >= targetStartRow && region.getLastRow() <= targetEndRow) || (region.getFirstRow() <= targetStartRow && region.getLastRow() >= targetEndRow);
    }

    /**
     * 验证合并区域是否有效且不与现有区域重叠
     */
    private static boolean isValidMergedRegion(Sheet sheet, CellRangeAddress newRegion) {
        // 基本验证
        if (newRegion.getFirstRow() < 0 || newRegion.getLastRow() >= sheet.getLastRowNum() || newRegion.getFirstColumn() < 0 || newRegion.getLastColumn() >= 16384) { // Excel的最大列数
            return false;
        }

        // 检查是否与现有合并区域重叠
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            CellRangeAddress existing = sheet.getMergedRegion(i);
            if (newRegion.intersects(existing)) {
                return false;
            }
        }

        return true;
    }


    /**
     * 创建调整后的数据验证
     */
    private static DataValidation createAdjustedValidation(DataValidation sourceValidation, int shiftDistance, DataValidationHelper dvHelper) {
        try {
            // 1. 调整验证区域
            CellRangeAddressList originalRegions = sourceValidation.getRegions();
            CellRangeAddress[] originalAddresses = originalRegions.getCellRangeAddresses();
            List<CellRangeAddress> newAddresses = new ArrayList<>();

            for (CellRangeAddress addr : originalAddresses) {
                CellRangeAddress newAddr = new CellRangeAddress(addr.getFirstRow() + shiftDistance, addr.getLastRow() + shiftDistance, addr.getFirstColumn(), addr.getLastColumn());
                newAddresses.add(newAddr);
            }

            CellRangeAddressList newRegions = new CellRangeAddressList();
            for (CellRangeAddress addr : newAddresses) {
                newRegions.addCellRangeAddress(addr);
            }

            // 2. 创建新的验证约束
            DataValidationConstraint constraint = null;
            DataValidationConstraint sourceConstraint = sourceValidation.getValidationConstraint();

            if (sourceConstraint != null) {
                if (sourceConstraint.getExplicitListValues() != null) {
                    // 处理显式列表值
                    constraint = dvHelper.createExplicitListConstraint(sourceConstraint.getExplicitListValues());
                } else if (sourceConstraint.getFormula1() != null) {
                    // 处理公式类型的验证
                    String adjustedFormula = adjustFormulas(sourceConstraint.getFormula1(), shiftDistance);

                    switch (sourceConstraint.getValidationType()) {
                        case ValidationType.LIST:
                            constraint = dvHelper.createFormulaListConstraint(adjustedFormula);
                            break;
                        case ValidationType.DECIMAL:
                            constraint = dvHelper.createNumericConstraint(ValidationType.DECIMAL, sourceConstraint.getOperator(), adjustedFormula, sourceConstraint.getFormula2() != null ? adjustFormulas(sourceConstraint.getFormula2(), shiftDistance) : null);
                            break;
                        // 添加其他类型的处理...
                        default:
                            constraint = dvHelper.createFormulaListConstraint(adjustedFormula);
                    }
                }
            }

            // 3. 创建新的数据验证
            if (constraint != null) {
                DataValidation newValidation = dvHelper.createValidation(constraint, newRegions);

                // 4. 复制验证属性
                newValidation.setShowErrorBox(sourceValidation.getShowErrorBox());
                newValidation.setShowPromptBox(sourceValidation.getShowPromptBox());

                if (sourceValidation.getErrorBoxText() != null && sourceValidation.getErrorBoxTitle() != null) {
                    newValidation.createErrorBox(sourceValidation.getErrorBoxTitle(), sourceValidation.getErrorBoxText());
                }

                if (sourceValidation.getPromptBoxText() != null && sourceValidation.getPromptBoxTitle() != null) {
                    newValidation.createPromptBox(sourceValidation.getPromptBoxTitle(), sourceValidation.getPromptBoxText());
                }

                return newValidation;
            }

            return null;
        } catch (Exception e) {
            log.error("Error creating adjusted validation: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 调整公式中的行引用
     */
    private static String adjustFormulas(String formula, int shiftDistance) {
        if (formula == null || formula.isEmpty()) {
            return formula;
        }

        try {
            // 简单的公式调整，根据需要可以进行更复杂的处理
            if (formula.startsWith("=")) {
                formula = formula.substring(1);
            }

            // 处理绝对引用和相对引用
            StringBuilder adjustedFormula = new StringBuilder();
            int start = 0;
            while (start < formula.length()) {
                char c = formula.charAt(start);
                if (Character.isLetter(c)) {
                    // 找到列引用的结束位置
                    int colEnd = start + 1;
                    while (colEnd < formula.length() && Character.isLetter(formula.charAt(colEnd))) {
                        colEnd++;
                    }

                    // 找到行号的结束位置
                    int rowEnd = colEnd;
                    while (rowEnd < formula.length() && Character.isDigit(formula.charAt(rowEnd))) {
                        rowEnd++;
                    }

                    if (rowEnd > colEnd) {
                        // 有行号，需要调整
                        String colRef = formula.substring(start, colEnd);
                        int rowNum = Integer.parseInt(formula.substring(colEnd, rowEnd));
                        rowNum += shiftDistance;

                        adjustedFormula.append(colRef).append(rowNum);
                        start = rowEnd;
                    } else {
                        // 没有行号，直接添加
                        adjustedFormula.append(c);
                        start++;
                    }
                } else {
                    // 非列引用字符，直接添加
                    adjustedFormula.append(c);
                    start++;
                }
            }

            return adjustedFormula.toString();
        } catch (Exception e) {
            log.error("Error adjusting formula: " + e.getMessage(), e);
            return formula;
        }
    }

    /**
     * 数据验证类型常量
     */
    private static class ValidationType {
        public static final int LIST = 3;
        public static final int DECIMAL = 2;
        // 添加其他类型...
    }

    /**
     * 删除指定范围内的数据验证
     */
    public static void removeDataValidations(String filePath, int startRow, int endRow) {
        try (FileInputStream fis = new FileInputStream("C:\\Work\\随机化生成平台\\file\\随机化与研究药物分配申请表模板_随机+管药_队列研究_镜像替换_20241217模板 - 副本.xlsx"); XSSFWorkbook workbook = new XSSFWorkbook(fis)) {
            XSSFSheet sheet = workbook.getSheetAt(1);
            DataValidationHelper dvHelper = sheet.getDataValidationHelper();
            List<? extends DataValidation> validations = sheet.getDataValidations();
            for (DataValidation validation : validations) {
                if (sheet instanceof XSSFSheet) {
                    try {
                        // 1. 删除数据验证
                        removeXSSFDataValidations(sheet, 5, 46);

                        // 2. 清除单元格的数据验证属性
                        clearCellDataValidations(sheet, 5, 46);

                        // 3. 删除隐藏的数据验证列表
                        removeHiddenDataValidationLists(sheet.getWorkbook());

                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            } // 保存修改后的Excel文件。
            try (FileOutputStream fos = new FileOutputStream("C:\\Work\\随机化生成平台\\file\\随机化与研究药物分配申请表模板_随机+管药_队列研究_镜像替换_20241217模板 - 副本.xlsx")) {
                workbook.write(fos);
            }
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 删除XSSF数据验证
     */
    public static void removeXSSFDataValidations(XSSFSheet sheet, int startRow, int endRow) {
        // 获取所有数据验证
        List<XSSFDataValidation> dataValidations = sheet.getDataValidations();
        List<XSSFDataValidation> validationsToRemove = new ArrayList<>();

        // 找出需要删除的数据验证
        for (XSSFDataValidation validation : dataValidations) {
            CellRangeAddress[] ranges = validation.getRegions().getCellRangeAddresses();
            for (CellRangeAddress range : ranges) {
                if (isValidationInRange(range, startRow, endRow)) {
                    validationsToRemove.add(validation);
                    break;
                }
            }
        }

        // 删除找到的数据验证
        for (XSSFDataValidation validation : validationsToRemove) {
            sheet.getDataValidations().remove(validation);
        }
    }

    /**
     * 清除单元格的数据验证属性
     */
    public static void clearCellDataValidations(XSSFSheet sheet, int startRow, int endRow) {
        for (int rowNum = startRow; rowNum <= endRow; rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (row != null) {
                for (Cell cell : row) {
                    if (cell != null) {
                        // 清除单元格的数据验证
                        XSSFCell xssfCell = (XSSFCell) cell;
                        try {
                            // 设置单元格的数据验证为null
                            DataValidation validation = null;
                            xssfCell.setCellStyle(sheet.getWorkbook().createCellStyle());

                            // 如果单元格有公式，也清除它
                            if (cell.getCellType() == CellType.FORMULA) {
                                cell.setBlank();
                            }
                        } catch (Exception e) {
                            // 忽略单个单元格的错误，继续处理其他单元格
                            e.printStackTrace();
                        }
                    }
                }
            }
        }
    }

    /**
     * 删除隐藏的数据验证列表
     */
    /**
     * 删除隐藏的数据验证列表和名称范围
     */
    public static void removeHiddenDataValidationLists(XSSFWorkbook workbook) {
        try {
            // 1. 获取所有名称
            List<XSSFName> allNames = workbook.getAllNames();
            if (allNames == null || allNames.isEmpty()) {
                return;
            }

            // 2. 遍历并删除数据验证相关的名称
            Iterator<XSSFName> nameIterator = allNames.iterator();
            while (nameIterator.hasNext()) {
                XSSFName name = nameIterator.next();
                if (name != null) {
                    String nameName = name.getNameName();
                    // 检查是否是数据验证相关的名称
                    if (isValidationRelatedName(nameName)) {
                        try {
                            workbook.removeName(name);
                        } catch (Exception e) {
                            System.err.println("Failed to remove name: " + nameName);
                            e.printStackTrace();
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 检查名称是否与数据验证相关
     */
    public static boolean isValidationRelatedName(String name) {
        if (name == null) return false;

        // 转换为小写进行比较
        String lowerName = name.toLowerCase();

        // 检查各种可能的数据验证相关名称
        return lowerName.startsWith("_xlfn.") || lowerName.startsWith("_xlnm.") || lowerName.contains("_datavalidation") || lowerName.contains("validationlist") || lowerName.contains("dropdownlist") || lowerName.contains("validation") || lowerName.contains("dropdown") || lowerName.contains("list");
    }

    /**
     * 检查数据验证范围是否与指定行范围重叠
     */


    /**
     * 判断数据验证是否在指定行范围内
     */
    public static boolean isValidationInRange(CellRangeAddress range, int startRow, int endRow) {
        return (range.getFirstRow() >= startRow && range.getFirstRow() <= endRow) || (range.getLastRow() >= startRow && range.getLastRow() <= endRow) || (range.getFirstRow() <= startRow && range.getLastRow() >= endRow);
    }


    /**
     * 检查工作表保护并解除
     */
    public static void unprotectSheet(Sheet sheet) {
        if (sheet.getProtect()) {
            sheet.protectSheet(null); // 解除保护
        }
    }

    /**
     * 清除行中所有单元格的格式和内容
     */
// 或者方法3：先收集所有单元格，再删除
    public static void clearRowFormat(Row row, int startRowIndex, int endRowIndex) {
        if (row == null) return;
        Sheet sheet = row.getSheet();
        int rowIndex = row.getRowNum();
        //1.清楚数据验证
        convertValidationCellsToNormal(sheet, startRowIndex, endRowIndex);
        // 2. 清除合并区域
        removeMergedRegionsForRow(sheet, rowIndex);
        // 3. 清除单元格
        List<Cell> cellsToRemove = new ArrayList<>();
        for (Cell cell : row) {
            if (cell != null) {
                cellsToRemove.add(cell);
            }
        }

        for (Cell cell : cellsToRemove) {
            clearCellFormat(cell);
            row.removeCell(cell);
        }

        // 4. 重置行高
        row.setHeight((short) -1);
    }


    /**
     * 检查两个范围是否重叠
     */
    private static boolean isRangeOverlap(int start1, int end1, int start2, int end2) {
        return !(end1 < start2 || start1 > end2);
    }

    /**
     * 清除指定范围内的数据验证，转换为普通空单元格
     *
     * @param sheet    Excel工作表
     * @param startRow 开始行（包含）
     * @param endRow   结束行（包含）
     */
    public static void convertValidationCellsToNormal(Sheet sheet, int startRow, int endRow) {
        if (!(sheet instanceof XSSFSheet)) {
            return;
        }
        try {
            XSSFSheet xssfSheet = (XSSFSheet) sheet;
            List<DataValidation> validationsToRemove = new ArrayList<>();

            // 1. 收集并处理数据验证
            for (DataValidation validation : xssfSheet.getDataValidations()) {
                CellRangeAddressList regions = validation.getRegions();
                boolean needsRemoval = false;

                for (CellRangeAddress addr : regions.getCellRangeAddresses()) {
                    if (isRangeOverlap(addr.getFirstRow(), addr.getLastRow(), startRow, endRow)) {
                        needsRemoval = true;

                        // 清除单元格内容并设置为普通单元格
                        for (int rowNum = Math.max(startRow, addr.getFirstRow()); rowNum <= Math.min(endRow, addr.getLastRow()); rowNum++) {
                            Row row = sheet.getRow(rowNum);
                            if (row != null) {
                                for (int colNum = addr.getFirstColumn(); colNum <= addr.getLastColumn(); colNum++) {
                                    convertToNormalCell(row, colNum);
                                }
                            }
                        }
                    }
                }

                if (needsRemoval) {
                    validationsToRemove.add(validation);
                }
            }

            // 2. 移除数据验证
            for (DataValidation validation : validationsToRemove) {
                xssfSheet.getDataValidations().remove(validation);
            }

        } catch (Exception e) {
            log.error("Error converting validation cells: " + e.getMessage(), e);
            throw new RuntimeException("Failed to convert validation cells", e);
        }
    }

    /**
     * 将单个单元格转换为普通空单元格
     */
    private static void convertToNormalCell(Row row, int columnIndex) {
        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            cell = row.createCell(columnIndex);
        }

        // 1. 保存原始样式（如果需要）
        CellStyle originalStyle = cell.getCellStyle();

        // 2. 清除单元格内容
        cell.setBlank();

        // 3. 重新应用原始样式（保持格式，但去除数据验证）
        cell.setCellStyle(originalStyle);
    }


    /**
     * 清除指定行的合并区域
     */
    private static void removeMergedRegionsForRow(Sheet sheet, int rowIndex) {
        List<Integer> regionsToRemove = new ArrayList<>();

        // 从后向前遍历，以便安全删除
        for (int i = sheet.getNumMergedRegions() - 1; i >= 0; i--) {
            CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
            if (mergedRegion.getFirstRow() <= rowIndex && mergedRegion.getLastRow() >= rowIndex) {
                regionsToRemove.add(i);
            }
        }

        // 删除找到的合并区域
        for (int index : regionsToRemove) {
            sheet.removeMergedRegion(index);
        }
    }

    /**
     * 清除单元格格式和内容
     */
    public static void clearCellFormat(Cell cell) {
        if (cell == null) return;

        try {
            Workbook workbook = cell.getSheet().getWorkbook();

            // 1. 清除单元格样式
            CellStyle emptyStyle = workbook.createCellStyle();
            cell.setCellStyle(emptyStyle);

            // 2. 清除单元格内容
            cell.setBlank();

            // 3. 如果是公式单元格，移除公式
            if (cell.getCellType() == CellType.FORMULA) {
                cell.removeFormula();
            }

            // 4. 清除单元格注释
            if (cell.getCellComment() != null) {
                cell.removeCellComment();
            }

            // 5. 清除超链接
            if (cell instanceof XSSFCell) {
                XSSFCell xssfCell = (XSSFCell) cell;
                if (xssfCell.getHyperlink() != null) {
                    xssfCell.setHyperlink(null);
                }
            }
        } catch (Exception e) {
            log.error("Error clearing cell format: " + e.getMessage(), e);
        }
    }

    /**
     * 清除单元格的格式和内容
     */


    /**
     * 根据文本内容查找行号
     *
     * @param
     * @param searchText  要搜索的文本
     * @param columnIndex 指定搜索的列（如果知道文本在哪一列）
     * @return 找到的所有行号列表（0-based）
     */
    public static List<Integer> findRowsByText(String filePath, String searchText, Integer columnIndex) {
        try (FileInputStream fis = new FileInputStream(filePath); Workbook workbook = new XSSFWorkbook(fis)) {
            Sheet sheet = workbook.getSheet("随机化与研究药物分配申请表");
            List<Integer> foundRows = new ArrayList<>();

            try {
                // 获取最后一行的索引
                int lastRowNum = sheet.getLastRowNum();

                // 遍历所有行
                for (int rowNum = 0; rowNum <= lastRowNum; rowNum++) {
                    Row row = sheet.getRow(rowNum);
                    if (row != null) {
                        if (columnIndex != null) {
                            // 如果指定了列，只搜索该列
                            Cell cell = row.getCell(columnIndex);
                            if (isCellMatchText(cell, searchText)) {
                                foundRows.add(rowNum);
                            }
                        } else {
                            // 如果未指定列，搜索整行
                            for (Cell cell : row) {
                                if (isCellMatchText(cell, searchText)) {
                                    foundRows.add(rowNum);
                                    break; // 找到一个匹配就跳出当前行的搜索
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            // 保存更改
            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                workbook.write(fos);
                fis.close();
            }
            return foundRows;
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 检查单元格内容是否匹配搜索文本
     */
    private static boolean isCellMatchText(Cell cell, String searchText) {
        if (cell == null || searchText == null) {
            return false;
        }

        String cellValue = "";

        try {
            switch (cell.getCellType()) {
                case STRING:
                    cellValue = cell.getStringCellValue();
                    break;
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        cellValue = cell.getLocalDateTimeCellValue().toString();
                    } else {
                        cellValue = String.valueOf(cell.getNumericCellValue());
                    }
                    break;
                case BOOLEAN:
                    cellValue = String.valueOf(cell.getBooleanCellValue());
                    break;
                case FORMULA:
                    try {
                        cellValue = cell.getStringCellValue();
                    } catch (Exception e) {
                        cellValue = cell.getCellFormula();
                    }
                    break;
                default:
                    return false;
            }

            // 可以选择不同的匹配方式
            return cellValue.equals(searchText);   // 精确匹配


        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 复制数据验证和内容从一个单元格到另一个单元格
     *
     * @param filePath      Excel文件路径
     * @param sourceCellRef 源单元格引用（例如："D5"）
     * @param targetCellRef 目标单元格引用（例如："E5"）
     */
    public static void copyValidationAndContent(String filePath, String sourceCellRef, String targetCellRef) {
        try (FileInputStream fis = new FileInputStream(filePath); Workbook workbook = new XSSFWorkbook(fis)) {

            Sheet sheet = workbook.getSheetAt(1); // 获取第一个工作表
            if (sheet instanceof XSSFSheet) {
                XSSFSheet xssfSheet = (XSSFSheet) sheet;
                CellReference sourceRef = new CellReference(sourceCellRef);
                CellReference targetRef = new CellReference(targetCellRef);

                // 复制单元格内容
                Row sourceRow = sheet.getRow(sourceRef.getRow());
                Row targetRow = sheet.getRow(targetRef.getRow());
                if (targetRow == null) {
                    targetRow = sheet.createRow(targetRef.getRow());
                }
                Cell sourceCell = sourceRow.getCell(sourceRef.getCol());
                Cell targetCell = targetRow.createCell(targetRef.getCol());
                copyCellContent(sourceCell, targetCell);

                // 复制数据验证
                List<XSSFDataValidation> validations = xssfSheet.getDataValidations();
                for (DataValidation validation : validations) {
                    CellRangeAddressList regions = validation.getRegions();
                    for (CellRangeAddress addr : regions.getCellRangeAddresses()) {
                        if (addr.isInRange(sourceRef.getRow(), sourceRef.getCol())) {
                            // 创建新的数据验证区域
                            CellRangeAddressList newRegions = new CellRangeAddressList();
                            newRegions.addCellRangeAddress(new CellRangeAddress(targetRef.getRow(), targetRef.getRow(), targetRef.getCol(), targetRef.getCol()));

                            // 创建新的数据验证
                            DataValidationHelper dvHelper = sheet.getDataValidationHelper();
                            DataValidation newValidation = dvHelper.createValidation(validation.getValidationConstraint(), newRegions);
                            copyValidationProperties(validation, newValidation);
                            xssfSheet.addValidationData(newValidation);
                        }
                    }
                }
            }

            // 保存更改
            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Failed to copy validation and content", e);
        }
    }

    /**
     * 复制单元格内容
     */
    private static void copyCellContent(Cell sourceCell, Cell targetCell) {
        if (sourceCell == null || targetCell == null) return;

        targetCell.setCellStyle(sourceCell.getCellStyle());
        switch (sourceCell.getCellType()) {
            case STRING:
                targetCell.setCellValue(sourceCell.getStringCellValue());
                break;
            case NUMERIC:
                targetCell.setCellValue(sourceCell.getNumericCellValue());
                break;
            case BOOLEAN:
                targetCell.setCellValue(sourceCell.getBooleanCellValue());
                break;
            case FORMULA:
                targetCell.setCellFormula(sourceCell.getCellFormula());
                break;
            case BLANK:
                targetCell.setBlank();
                break;
            default:
                break;
        }
    }


    /**
     * 复制数据验证属性
     */
    private static void copyValidationProperties(DataValidation source, DataValidation target) {
        target.setShowErrorBox(source.getShowErrorBox());
        target.setShowPromptBox(source.getShowPromptBox());

        if (source.getErrorBoxTitle() != null && source.getErrorBoxText() != null) {
            target.createErrorBox(source.getErrorBoxTitle(), source.getErrorBoxText());
        }

        if (source.getPromptBoxTitle() != null && source.getPromptBoxText() != null) {
            target.createPromptBox(source.getPromptBoxTitle(), source.getPromptBoxText());
        }
    }


    /**
     * 根据单元格的文本内容清除文本和格式
     *
     * @param filePath   Excel文件路径
     * @param targetText 要匹配的文本内容
     */
    public static void clearCellsByText(String filePath, String targetText) {
        try (FileInputStream fis = new FileInputStream(filePath); Workbook workbook = new XSSFWorkbook(fis)) {

            Sheet sheet = workbook.getSheetAt(1); // 获取第一个工作表
            for (Row row : sheet) {
                for (Cell cell : row) {
                    if (cell.getCellType() == CellType.STRING && targetText.equals(cell.getStringCellValue())) {
                        clearCellContentAndStyle(cell);
                    }
                }
            }

            // 保存更改
            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                workbook.write(fos);
            }
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("Failed to clear cells by text", e);
        }
    }

    /**
     * 清除单元格的内容和格式
     */
    private static void clearCellContentAndStyle(Cell cell) {
        if (cell == null) return;

        // 清除内容
        cell.setBlank();

        Workbook workbook = cell.getSheet().getWorkbook();
        CellStyle originalStyle = cell.getCellStyle();

        // 创建一个新的单元格样式
        CellStyle newStyle = workbook.createCellStyle();
        newStyle.cloneStyleFrom(originalStyle);

        // 清除填充颜色
        newStyle.setFillPattern(FillPatternType.NO_FILL);

        // 应用新的样式到单元格
        cell.setCellStyle(newStyle);
    }


    public static void cancelBoundary(String filePath, String sourceCellRef, int sheetIndex) {
        try (FileInputStream fis = new FileInputStream(filePath); Workbook workbook = new XSSFWorkbook(fis)) {

            Sheet sheet = workbook.getSheetAt(sheetIndex); // 获取指定工作表
            if (sheet instanceof XSSFSheet) {
                XSSFSheet xssfSheet = (XSSFSheet) sheet;
                CellReference sourceRef = new CellReference(sourceCellRef);

                Row sourceRow = xssfSheet.getRow(sourceRef.getRow());
                if (sourceRow != null) {
                    Cell sourceCell = sourceRow.getCell(sourceRef.getCol());
                    if (sourceCell != null) {
                        // 获取单元格样式
                        CellStyle cellStyle = sourceCell.getCellStyle();

                        // 创建一个新的样式，取消边框
                        CellStyle newStyle = workbook.createCellStyle();
                        newStyle.cloneStyleFrom(cellStyle); // 复制原有样式
                        newStyle.setBorderTop(BorderStyle.NONE); // 取消上边框
                        newStyle.setBorderBottom(BorderStyle.NONE); // 取消下边框
                        newStyle.setBorderLeft(BorderStyle.NONE); // 取消左边框
                        newStyle.setBorderRight(BorderStyle.NONE); // 取消右边框

                        // 应用新样式到单元格
                        sourceCell.setCellStyle(newStyle);
                    } else {
                        System.out.println("指定的单元格不存在: " + sourceCellRef);
                    }
                } else {
                    System.out.println("指定的行不存在: " + sourceRef.getRow());
                }
            }

            // 保存更改
            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                workbook.write(fos);
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public static void dynamicLoadExcel(JSONObject object, String filePath) {
        //先删除
        List<Integer> rows4 = new ArrayList<>();
        List<Integer> rows2 = new ArrayList<>();
        List<Integer> rows5 = new ArrayList<>();
        List<Integer> rows6 = new ArrayList<>();
        List<Integer> rows7 = new ArrayList<>();

        if ((int) object.get("randMethod") == 0) {
            //有分层因素
            //删除分层参数
            rows2 = findRowsByText(filePath, "分层参数", 1);
            if (rows2.size() > 0) {
                int rowNum = rows2.get(0);
                log.info("Found '分层参数' in first column rows: " + rows2.get(0));
                log.info("删除了分层参数");
                //随机分配管理-子方案队列-分层参数删除
                com.hengrui.blind_back.utils.FileUtils.controlRowVisibility(filePath, rowNum, rowNum + 2);
            }
        }


        if ((int) object.get("randMethod") == 1) {
            //有分层因素
            //删除分层参数
            rows6 = findRowsByText(filePath, "哨兵组别比例", 1);
            if (rows6.size() > 0) {
                int rowNum = rows6.get(0);
                log.info("Found '哨兵组别比例' in first column rows: " + rows6.get(0));
                log.info("删除了哨兵组别比例");
                //随机分配管理-子方案队列-分层参数删除
                controlRowVisibility(filePath, rowNum, rowNum);
            }
        }

        //随机方法选择----区组随机
        if ((int) object.get("randMethod") == 2) {
            //有分层因素
            //删除分层参数
            rows6 = findRowsByText(filePath, "哨兵组别比例", 1);
            if (rows6.size() > 0) {
                int rowNum = rows6.get(0);
                log.info("Found '哨兵组别比例' in first column rows: " + rows6.get(0));
                log.info("删除了哨兵组别比例");
                //随机分配管理-子方案队列-分层参数删除
                controlRowVisibility(filePath, rowNum, rowNum);
            }

            //有分层因素
            //删除分层参数
            rows2 = findRowsByText(filePath, "分层参数", 1);
            if (rows2.size() > 0) {
                int rowNum = rows2.get(0);
                log.info("Found '分层参数' in first column rows: " + rows2.get(0));
                log.info("删除了分层参数");
                //随机分配管理-子方案队列-分层参数删除
                com.hengrui.blind_back.utils.FileUtils.controlRowVisibility(filePath, rowNum, rowNum + 2);
            }
        }


        if ((int) object.get("subjectReplace") == 1) {
            //倒序替换
            List<Integer> rows3 = findRowsByText(filePath, "镜像替换间隔", 1);
            if (rows3.size() > 0) {
                int rowNum3 = rows3.get(0);
                com.hengrui.blind_back.utils.FileUtils.controlRowVisibility(filePath, rowNum3, rowNum3);
                log.info("删除了镜像替换间隔");
            }
        } else if ((int) object.get("subjectReplace") == 2) {
            //镜像替换
            com.hengrui.blind_back.utils.FileUtils.clearCellsByText(filePath, "顺序区组数量(如有)");
            log.info("删除了顺序区组数量(如有)");
        } else if ((int) object.get("subjectReplace") == 3) {
            //不替换
            List<Integer> rows3 = findRowsByText(filePath, "镜像替换间隔", 1);
            if (rows3.size() > 0) {
                int rowNum3 = rows3.get(0);
                com.hengrui.blind_back.utils.FileUtils.controlRowVisibility(filePath, rowNum3, rowNum3);
            }
            com.hengrui.blind_back.utils.FileUtils.clearCellsByText(filePath, "顺序区组数量(如有)");
            log.info("删除了镜像替换间隔和顺序区组数量(如有)");
        }


        //二次随机是否
        if ((int) object.get("secondRand") == 0) {
            //删除二次随机
            rows2 = findRowsByText(filePath, "二次随机号设计1", 1);
            if (rows2.size() > 0) {
                int rowNum = rows2.get(0);
                log.info("Found '二次随机号设计1' in first column rows: " + rows2.get(0));
                //二次随机删除
                com.hengrui.blind_back.utils.FileUtils.controlRowVisibility(filePath, rowNum, rowNum + 17);
                log.info("删除了二次随机模块");
            }

        }

        //二次随机分层因素-是否
        if ((int) object.get("secondRandLevel") == 0 && (int) object.get("secondRand") == 1) {
            //删除分层参数
            rows2 = findRowsByText(filePath, "二次随机分层参数", null);
            if (rows2.size() > 0) {
                int rowNum = rows2.get(0);
                log.info("Found '二次随机分层参数' in first column rows: " + rows2.get(0));
                log.info("删除了分层参数");
                //二次随机分层因素-分层参数删除
                com.hengrui.blind_back.utils.FileUtils.controlRowVisibility(filePath, rowNum, rowNum + 2);
            }

        }

        //是否按一次随机组别分层：否
        if ((int) object.get("firstRandGroup") == 0 && (int) object.get("secondRand") == 1) {
            rows2 = findRowsByText(filePath, "二次随机号设计", 1);
            if (rows2.size() > 0) {
                int rowNum = rows2.get(0);
                log.info("Found '二次随机号设计' in first column rows: " + rows2.get(0));
                com.hengrui.blind_back.utils.FileUtils.controlRowVisibility(filePath, rowNum, rowNum + 6);
            }
        } else if ((int) object.get("firstRandGroup") == 1 && (int) object.get("secondRand") == 1 && (int) object.get("secondRandLevel") == 0) {
            //是否按一次随机组别分层：是
            rows2 = findRowsByText(filePath, "二次随机号设计1", 1);
            if (rows2.size() > 0) {
                int rowNum = rows2.get(0);
                log.info("Found '二次随机号设计1' in first column rows: " + rows2.get(0));
                //随机分配管理-子方案队列-分层参数删除
                com.hengrui.blind_back.utils.FileUtils.controlRowVisibility(filePath, rowNum, rowNum + 7);
            }

        } else if ((int) object.get("firstRandGroup") == 1 && (int) object.get("secondRand") == 1 && (int) object.get("secondRandLevel") == 1) {
            //是否按一次随机组别分层：是
            rows2 = findRowsByText(filePath, "二次随机号设计1", 1);
            if (rows2.size() > 0) {
                int rowNum = rows2.get(0);
                log.info("Found '二次随机号设计1' in first column rows: " + rows2.get(0));
                //随机分配管理-子方案队列-分层参数删除
                com.hengrui.blind_back.utils.FileUtils.controlRowVisibility(filePath, rowNum, rowNum + 10);
            }

        }

        //将数据验证加上-随即方法
        List<Integer> rmRow = new ArrayList<>();
        List<Integer> qzRow = new ArrayList<>();
        List<Integer> rrRow = new ArrayList<>();
        List<Integer> rfRow = new ArrayList<>();
        List<Integer> medRow = new ArrayList<>();
        //子方案队列
        rows4 = findRowsByText(filePath, "子方案/队列1", 1);
        if (rows4.size() > 0) {
            int rowNum4 = rows4.get(0);
            int levelCopy = (int) object.get("levelNum") - 1;//分层参数的复制的次数
            int twiceRandCopy = (int) object.get("queueNum") - 1;//子方案队列复制的次数
            //子方案队列-分层参数 没被删除
            if ((int) object.get("randMethod") == 1) {
                if ((int) object.get("subjectReplace") == 1 || (int) object.get("subjectReplace") == 3) {
                    copyAndPasteContent(filePath, 1, rowNum4 + 9, rowNum4 + 9, levelCopy);
                    copyAndPasteContent(filePath, 1, rowNum4, (rowNum4 + 9) + levelCopy, twiceRandCopy);
                } else {
                    copyAndPasteContent(filePath, 1, rowNum4 + 10, rowNum4 + 10, levelCopy);
                    copyAndPasteContent(filePath, 1, rowNum4, (rowNum4 + 10) + levelCopy, twiceRandCopy);
                }

            } else if ((int) object.get("randMethod") == 0) {
                if ((int) object.get("subjectReplace") == 1 || (int) object.get("subjectReplace") == 3) {
                    //子方案队列-分层参数 被删除
                    copyAndPasteContent(filePath, 1, rowNum4, rowNum4 + 7, twiceRandCopy);
                } else {
                    //子方案队列-分层参数 被删除
                    copyAndPasteContent(filePath, 1, rowNum4, rowNum4 + 8, twiceRandCopy);
                }
            } else if ((int) object.get("randMethod") == 2) {
                if ((int) object.get("subjectReplace") == 1 || (int) object.get("subjectReplace") == 3) {
                    //子方案队列-分层参数 被删除
                    copyAndPasteContent(filePath, 1, rowNum4, rowNum4 + 6, twiceRandCopy);
                } else {
                    //子方案队列-分层参数 被删除
                    copyAndPasteContent(filePath, 1, rowNum4, rowNum4 + 7, twiceRandCopy);
                }
            }
        }

        rows5 = findRowsByText(filePath, "二次随机号设计1", 1);
        if (rows5.size() > 0) {
            int rowNum5 = rows5.get(0);
            int levelCopy = (int) object.get("secondRandLevelNum") - 1;//二次分层参数的复制的次数
            int twiceRandCopy = (int) object.get("secondRandDesignType") - 1;//二次随机号设计类型
            //二次随机分层参数 没被删除
            if ((int) object.get("secondRandLevel") == 1 && (int) object.get("firstRandGroup") == 0 && (int) object.get("secondRand") == 1) {
                com.hengrui.blind_back.utils.FileUtils.copyAndPasteContent(filePath, 1, rowNum5 + 10, rowNum5 + 10, levelCopy);
                com.hengrui.blind_back.utils.FileUtils.copyAndPasteContent(filePath, 1, rowNum5, (rowNum5 + 10) + levelCopy, twiceRandCopy);
            } else if ((int) object.get("secondRandLevel") == 0 && (int) object.get("firstRandGroup") == 0 && (int) object.get("secondRand") == 1) {
                //二次随机分层参数 被删除
                com.hengrui.blind_back.utils.FileUtils.copyAndPasteContent(filePath, 1, rowNum5, rowNum5 + 7, twiceRandCopy);
            }
        }


        //药物供应管理
        rows5 = findRowsByText(filePath, "药物号设计1", 1);
        if (rows5.size() > 0 && ((int) object.get("rmdm") == 2 || (int) object.get("rmdm") == 3)) {
            int rowNum5 = rows5.get(0);
            int levelCopy = (int) object.get("medDesignNum") - 1;
            com.hengrui.blind_back.utils.FileUtils.copyAndPasteContent(filePath, 1, rowNum5, rowNum5 + 6, levelCopy);
            int isMedLevelNum = (int) object.get("isMedLevelNum");
            if (isMedLevelNum == 0) {
                clearCellsByText(filePath, "内层药物号后缀和药物名称");
            }
        }


        //处理数据验证下拉框
        //获取随机方法的所在行
        rmRow = findRowsByText(filePath, "随机方法", 1);
        //获取区组类型的所在行
        qzRow = findRowsByText(filePath, "区组类型", 4);
        //获取随机号规则的所在行
        rrRow = findRowsByText(filePath, "随机号规则", 7);
        //获取随机号格式的所在行
        rfRow = findRowsByText(filePath, "随机号格式", 1);
        //获取药物号规则的所在行
        medRow = findRowsByText(filePath, "药物号规则", 1);
        for (int row : rmRow) {
            copyValidationAndContent(filePath, "Z1", "D" + (row + 1));
        }
        for (int row : qzRow) {
            copyValidationAndContent(filePath, "Z2", "G" + (row + 1));
        }
        for (int row : rrRow) {
            copyValidationAndContent(filePath, "Z3", "J" + (row + 1));
        }
        for (int row : rfRow) {
            copyValidationAndContent(filePath, "Z4", "D" + (row + 1));
        }
        for (int row : medRow) {
            copyValidationAndContent(filePath, "Z5", "D" + (row + 1));
        }

        //取消边框
        cancelBoundary(filePath, "Z1", 1);
        cancelBoundary(filePath, "Z2", 1);
        cancelBoundary(filePath, "Z3", 1);
        cancelBoundary(filePath, "Z4", 1);
        cancelBoundary(filePath, "Z5", 1);
    }


    /**
     * 将多个 CSV 文件打包到一个 ZIP 文件中
     *
     * @param csvFiles      CSV 文件路径列表
     * @param outputZipFile 输出的 ZIP 文件路径
     * @throws IOException 如果发生 I/O 错误
     */
    public static void packCsvFilesToZip(List<String> csvFiles, String outputZipFile) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(outputZipFile); ZipOutputStream zipOut = new ZipOutputStream(fos)) {

            for (String csvFile : csvFiles) {
                File fileToZip = new File(csvFile);
                if (!fileToZip.exists()) {
                    System.err.println("文件不存在: " + csvFile);
                    continue;
                }

                try (FileInputStream fis = new FileInputStream(fileToZip)) {
                    // 创建 ZIP 条目
                    ZipEntry zipEntry = new ZipEntry(fileToZip.getName());
                    zipOut.putNextEntry(zipEntry);

                    // 将文件内容写入 ZIP
                    byte[] bytes = new byte[1024];
                    int length;
                    while ((length = fis.read(bytes)) >= 0) {
                        zipOut.write(bytes, 0, length);
                    }
                }
            }
        }
    }


    public static void convertExcelToPdf(String excelFilePath, String pdfOutputPath) throws IOException, DocumentException {
        // 加载 Excel 文件
        FileInputStream fileInputStream = new FileInputStream(excelFilePath);
        Workbook workbook = new XSSFWorkbook(fileInputStream);

        // 创建 PDF 文档
        Document document = new Document();
        PdfWriter.getInstance(document, new FileOutputStream(pdfOutputPath));
        document.open();

        // 遍历每个 Sheet
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            PdfPTable table = new PdfPTable(getMaxColumns(sheet));

            // 处理合并单元格
            handleMergedCells(sheet, table);

            // 遍历每一行
            for (Row row : sheet) {
                for (Cell cell : row) {
                    // 如果单元格已经被合并，跳过
                    if (isCellMerged(sheet, cell)) {
                        continue;
                    }

                    // 创建 PDF 单元格
                    PdfPCell pdfCell = new PdfPCell(new Phrase(getCellValueAsString(cell)));

                    // 设置单元格样式
                    pdfCell.setBorder(Rectangle.BOX); // 添加边框
                    pdfCell.setHorizontalAlignment(Element.ALIGN_CENTER); // 居中对齐

                    // 添加单元格到表格
                    table.addCell(pdfCell);
                }
            }

            // 将表格添加到 PDF 文档
            document.add(table);
            document.newPage(); // 每个 Sheet 生成一个新页面
        }

        // 关闭文档和工作簿
        document.close();
        workbook.close();
        fileInputStream.close();
    }

    // 获取 Sheet 的最大列数
    private static int getMaxColumns(Sheet sheet) {
        int maxColumns = 0;
        for (Row row : sheet) {
            if (row.getPhysicalNumberOfCells() > maxColumns) {
                maxColumns = row.getPhysicalNumberOfCells();
            }
        }
        return maxColumns;
    }

    // 获取单元格的值
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    // 处理合并单元格
    private static void handleMergedCells(Sheet sheet, PdfPTable table) {
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            CellRangeAddress region = sheet.getMergedRegion(i);
            int firstRow = region.getFirstRow();
            int lastRow = region.getLastRow();
            int firstCol = region.getFirstColumn();
            int lastCol = region.getLastColumn();

            // 获取合并区域的值
            Cell cell = sheet.getRow(firstRow).getCell(firstCol);
            String cellValue = getCellValueAsString(cell);

            // 创建 PDF 单元格
            PdfPCell pdfCell = new PdfPCell(new Phrase(cellValue));
            pdfCell.setColspan(lastCol - firstCol + 1); // 设置列跨度
            pdfCell.setRowspan(lastRow - firstRow + 1); // 设置行跨度
            pdfCell.setBorder(Rectangle.BOX); // 添加边框
            pdfCell.setHorizontalAlignment(Element.ALIGN_CENTER); // 居中对齐

            // 添加单元格到表格
            table.addCell(pdfCell);
        }
    }

    // 检查单元格是否属于合并区域
    private static boolean isCellMerged(Sheet sheet, Cell cell) {
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            CellRangeAddress region = sheet.getMergedRegion(i);
            if (region.isInRange(cell.getRowIndex(), cell.getColumnIndex())) {
                return true;
            }
        }
        return false;
    }

    // 提取 < > 中的内容（包含 < >）
    public static String extractInsideBrackets(String input) {
        Pattern pattern = Pattern.compile("<[^>]+>"); // 匹配 < > 中的内容
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            return matcher.group(); // 返回匹配的内容
        }
        return ""; // 如果没有匹配到，返回空字符串
    }

    // 提取 < > 外的文本
    public static String extractOutsideBrackets(String input) {
        Pattern pattern = Pattern.compile("<[^>]+>"); // 匹配 < > 中的内容
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            // 将 < > 中的内容替换为空字符串，得到 < > 外的文本
            return input.replace(matcher.group(), "").trim();
        }
        return ""; // 如果没有匹配到，返回原始字符串（去掉前后空格）
    }


    public static String extractDate(String fileName) {
        log.info("-----------------------提取文件名中的日期，文件名为：" + fileName + "-----------------------");
        // 正则表达式匹配日期格式：YYYY-MM-DD
        String regex = "\\d{4}-\\d{2}-\\d{2}";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
        java.util.regex.Matcher matcher = pattern.matcher(fileName);

        if (matcher.find()) {
            return matcher.group(); // 返回匹配的日期
        }
        return ""; // 未找到日期
    }


    public static String getCurrentTimeStr() {
        // 获取当前时间
        LocalDateTime currentTime = LocalDateTime.now();
        // 格式化时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        String formattedTime = currentTime.format(formatter);
        return formattedTime;
    }

    //添加签字文件水印
    public static void PDFAddWatermark(String inputFile, String outputFile, String waterMarkName, int textH, int textW, int fontAndSize, int R, int G, int B) {
        try {
            // 参数默认赋值
            if (textH == 0) {
                textH = 45;
            }
            if (textW == 0) {
                textW = 100;
            }
            if (fontAndSize == 0) {
                fontAndSize = 15;
            }
            if (R == 0) {
                R = 2;
            }
            if (G == 0) {
                G = 152;
            }
            if (B == 0) {
                G = 246;
            }
            // 间隔距离
            int interval = 30;
            PdfReader reader = new PdfReader(inputFile);
            PdfStamper stamper = new PdfStamper(reader, new FileOutputStream(outputFile));
            File fontFile = new File("/usr/share/fonts/simsun.ttc");
            if (!fontFile.exists()) {
                throw new FileNotFoundException("字体文件不存在: " + fontFile.getAbsolutePath());
            }
            // 设置文字水印样式
            URL fontPath = fontFile.toURI().toURL();
            if (fontPath == null) {
                throw new RuntimeException("字体文件未找到，请检查resources/font目录");
            }
            BaseFont base = BaseFont.createFont(fontPath.getFile() + ",0", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            // 设置字体颜色
            BaseColor baseColor = new BaseColor(R, G, B);
            PdfGState gs = new PdfGState();
            gs.setFillOpacity(0.8f);//改透明度
            gs.setStrokeOpacity(0.4f);
            int total = reader.getNumberOfPages() + 1;
            JLabel label = new JLabel();
            label.setText(waterMarkName);
            PdfContentByte under;
            // 添加多行文字水印
            Rectangle pageRect = null;
            label.setText(waterMarkName);
            for (int i = 1; i < total; i++) {
                pageRect = reader.getPageSizeWithRotation(i);
                // 在内容下方加水印
//                under = stamper.getUnderContent(i);
                under = stamper.getOverContent(i);
                under.saveState();
                under.setGState(gs);
                under.beginText();
                under.setFontAndSize(base, fontAndSize);
                under.setColorFill(baseColor);
                // 水印文字成30度角倾斜
                for (int height = interval + textH; height < pageRect.getHeight(); height = height + textH * 3) {
                    for (int width = interval + textW; width < pageRect.getWidth() + textW; width = width + textW * 2) {


                        under.showTextAligned(Element.ALIGN_LEFT, waterMarkName, width - textW, pageRect.getHeight() - (height - textH), 30);
                        under.setGState(gs);


                    }
                }


                // 添加水印文字
                under.endText();
            }
            stamper.close();
            reader.close();
        } catch (Exception e) {
            log.error("IntegrationFileUtil工具类文字水印添加异常" + e.getMessage());
        }
    }


    public static List<Integer> findAccountRowsByText(String filePath, String searchText, Integer columnIndex) {
        try (FileInputStream fis = new FileInputStream(filePath); Workbook workbook = new XSSFWorkbook(fis)) {
            Sheet sheet = workbook.getSheet("封面");
            List<Integer> foundRows = new ArrayList<>();

            try {
                // 获取最后一行的索引
                int lastRowNum = sheet.getLastRowNum();

                // 遍历所有行
                for (int rowNum = 0; rowNum <= lastRowNum; rowNum++) {
                    Row row = sheet.getRow(rowNum);
                    if (row != null) {
                        if (columnIndex != null) {
                            // 如果指定了列，只搜索该列
                            Cell cell = row.getCell(columnIndex);
                            if (isCellMatchText(cell, searchText)) {
                                foundRows.add(rowNum);
                            }
                        } else {
                            // 如果未指定列，搜索整行
                            for (Cell cell : row) {
                                if (isCellMatchText(cell, searchText)) {
                                    foundRows.add(rowNum);
                                    break; // 找到一个匹配就跳出当前行的搜索
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            // 保存更改
            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                workbook.write(fos);
                fis.close();
            }
            return foundRows;
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取指定行列的单元格内容
     *
     * @param filePath    Excel文件路径
     * @param sheetName   工作表名称
     * @param rowIndex    行索引
     * @param columnIndex 列索引
     * @return 单元格内容
     */
    public static String getCellContentByRowAndColumn(String filePath, String sheetName, int rowIndex, int columnIndex) {
        try (FileInputStream fis = new FileInputStream(filePath); Workbook workbook = new XSSFWorkbook(fis)) {

            Sheet sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                return "";
            }

            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                return "";
            }

            Cell cell = row.getCell(columnIndex);
            if (cell == null) {
                return "";
            }

            return getCellValueToString(cell);
        } catch (Exception e) {
            log.error("获取单元格内容失败: " + e.getMessage(), e);
            return "";
        }
    }

    /**
     * 获取单元格的值并转换为字符串
     */
    private static String getCellValueToString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getLocalDateTimeCellValue().toString();
                } else {
                    // 避免数值显示为科学计数法
                    return new BigDecimal(String.valueOf(cell.getNumericCellValue())).toPlainString();
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue();
                } catch (Exception e) {
                    try {
                        return String.valueOf(cell.getNumericCellValue());
                    } catch (Exception ex) {
                        return cell.getCellFormula();
                    }
                }
            default:
                return "";
        }
    }


    public static void extractFirstSheetByRemovingOthers(String sourceFilePath, String targetFilePath) {
        try (FileInputStream fis = new FileInputStream(sourceFilePath); Workbook workbook = new XSSFWorkbook(fis)) {

            // Keep track of the first sheet
            Sheet firstSheet = workbook.getSheetAt(0);
            String firstSheetName = firstSheet.getSheetName();

            // Remove all other sheets
            for (int i = workbook.getNumberOfSheets() - 1; i >= 0; i--) {
                if (i != 0) { // Skip the first sheet
                    workbook.removeSheetAt(i);
                }
            }

            // Write the modified workbook to the target file
            try (FileOutputStream fos = new FileOutputStream(targetFilePath)) {
                workbook.write(fos);
            }

            log.info("Successfully extracted first sheet '{}' to {}", firstSheetName, targetFilePath);
        } catch (IOException e) {
            log.error("Failed to extract first sheet: {}", e.getMessage());
            throw new RuntimeException("Failed to extract first sheet", e);
        }
    }


    public static String addSuffixToFileName(String filePath, String suffix) {
        if (filePath == null || filePath.isEmpty()) {
            return filePath;
        }

        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex == -1) {
            // No extension, just append the suffix
            return filePath + suffix;
        }

        // Insert suffix before the extension
        String basePath = filePath.substring(0, lastDotIndex);
        String extension = filePath.substring(lastDotIndex);

        return basePath + suffix + extension;
    }


    public static boolean combinePdfFiles(String firstPdfPath, String secondPdfPath, String outputPdfPath) {
        try {
            // Create readers for both PDFs
            PdfReader firstReader = new PdfReader(firstPdfPath);
            PdfReader secondReader = new PdfReader(secondPdfPath);

            // Create document and PdfCopy instance
            Document document = new Document();
            PdfCopy copy = new PdfCopy(document, new FileOutputStream(outputPdfPath));
            document.open();

            // Copy pages from first PDF
            int firstPdfPages = firstReader.getNumberOfPages();
            for (int i = 1; i <= firstPdfPages; i++) {
                PdfImportedPage page = copy.getImportedPage(firstReader, i);
                copy.addPage(page);
            }

            // Copy pages from second PDF
            int secondPdfPages = secondReader.getNumberOfPages();
            for (int i = 1; i <= secondPdfPages; i++) {
                PdfImportedPage page = copy.getImportedPage(secondReader, i);
                copy.addPage(page);
            }

            // Close everything
            document.close();
            firstReader.close();
            secondReader.close();

            log.info("Successfully combined PDFs: {} and {} into {}", firstPdfPath, secondPdfPath, outputPdfPath);
            return true;
        } catch (Exception e) {
            log.error("Failed to combine PDF files: {}", e.getMessage(), e);
            return false;
        }
    }


    public static String downloadFile(String fileUrl, String localFilePath) throws UnsupportedEncodingException, NoSuchAlgorithmException {
        //设置可通过ip地址访问https请求
        HttpsURLConnection.setDefaultHostnameVerifier(new NullHostNameVerifier());
        TrustManager[] tm = {new MyX509TrustManager()};
        SSLContext sslContext = SSLContext.getInstance("TLS");
        try {
            sslContext.init(null, tm, new java.security.SecureRandom());
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        }
        // 从上述SSLContext对象中得到SSLSocketFactory对象
        SSLSocketFactory ssf = sslContext.getSocketFactory();
        String urlStr = fileUrl;
        URL url = null;
        try {
            url = new URL(urlStr);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
        HttpsURLConnection con = null;
        try {
            con = (HttpsURLConnection) url.openConnection();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        con.setSSLSocketFactory(ssf);
        try {
            con.setRequestMethod("GET"); // 设置以POST方式提交数据
        } catch (ProtocolException e) {
            throw new RuntimeException(e);
        }
        con.setDoInput(true); // 打开输入流，以便从服务器获取数据
        con.setDoOutput(true);// 打开输出流，以便向服务器提交数据
        //设置发送参数
        PrintWriter out = null;
        try {
            out = new PrintWriter(new OutputStreamWriter(con.getOutputStream(), "UTF-8"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        out.flush();
        out.close();
        try {
            con.getInputStream();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        OutputStream os = null;
        try {
            String path = localFilePath;
            // 2、保存到临时文件
            // 1K的数据缓冲
            byte[] bs = new byte[2048];
            // 读取到的数据长度
            int len;
            // 输出的文件流保存到本地文件
            File tempFile = new File(path);
            os = new FileOutputStream(tempFile.getPath());
            // 开始读取
            while ((len = con.getInputStream().read(bs)) != -1) {
                os.write(bs, 0, len);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 完毕，关闭所有链接
            try {
                os.close();
                con.getInputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return localFilePath;
    }


    public static String downloadRTSMGenFile(String fileUrl, String localFilePath) throws UnsupportedEncodingException, NoSuchAlgorithmException {
        //设置可通过ip地址访问https请求
        HttpsURLConnection.setDefaultHostnameVerifier(new NullHostNameVerifier());
        TrustManager[] tm = {new MyX509TrustManager()};
        SSLContext sslContext = SSLContext.getInstance("TLS");
        try {
            sslContext.init(null, tm, new java.security.SecureRandom());
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        }
        // 从上述SSLContext对象中得到SSLSocketFactory对象
        SSLSocketFactory ssf = sslContext.getSocketFactory();
        String urlStr = fileUrl;
        URL url = null;
        try {
            url = new URL(urlStr);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
        HttpsURLConnection con = null;
        try {
            con = (HttpsURLConnection) url.openConnection();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        con.setSSLSocketFactory(ssf);
        try {
            con.setRequestMethod("GET"); // 设置GET方法
        } catch (ProtocolException e) {
            throw new RuntimeException(e);
        }
        con.setDoInput(true); // 打开输入流，以便从服务器获取数据
        // Remove these lines as they're not needed for GET requests
        // con.setDoOutput(true);
        // PrintWriter out = null;
        // try {
        //     out = new PrintWriter(new OutputStreamWriter(con.getOutputStream(), "UTF-8"));
        // } catch (IOException e) {
        //     throw new RuntimeException(e);
        // }
        // out.flush();
        // out.close();

        try {
            // Read the response and save to file
            try (InputStream in = con.getInputStream(); FileOutputStream fos = new FileOutputStream(localFilePath)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
            }
            return localFilePath;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


}










