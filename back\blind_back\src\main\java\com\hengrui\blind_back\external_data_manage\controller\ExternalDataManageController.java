package com.hengrui.blind_back.external_data_manage.controller;

import com.hengrui.blind_back.ecrf_transfer.controller.ECRFTransferController;
import com.hengrui.blind_back.external_data_manage.service.ExternalDataManageService;
import com.hengrui.blind_back.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName ExternalDataManageController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/13 11:10
 * @Version 1.0
 **/
@RestController
@Slf4j
public class ExternalDataManageController {


    @Autowired
    ExternalDataManageService externalDataManageService;

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getEDMTrainRecord")

    public Map<String, Object> getEDMTrainRecord(String taskId,
                                                   String server,
                                                   String projectId) {
        ExternalDataManageController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, Object> results = externalDataManageService.getEDMTrainRecord(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", results);
        return result;

    }
}
