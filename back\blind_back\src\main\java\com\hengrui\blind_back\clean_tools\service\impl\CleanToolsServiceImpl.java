package com.hengrui.blind_back.clean_tools.service.impl;

/**
 * @ClassName CleanToolsServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/9/20 15:46
 * @Version 1.0
 **/

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hengrui.blind_back.blind.mapper.EDMCDTMSInfoMapper;
import com.hengrui.blind_back.blind.utils.Decode64Util;
import com.hengrui.blind_back.blind.utils.MinioUtil;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import com.hengrui.blind_back.clean_tools.service.CleanToolsService;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.ecrf_unlock.utils.FileUtil;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.hengrui.blind_back.utils.CallPython;
import com.hengrui.blind_back.utils.FileUtils;
import com.hengrui.blind_back.utils.SubmitSAS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Slf4j
@Service
public class CleanToolsServiceImpl implements CleanToolsService {

    @Autowired
    private SubmitSAS submitSAS;

    @Autowired
    MinioUtil minioUtil;


    @Autowired
    CallPython callPython;

    @Autowired

    Decode64Util decode64Util;

    @Autowired
    CDTMSAPI cdtmsapi;

    @Override
    public Map<String, String> submitToRecistSAS(String taskId, String projectId) {
        Map<String, String> result=new HashMap<>();
        List<Map<String, String>> uploadFilesFromEDC = new ArrayList<>();
        Map<String,String> fileObject1 = new HashMap<>();
        fileObject1.put("fid", "edc_dataset");
        fileObject1.put("fileType", ".zip");
        uploadFilesFromEDC.add(fileObject1);
        Map<String, String> ENVInfo = new HashMap<>();

        //1.拿到页面的参数，studyId ,核查内容，肿瘤影像学选项
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        String uuid = ULIDGenerator.generateULID();
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);

        //获取als同步到minio
        minioUtil.uploadALSUATFile(ENVInfo,studyId,taskId,formId,projectId);

        ENVInfo.put("ENV", "UAT");
        ENVInfo.put("data_type", "data_set");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", taskId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", projectId);
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("data_format", "SAS");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        ENVInfo.put("isLatest", "");
        ENVInfo.put("fileSuffix", "a".toString());
        Map<String,String> pyResult=new HashMap<>();
        pyResult=callPython.downloadEDCServerFile(ENVInfo, uploadFilesFromEDC);
        String fileName = pyResult.get("original_name");
        String dataIsTwoDays = pyResult.get("dataIsTwoDays");
        if(dataIsTwoDays.equals("否")){
            result.put("dataIsTwoDays","数据是否在两天之内："+dataIsTwoDays);
            return result;
        }
        ENVInfo.put("taskId", taskId);
        ENVInfo.put("projectId", projectId);
        ENVInfo.put("isLatest", "");
        ENVInfo.put("ENV", "");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        ENVInfo.put("data_type", "cleanTool");
        //获取语言选项
        String used_language = CDTMSAPI.getStudyLanguage(studyId);
        //json参数
        String data = formInfo.get("param");
        String isAdd = "";
        String  customcode= "";
        String sascodeStr="";
        String parmStr="";
        if (!data.isEmpty()) {
            cn.hutool.json.JSONObject formInfos = new cn.hutool.json.JSONObject(data);
            isAdd = formInfos.get("isadd").toString();
            customcode = formInfos.get("customcode").toString();
            parmStr=FileUtils.extractInsideBrackets(customcode);
            sascodeStr= FileUtils.extractOutsideBrackets(customcode);
        }
        JSONObject param = new JSONObject();
        param.put("isadd", isAdd);
        param.put("system", "HRTAU4");
        param.put("parm", parmStr);
        param.put("sascode", sascodeStr);
        String paramStr = param.toString();
        CleanToolsServiceImpl.log.info("需要传递的参数为:"+paramStr);
        //2.参数放入json，上传到minio
         uuid = ULIDGenerator.generateULID();
        String paramFileName = formInfo.get("studyId").toString() + "_Recist1.1_" + uuid + ".json";
        //2.1 submit to sas via json params and define the json path on the minio storage platform
        formInfo.put("jsonMinioPath", SASOnlieConstant.PREFIX_PRO_MINIO+"/sdv/json/" + paramFileName);
        formInfo.put("sasCodePath", SASOnlieConstant.SAS_RECIST_UAT_CODE_PATH);
        formInfo.put("paramFileName", paramFileName);
        formInfo.put("bucket", "sdv");
        formInfo.put("param", paramStr);
        formInfo.put("language", used_language);
        formInfo.put("uuid",uuid);
        //获取表单上传的数据集，并打上tag   example env:uat, key1:RAVE, key2:SHR-2010-201, key3:2024/09/27/13/17
        cdtmsapi.uploadSAS(studyId);
        //查询ecrf设计与搭建 uat审核版的文件，将该文件上传到minio doc目录下
        cdtmsapi.uploadDBS(studyId);
        //3.调用SAS接口，获取结果,将输出文件回填到cdtms
        List<Map<String, String>> sasOutputFilesInfo = new ArrayList<>();
        Map<String, String> sasOutputFile = new HashMap<>();
        sasOutputFile.put("fid", "output");
        String outputName = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.RECIST_SUFFIX;
        sasOutputFile.put("outputName", outputName);
        formInfo.put("outputName",outputName);
        sasOutputFilesInfo.add(sasOutputFile);
         result = submitSAS.submitToSAS(ENVInfo, uploadFilesFromEDC, formInfo, sasOutputFilesInfo);

        //获取数据集日期
        Map<String,String> tagInfo= minioUtil.getObjectTags("raw", formInfo.get("studyId").toString() + "_sas.zip");
        String date=tagInfo.get("key3");
        String EDCDate = FileUtil.formatDate(date);


        //核查程序日期
        LocalDate currentDate = LocalDate.now();

        // Define the desired format
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // Format the date as a string
        String sasCheckDate = currentDate.format(formatter);

        String dataId = "";
        if (!data.isEmpty()) {
            cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(data);
            cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();
            dataId = formInfoData.get("id").toString();
            cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
            temp.put("id", dataId);
            temp.put("edc_data_dt", EDCDate);
            temp.put("edc_dataset_t", fileName);
          //  temp.put("mr_plan_version_date", sasCheckDate);
            temp.put("manual_rev_prog_version", "Recist1.1");
            temp.put("crf_zt", "05");
            params.put("data", temp);
            String newFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
            params.put("formId", newFormId);
            params.put("taskId", taskId);
            params.put("projectId", projectId);
            CDTMSAPI.dataSave(params);
        }

        //upload the sas log report to cdtms API
        formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String logPath = result.get("sasLogPath").toString();
        File logFile = new File(logPath);
        FileUtil.uploadSASOutputFile(taskId, formId, "uat_log", logPath, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.REMOTE_SERVER_API_PREFIX, logFile.getName(), "log");
        result.put("dataIsTwoDays","数据是否在两天之内："+dataIsTwoDays);
        return result;
    }

    @Override
    public Map<String, String> submitToAEXOTSAS(String taskId, String projectId) {
        Map<String, String> result=new HashMap<>();
        List<Map<String, String>> uploadFilesFromEDC = new ArrayList<>();
        Map<String, String> ENVInfo = new HashMap<>();
        Map<String,String> fileObject1 = new HashMap<>();
        fileObject1.put("fid", "edc_dataset");
        fileObject1.put("fileType", ".zip");
        uploadFilesFromEDC.add(fileObject1);
        //1.拿到页面的参数，studyId ,核查内容，肿瘤影像学选项
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        //获取语言选项
        String used_language = CDTMSAPI.getStudyLanguage(studyId);

        //json参数
        String data = formInfo.get("param");
        String  customcode= "";
        String sascodeStr="";
        String parmStr="";

        if (!data.isEmpty()) {
            cn.hutool.json.JSONObject formInfos = new cn.hutool.json.JSONObject(data);
            customcode = formInfos.get("customcode").toString();
            parmStr=FileUtils.extractInsideBrackets(customcode);
            if (parmStr.isEmpty()){
                return new HashMap<>();
            }
            sascodeStr= FileUtils.extractOutsideBrackets(customcode);
        }
        JSONObject param = new JSONObject();
        param.put("parm", parmStr);
        param.put("sascode", sascodeStr);
        String paramStr = param.toString();
        //2.参数放入json，上传到minio
        String uuid = ULIDGenerator.generateULID();
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        //获取als同步到minio
        minioUtil.uploadALSUATFile(ENVInfo,studyId,taskId,formId,projectId);
        ENVInfo.put("ENV", "UAT");
        ENVInfo.put("data_type", "data_set");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", taskId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", projectId);
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("data_format", "SAS");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        ENVInfo.put("isLatest", "");
        ENVInfo.put("fileSuffix", "a".toString());
        Map<String,String> pyResult=new HashMap<>();
        pyResult=callPython.downloadEDCServerFile(ENVInfo, uploadFilesFromEDC);
        String fileName=pyResult.get("original_name");
        String dataIsTwoDays=pyResult.get("dataIsTwoDays");
        if(dataIsTwoDays.equals("否")){
            result.put("dataIsTwoDays","数据是否在两天之内："+dataIsTwoDays);
            return result;
        }
        if(fileName == null||fileName.isEmpty()){
            //没有下载到数据集，使用表单上传的附件，上传到minio raw下,tag  example env:uat, key1:RAVE, key2:SHR-2010-201, key3:2024/09/27/13/17
            cdtmsapi.uploadSAS(studyId);
        }
         CleanToolsServiceImpl.log.info("获取的edc数据集的名称为：{}", fileName);
        String EDCTime = FileUtil.transferEDCFIleName(fileName);
        CleanToolsServiceImpl.log.info("获取的edc数据集的时间为：{}", EDCTime);

        String paramFileName = formInfo.get("studyId").toString() + "_AE-EX-EOT" + ".json";
        //2.1 submit to sas via json params and define the json path on the minio storage platform
        formInfo.put("jsonMinioPath", SASOnlieConstant.PREFIX_PRO_MINIO+"/sdv/json/" + paramFileName);
        formInfo.put("sasCodePath", SASOnlieConstant.SAS_AEOXT_UAT_CODE_PATH);
        formInfo.put("paramFileName", paramFileName);
        formInfo.put("bucket", "sdv");
        formInfo.put("param", paramStr);
        formInfo.put("language", used_language);
        formInfo.put("uuid",uuid);

        //查询ecrf设计与搭建 uat审核版的文件，将该文件上传到minio doc目录下

        cdtmsapi.uploadDBS(studyId);

        //3.调用SAS接口，获取结果,将输出文件回填到cdtms
        List<Map<String, String>> sasOutputFilesInfo = new ArrayList<>();
        Map<String, String> sasOutputFile = new HashMap<>();
        sasOutputFile.put("fid", "output");

        String outputName = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.AEOXT;
        sasOutputFile.put("outputName", outputName);
        formInfo.put("outputName", outputName);
        sasOutputFilesInfo.add(sasOutputFile);
        result = submitSAS.submitToSAS(ENVInfo, uploadFilesFromEDC, formInfo, sasOutputFilesInfo);



        //核查程序日期
        LocalDate currentDate = LocalDate.now();

        // Define the desired format
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // Format the date as a string
        String sasCheckDate = currentDate.format(formatter);



        String dataId = "";
        if (!data.isEmpty()) {
            cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(data);
            cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();
            dataId = formInfoData.get("id").toString();
            cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
            temp.put("id", dataId);
            temp.put("edc_data_dt", EDCTime);
            temp.put("edc_dataset_t", fileName);
           // temp.put("mr_plan_version_date", sasCheckDate);
            temp.put("manual_rev_prog_version", "AE-EX-EOT");
            temp.put("crf_zt", "05");
            params.put("data", temp);
            formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
            params.put("formId", formId);
            params.put("taskId", taskId);
            params.put("projectId", projectId);
            CDTMSAPI.dataSave(params);
        }


        //upload the sas log report to cdtms API
         formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String logPath = result.get("sasLogPath").toString();
        File logFile = new File(logPath);
        FileUtil.uploadSASOutputFile(taskId, formId, "uat_log", logPath, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.REMOTE_SERVER_API_PREFIX, logFile.getName(), "log");
        result.put("dataIsTwoDays","数据是否在两天之内："+dataIsTwoDays);
        return result;
    }




    @Autowired
    EDMCDTMSInfoMapper edmcdtmsInfoMapper;
    @Override
    public Map<String, String> submitToLabAESAS(String taskId, String projectId) {
        Map<String, String> result=new HashMap<>();
        List<Map<String, String>> uploadFilesFromEDC = new ArrayList<>();
        Map<String, String> ENVInfo = new HashMap<>();
        Map<String,String> fileObject1 = new HashMap<>();
        fileObject1.put("fid", "edc_dataset");
        fileObject1.put("fileType", ".zip");
        uploadFilesFromEDC.add(fileObject1);
        //1.拿到页面的参数，studyId ,核查内容，肿瘤影像学选项
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        String data=formInfo.get("param").toString();
        //获取语言选项
        String used_language = CDTMSAPI.getStudyLanguage(studyId);
        //json参数
        JSONObject param = new JSONObject();
        String edcName = edmcdtmsInfoMapper.getEDCName(studyId);
        if(!ObjectUtils.isEmpty(edcName)&&!edcName.isEmpty()){
            if (edcName.equals("TAU")) {
                param.put("system", "HRTAU4");
            } else if (edcName.equals("Rave")) {
                param.put("system", "RAVE");
            }
        }
        String sascodeStr="";
        String parmStr="";
        String  customcode="";
        if (!data.isEmpty()) {
            cn.hutool.json.JSONObject formInfos = new cn.hutool.json.JSONObject(data);
            customcode = formInfos.get("customcode").toString();
            parmStr=FileUtils.extractInsideBrackets(customcode);
            sascodeStr= FileUtils.extractOutsideBrackets(customcode);
        }

        param.put("parm", parmStr);
        param.put("sascode", sascodeStr);
        String paramStr = param.toString();
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        //获取als同步到minio
        minioUtil.uploadALSUATFile(ENVInfo,studyId,taskId,formId,projectId);


        //2.参数放入json，上传到minio
        String  uuid = ULIDGenerator.generateULID();

        ENVInfo.put("ENV", "UAT");
        ENVInfo.put("data_type", "data_set");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", taskId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", projectId);
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("data_format", "SAS");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        ENVInfo.put("isLatest", "");
        ENVInfo.put("fileSuffix", "a".toString());
        Map<String,String> pyResult=callPython.downloadEDCServerFile(ENVInfo, uploadFilesFromEDC);
        String fileName = pyResult.get("original_name");
        String dataIsTwoDays = pyResult.get("dataIsTwoDays");
        if(dataIsTwoDays.equals("否")){
            result.put("dataIsTwoDays","数据是否在两天之内："+dataIsTwoDays);
            return result;
        }
        if(fileName == null||fileName.isEmpty()){
            //没有下载到数据集，使用表单上传的附件，上传到minio raw下,tag  example env:uat, key1:RAVE, key2:SHR-2010-201, key3:2024/09/27/13/17
            cdtmsapi.uploadSAS(studyId);
        }
        CleanToolsServiceImpl.log.info("获取的edc数据集的名称为：{}", fileName);
        String EDCTime = FileUtil.transferEDCFIleName(fileName);
        CleanToolsServiceImpl.log.info("获取的edc数据集的时间为：{}", EDCTime);

        String paramFileName = formInfo.get("studyId").toString() + "_LAB-AE" + ".json";
        //2.1 submit to sas via json params and define the json path on the minio storage platform
        formInfo.put("jsonMinioPath", SASOnlieConstant.PREFIX_PRO_MINIO+"/sdv/json/" + paramFileName);
        formInfo.put("sasCodePath", SASOnlieConstant.SAS_LABAE_CODE_PATH);
        formInfo.put("paramFileName", paramFileName);
        formInfo.put("bucket", "sdv");
        formInfo.put("param", paramStr);
        formInfo.put("language", used_language);
        formInfo.put("uuid",uuid);

        //查询ecrf设计与搭建 uat审核版的文件，将该文件上传到minio doc目录下

        //cdtmsapi.uploadDBS(studyId);

        //3.调用SAS接口，获取结果,将输出文件回填到cdtms
        List<Map<String, String>> sasOutputFilesInfo = new ArrayList<>();
        Map<String, String> sasOutputFile = new HashMap<>();
        sasOutputFile.put("fid", "output");

        String outputName = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.LAB_AE;
        sasOutputFile.put("outputName", outputName);
        formInfo.put("outputName", outputName);
        sasOutputFilesInfo.add(sasOutputFile);
         result = submitSAS.submitToSAS(ENVInfo, uploadFilesFromEDC, formInfo, sasOutputFilesInfo);



        //核查程序日期
        LocalDate currentDate = LocalDate.now();

        // Define the desired format
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // Format the date as a string
        String sasCheckDate = currentDate.format(formatter);



        String dataId = "";
        if (!data.isEmpty()) {
            cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(data);
            cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();
            dataId = formInfoData.get("id").toString();
            cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
            temp.put("id", dataId);
            temp.put("edc_data_dt", EDCTime);
            temp.put("edc_dataset_t", fileName);
           // temp.put("mr_plan_version_date", sasCheckDate);
            temp.put("manual_rev_prog_version", "LAB-AE");
            temp.put("crf_zt", "05");
            params.put("data", temp);
            formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
            params.put("formId", formId);
            params.put("taskId", taskId);
            params.put("projectId", projectId);
            CDTMSAPI.dataSave(params);
        }

        //upload the sas log report to cdtms API
        formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String logPath = result.get("sasLogPath").toString();
        File logFile = new File(logPath);
        FileUtil.uploadSASOutputFile(taskId, formId, "uat_log", logPath, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.REMOTE_SERVER_API_PREFIX, logFile.getName(), "log");
        result.put("dataIsTwoDays","数据是否在两天之内："+dataIsTwoDays);
        return result;
    }
}
