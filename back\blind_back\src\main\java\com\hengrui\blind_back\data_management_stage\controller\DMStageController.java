package com.hengrui.blind_back.data_management_stage.controller;

import com.hengrui.blind_back.data_management_stage.service.DMStageService;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.hengrui.blind_back.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName DMStageController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/9/11 9:42
 * @Version 1.0
 **/

@RestController
@Slf4j
public class DMStageController {


    @Autowired
    DMStageService dmStageService;


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/submitToDMStageSAS")

    public Map<String, Object> submitToUATSAS(String taskId,
                                              String server,
                                              String projectId) {
        DMStageController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> results = dmStageService.submitToDMStageSAS(taskId, projectId);
        CDTMSAPI.updateWorkFlowStatus(taskId,projectId,"status","10");
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", results);
        return result;
    }

    //getProgressReport
    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getProgressReport")

    public Map<String, Object> getProgressReport(String taskId,
                                              String server,
                                              String projectId) {
        DMStageController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> results = dmStageService.getProgressReport(taskId, projectId);
       // CDTMSAPI.updateWorkFlowStatus(taskId,projectId,"status","10");
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", results);
        return result;
    }

    //数据清理
    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/submitToDMDateClean")

    public Map<String, Object> submitToDMDateClean(String taskId,
                                              String server,
                                              String projectId) {
        DMStageController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> results = dmStageService.submitToDMDateClean(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", results);
        return result;
    }
}
