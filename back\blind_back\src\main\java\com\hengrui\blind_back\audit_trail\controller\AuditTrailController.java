package com.hengrui.blind_back.audit_trail.controller;

import com.hengrui.blind_back.audit_trail.service.AuditTrailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName AuditTrailController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/7 14:50
 * @Version 1.0
 **/

@RestController
@Slf4j
public class AuditTrailController {
    @Autowired
    AuditTrailService auditTrailService;

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getAuditTrailFile")
    public Map<String, Object> getAuditTrailFile(String taskId,
                                                 String server,
                                                 String projectId) {
        AuditTrailController.log.info("server is :" + server);
        Map<String, String> results = auditTrailService.getAuditTrailFile(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", results.get("result"));
        result.put("data", results.get("result"));
        return result;
    }
}
