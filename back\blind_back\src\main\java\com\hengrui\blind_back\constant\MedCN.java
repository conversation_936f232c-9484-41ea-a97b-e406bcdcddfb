package com.hengrui.blind_back.constant;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * @ClassName MedCN
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/27 15:13
 * @Version 1.0
 **/
public class MedCN {
    @ExcelProperty("No.")
    private Integer no;

    @ExcelProperty("Study ID")
    private String studyId;

    @ExcelProperty("Site ID")
    private String siteId;

    @ExcelProperty("Subject Code")
    private String subjectCode;

    @ExcelProperty("Visit Name")
    private String visitName;

    @ExcelProperty("Form Name")
    private String formName;

    @ExcelProperty("Sn")
    private String sn;

    @ExcelProperty("Verbatims")
    private String verbatims;

    @ExcelProperty("LLT_CN")
    private String lltCn;

    @ExcelProperty("LLT_EN")
    private String lltEn;

    @ExcelProperty("LLT Code")
    private String lltCode;

    @ExcelProperty("PT_CN")
    private String ptCn;

    @ExcelProperty("PT_EN")
    private String ptEn;

    @ExcelProperty("PT Code")
    private String ptCode;

    @ExcelProperty("HLT_CN")
    private String hltCn;

    @ExcelProperty("HLT_EN")
    private String hltEn;

    @ExcelProperty("HLT Code")
    private String hltCode;

    @ExcelProperty("HLGT_CN")
    private String hlgtCn;

    @ExcelProperty("HLGT_EN")
    private String hlgtEn;

    @ExcelProperty("HLGT Code")
    private String hlgtCode;

    @ExcelProperty("SOC_CN")
    private String socCn;

    @ExcelProperty("SOC_EN")
    private String socEn;

    @ExcelProperty("SOC Code")
    private String socCode;

    @ExcelProperty("Primary SOC")
    private String primarySoc;

    @ExcelProperty("Query")
    private String query;

    @ExcelProperty("Dictionary Name")
    private String dictionaryName;

    @ExcelProperty("Dictionary Version")
    private String dictionaryVersion;

    @ExcelProperty("Updated time")
    private String updatedTime;

    @ExcelProperty("Coding Method")
    private String codingMethod;

    // Getters and Setters
    public Integer getNo() { return no; }
    public void setNo(Integer no) { this.no = no; }
    public String getStudyId() { return studyId; }
    public void setStudyId(String studyId) { this.studyId = studyId; }
    public String getSiteId() { return siteId; }
    public void setSiteId(String siteId) { this.siteId = siteId; }
    public String getSubjectCode() { return subjectCode; }
    public void setSubjectCode(String subjectCode) { this.subjectCode = subjectCode; }
    public String getVisitName() { return visitName; }
    public void setVisitName(String visitName) { this.visitName = visitName; }
    public String getFormName() { return formName; }
    public void setFormName(String formName) { this.formName = formName; }
    public String getSn() { return sn; }
    public void setSn(String sn) { this.sn = sn; }
    public String getVerbatims() { return verbatims; }
    public void setVerbatims(String verbatims) { this.verbatims = verbatims; }
    public String getLltCn() { return lltCn; }
    public void setLltCn(String lltCn) { this.lltCn = lltCn; }
    public String getLltEn() { return lltEn; }
    public void setLltEn(String lltEn) { this.lltEn = lltEn; }
    public String getLltCode() { return lltCode; }
    public void setLltCode(String lltCode) { this.lltCode = lltCode; }
    public String getPtCn() { return ptCn; }
    public void setPtCn(String ptCn) { this.ptCn = ptCn; }
    public String getPtEn() { return ptEn; }
    public void setPtEn(String ptEn) { this.ptEn = ptEn; }
    public String getPtCode() { return ptCode; }
    public void setPtCode(String ptCode) { this.ptCode = ptCode; }
    public String getHltCn() { return hltCn; }
    public void setHltCn(String hltCn) { this.hltCn = hltCn; }
    public String getHltEn() { return hltEn; }
    public void setHltEn(String hltEn) { this.hltEn = hltEn; }
    public String getHltCode() { return hltCode; }
    public void setHltCode(String hltCode) { this.hltCode = hltCode; }
    public String getHlgtCn() { return hlgtCn; }
    public void setHlgtCn(String hlgtCn) { this.hlgtCn = hlgtCn; }
    public String getHlgtEn() { return hlgtEn; }
    public void setHlgtEn(String hlgtEn) { this.hlgtEn = hlgtEn; }
    public String getHlgtCode() { return hlgtCode; }
    public void setHlgtCode(String hlgtCode) { this.hlgtCode = hlgtCode; }
    public String getSocCn() { return socCn; }
    public void setSocCn(String socCn) { this.socCn = socCn; }
    public String getSocEn() { return socEn; }
    public void setSocEn(String socEn) { this.socEn = socEn; }
    public String getSocCode() { return socCode; }
    public void setSocCode(String socCode) { this.socCode = socCode; }
    public String getPrimarySoc() { return primarySoc; }
    public void setPrimarySoc(String primarySoc) { this.primarySoc = primarySoc; }
    public String getQuery() { return query; }
    public void setQuery(String query) { this.query = query; }
    public String getDictionaryName() { return dictionaryName; }
    public void setDictionaryName(String dictionaryName) { this.dictionaryName = dictionaryName; }
    public String getDictionaryVersion() { return dictionaryVersion; }
    public void setDictionaryVersion(String dictionaryVersion) { this.dictionaryVersion = dictionaryVersion; }
    public String getUpdatedTime() { return updatedTime; }
    public void setUpdatedTime(String updatedTime) { this.updatedTime = updatedTime; }
    public String getCodingMethod() { return codingMethod; }
    public void setCodingMethod(String codingMethod) { this.codingMethod = codingMethod; }

    @Override
    public String toString() {
        return "CsvDataModel{" +
                "no=" + no +
                ", studyId='" + studyId + '\'' +
                ", siteId='" + siteId + '\'' +
                ", subjectCode='" + subjectCode + '\'' +
                ", visitName='" + visitName + '\'' +
                ", formName='" + formName + '\'' +
                ", sn='" + sn + '\'' +
                ", verbatims='" + verbatims + '\'' +
                ", lltCn='" + lltCn + '\'' +
                ", lltEn='" + lltEn + '\'' +
                ", lltCode='" + lltCode + '\'' +
                ", ptCn='" + ptCn + '\'' +
                ", ptEn='" + ptEn + '\'' +
                ", ptCode='" + ptCode + '\'' +
                ", hltCn='" + hltCn + '\'' +
                ", hltEn='" + hltEn + '\'' +
                ", hltCode='" + hltCode + '\'' +
                ", hlgtCn='" + hlgtCn + '\'' +
                ", hlgtEn='" + hlgtEn + '\'' +
                ", hlgtCode='" + hlgtCode + '\'' +
                ", socCn='" + socCn + '\'' +
                ", socEn='" + socEn + '\'' +
                ", socCode='" + socCode + '\'' +
                ", primarySoc='" + primarySoc + '\'' +
                ", query='" + query + '\'' +
                ", dictionaryName='" + dictionaryName + '\'' +
                ", dictionaryVersion='" + dictionaryVersion + '\'' +
                ", updatedTime='" + updatedTime + '\'' +
                ", codingMethod='" + codingMethod + '\'' +
                '}';
    }
}
