package com.hengrui.blind_back.rtsm.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hengrui.blind_back.blind.constant.BlindConstant;
import com.hengrui.blind_back.blind.utils.Decode64Util;
import com.hengrui.blind_back.blind.utils.MinioUtil;
import com.hengrui.blind_back.blind.utils.ResponseResult;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.ecrf_unlock.utils.FileUtil;
import com.hengrui.blind_back.onlyOffice.mapper.OnlyOfficeFileMapper;
import com.hengrui.blind_back.rtsm.entity.EsignEntity;
import com.hengrui.blind_back.rtsm.mapper.RTSMFileMapper;
import com.hengrui.blind_back.rtsm.mapper.RTSMFileNameMapper;
import com.hengrui.blind_back.rtsm.service.RTSMService;
import com.hengrui.blind_back.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.mail.MessagingException;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.hengrui.blind_back.utils.EsignAPI.addSigner;

/**
 * @ClassName RTSMServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/11 11:15
 * @Version 1.0
 **/
@Slf4j
@Service
public class RTSMServiceImpl implements RTSMService {
    @Autowired
    Decode64Util decode64Util;
    @Autowired
    MinioUtil minioUtil;

    @Autowired
    CallPython callPython;

    @Autowired
    FileUtil fileUtil;

    @Autowired
    private SubmitSAS submitSAS;

    @Autowired
    OnlyOfficeFileMapper onlyOfficeFileMapper;

    @Autowired
    RTSMFileMapper rtsmFileMapper;

    @Autowired
    RTSMFileNameMapper rtsmFileNameMapper;

    @Autowired
    MailUtil mailUtil;

    @Autowired
    FileUtils fileUtils;

    @Autowired
    SSHRemoteCall sshRemoteCall;

    @Autowired
    CDTMSAPI cdtmsapi;


    @Override
    public Map<String, String> submitToRTSMSAS(String taskId, String projectId) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId = "";
        String tableId = "";
        String drugname="";
        if (!ObjectUtils.isEmpty(formInfo.get("recordId"))) {
            recordId = formInfo.get("recordId");
            tableId = formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }
        //1.get the  studyId
        String studyId = formInfo.get("studyId");
        String used_language = CDTMSAPI.getStudyLanguage(studyId);
        //2.get sas check program form param from dataList API
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "manual_rev_prog");
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "manual_rev_prog", "obj.studyid=(select obj2.id from Xsht as obj2 where obj2.studyid='" + studyId + "') and obj.version_zt=4 and obj.checkarea=85  ", "", URLEncoder.encode("createtime desc"));
        JSONArray jsonArray = JSONArray.parseArray(dataListInfo);
        RTSMServiceImpl.log.info("获取到sas核查程序填写参数是" + jsonArray.toString());
        String param = "";
        cn.hutool.json.JSONObject paramObj = null;
        if (jsonArray.size() > 0) {
            String tmp = jsonArray.get(0).toString();
            param = JSONObject.parseObject(tmp).toString();
            String customcode = "";
            String sascodeStr = "";
            String parmStr = "";

            RTSMServiceImpl.log.info("获取到sas核查程序填写参数是" + param);
            if (!param.isEmpty()) {
                paramObj = new cn.hutool.json.JSONObject(param);
                CDTMSAPI.setRTSMCOMPParam(paramObj, studyId);
                String paramData = formInfo.get("param");
                cn.hutool.json.JSONObject paramDataObj = new cn.hutool.json.JSONObject(paramData);
                drugname=paramDataObj.get("drugname").toString();


                RTSMServiceImpl.log.info("随机一致性比对进行阶段表单的id是" + paramDataObj.get("id").toString());
                paramObj.put("recordid", paramDataObj.get("id").toString());
                paramObj.put("testyn", "N");
                customcode = paramObj.get("customcode").toString();
                parmStr = FileUtils.extractInsideBrackets(customcode);
                if(parmStr.isEmpty()){
                    return new HashMap<>();
                }
                sascodeStr = FileUtils.extractOutsideBrackets(customcode);
                paramObj.put("parm", parmStr);
                paramObj.put("sascode", sascodeStr);
            }
        }


        //3.download file from cdtms and upload file to minio storage
        //3.1 rtsm_data file upload
        String RTSMDataSetDate = "";
        try {
            String RTSMDataSetPath = fileUtil.uploadLocalFileToMinio(taskId, projectId, "rtsm_data", "_dsrand_informtable.csv");
            String rtsmUploadFileName = fileUtil.getDownloadFilesName(taskId, "rtsm_data");
            Map<String, String> originTag = minioUtil.getObjectTags("sascomp", "doc/" + studyId + "_dsrand_informtable.csv");
            // 创建一个新的可修改的 Map，并将原始标签数据复制到新 Map 中
            Map<String, String> modifiableTag = new HashMap<>(originTag);
            //提取文件名日期
            String date = FileUtils.extractDate(rtsmUploadFileName);
            RTSMDataSetDate=date;
            log.info("-------------------------随机一致性比对文件录入的时间戳date为:" + date + "----------------------------------");
            if (!ObjectUtils.isEmpty(date) || !date.isEmpty()) {
                modifiableTag.put("date", date);
                modifiableTag.put("env", "pro");
                //添加文件日期tag
                minioUtil.setObjectTags("sascomp", "doc/" + studyId + "_dsrand_informtable.csv", modifiableTag);
            }


            RTSMServiceImpl.log.info("---------------------------------------------------------------upload RTSMDataSetPath is:{}", RTSMDataSetPath);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        //3.2 rtms_drugdata file upload
        String randDataSetDate="";
if(!StringUtils.isEmpty(drugname)&&drugname.equals("是")){
    try {
        String RTSMDrugDataSetPath = fileUtil.uploadLocalFileToMinio(taskId, projectId, "rtms_drugdata", "_dsrand_druginformtable.csv");
        String rtsmUploadFileDrugName = fileUtil.getDownloadFilesName(taskId, "rtms_drugdata");
        Map<String, String> originTag = minioUtil.getObjectTags("sascomp", "doc/" + studyId + "_dsrand_druginformtable.csv");
        Map<String, String> modifiableTag = new HashMap<>(originTag);
        //提取文件名日期
        String date = FileUtils.extractDate(rtsmUploadFileDrugName);
        randDataSetDate=date;
        log.info("-------------------------随机一致性比对文件录入的时间戳date为:" + date + "----------------------------------");
        if (!ObjectUtils.isEmpty(date) || !date.isEmpty()) {
            modifiableTag.put("date", date);
            //添加文件日期tag
            minioUtil.setObjectTags("sascomp", "doc/" + studyId + "_dsrand_druginformtable.csv", modifiableTag);
        }
        RTSMServiceImpl.log.info("---------------------------------------------------------------upload RTSMDrugDataSetPath is:{}", RTSMDrugDataSetPath);
    } catch (IOException e) {
        throw new RuntimeException(e);
    }
}



        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        //4.call the python script to get file from edc server and upload the file into minio
        List<Map<String, String>> filesFromEDC = new ArrayList<>();
        Map<String, String> fileObjectA = new HashMap<>();
        fileObjectA.put("fid", "edc_dataset");
        fileObjectA.put("fileType", ".zip");
        Map<String, String> fileObjectB = new HashMap<>();
        fileObjectB.put("fid", "postamend");
        fileObjectB.put("fileType", ".zip");
        filesFromEDC.add(fileObjectA);


        String uuid = ULIDGenerator.generateULID();
        Map<String, String> ENVInfo = new HashMap<>();
        //上传进行阶段als到minio
        minioUtil.uploadALSPROFile(ENVInfo,studyId,taskId,formId,projectId);
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("ENV", "PRO");
        ENVInfo.put("data_format", "SAS");
        ENVInfo.put("data_type", "data_set");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", recordId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", tableId);
        ENVInfo.put("fileSuffix", "a".toString());
        //4.1.upload the latest report and second latest report to cdtms API
        //4.2 upload second latest report to cdtms
        ENVInfo.put("isLatest", "");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        Map<String, String> pyResult = callPython.downloadEDCServerFile(ENVInfo, filesFromEDC);
        String edcFileName = pyResult.get("original_name");
        String dataIsTwoDays = pyResult.get("dataIsTwoDays");
        String edcTime = FileUtil.transferEDCFIleName(edcFileName);
        //4.3 call python to move file from minio/raw to  minio/compsas/data/sas7bat
        callPython.executeLatestFIleTransfer(studyId);
        //5.call sas program
        formInfo.put("uuid", uuid);
        String paramFileName = formInfo.get("studyId").toString() + "_rtsmedc.json";
        //5.1 submit to sas via json params and define the json path on the minio storage platform
        formInfo.put("jsonMinioPath", SASOnlieConstant.PREFIX_PRO_MINIO+"/sascomp/json/" + paramFileName);
        formInfo.put("sasCodePath", SASOnlieConstant.RTSM_SAS_COMP_CODE_PRO_PATH);
        formInfo.put("paramFileName", paramFileName);
        formInfo.put("param", paramObj.toString());
        String outputName = "";
        if (used_language.equals("CH")) {
            outputName = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.RTSM_COMP_CH_SUFFIX;
        } else {
            outputName = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.RTSM_COMP_EN_SUFFIX;
        }
        //6. define the output report name on minio

        List<Map<String, String>> sasOutputFilesInfo = new ArrayList<>();
        formInfo.put("outputName", outputName);
        formInfo.put("bucket", "sascomp");
        formInfo.put("language", used_language);
        //7.upload the  sas output report to cdtms API
        Map<String, String> sasOutputFile = new HashMap<>();
        sasOutputFile.put("fid", "com_report");
        sasOutputFile.put("outputName", outputName);
        sasOutputFilesInfo.add(sasOutputFile);
        //8. call sas program
        formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        ENVInfo.put("formId", formId);
        Map<String, String> result = submitSAS.submitToSAS(ENVInfo, filesFromEDC, formInfo, sasOutputFilesInfo);
        //程序比对结果使用sas处理后的数据集
        String comResultName = "";
        if (used_language.equals("CH")) {
            comResultName = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.RTSM_COMP_CH__INIT_SUFFIX;
        } else {
            comResultName = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.RTSM_COMP_EN_INIT_SUFFIX;
        }
        Map<String, String> comOutputFile = new HashMap<>();
        comOutputFile.put("fid", "com_result");
        comOutputFile.put("outputName", comResultName);
        sasOutputFilesInfo.add(comOutputFile);
        Boolean isSuccess = minioUtil.downloadGetObject(formInfo.get("bucket"), comResultName);
        if(isSuccess){
            //获取tag,
            Map<String,String> tagInfo= minioUtil.getObjectTags(formInfo.get("bucket"), comResultName);
            String comnum= tagInfo.get("comnum");
            String date= tagInfo.get("date");
            if(comResultName.contains("随机化与供应管理数据一致性比对报告")){
                //获取文件名
                outputName=formInfo.get("studyId").toString()+"_随机化与供应管理数据一致性比对报告#"+comnum+"_"+date+".xlsx";
            }else{
                outputName=formInfo.get("studyId").toString()+"_RTSM Data Reconciliation Report#"+comnum+"_"+date+".xlsx";
            }



            String fileNameClear =comResultName;
            if (fileNameClear.contains("/")) {
                fileNameClear = fileNameClear.split("/")[1];
            }
            formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
            FileUtil.uploadSASOutputFile(ENVInfo.get("taskId").toString(), formId, "com_result", SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + fileNameClear, ENVInfo.get("projectId").toString(), SASOnlieConstant.REMOTE_SERVER_API_PREFIX, outputName, ENVInfo.get("data_type").toString());
        }



        //9.upload the sas log report to cdtms API
        formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String logPath = result.get("sasLogPath").toString();
        File logFile = new File(logPath);
        FileUtil.uploadSASOutputFile(taskId, formId, "log", logPath, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.REMOTE_SERVER_API_PREFIX, logFile.getName(), "log");

        Map<String, String>  newFormInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String data = newFormInfo.get("param").toString();
        String dataId = "";
        if (!data.isEmpty()) {
            cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(data);
            cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();
            dataId = formInfoData.get("id").toString();
            cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
            temp.put("id", dataId);
            if (!ObjectUtils.isEmpty(randDataSetDate) || !randDataSetDate.isEmpty()) {
              temp.put("rtms_drugdata_dt", randDataSetDate);
            }
            if (!ObjectUtils.isEmpty(RTSMDataSetDate) || !RTSMDataSetDate.isEmpty()) {
                temp.put("rtms_data_dt", RTSMDataSetDate);
            }

            params.put("data", temp);
            String newFormId = CDTMSAPI.getFormIdByTaskId(recordId, projectId);
            params.put("formId", newFormId);
            params.put("taskId", recordId);
            params.put("projectId", tableId);
            CDTMSAPI.dataSave(params);
        }


        result.put("edcFileDate", edcTime);
        result.put("dataIsTwoDays", "数据是否在两天之内：" + dataIsTwoDays);
        return result;
    }

    @Override
    public Map<String, String> submitToRTSMSASBack(String taskId, String projectId) {
        Map<String, String> result = new HashMap<>();
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);

        String recordId = "";
        String tableId = "";
        if (!ObjectUtils.isEmpty(formInfo.get("recordId"))) {
            recordId = formInfo.get("recordId");
            tableId = formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }
        //1.get the  studyId
        String studyId = formInfo.get("studyId");
        String used_language = CDTMSAPI.getStudyLanguage(studyId);
        //2.get sas check program form param from dataList API
        String param = "";
        param = formInfo.get("param");
        String customcode = "";
        String sascodeStr = "";
        String parmStr = "";
        cn.hutool.json.JSONObject paramObj = null;
        if (!param.isEmpty()) {
            paramObj = new cn.hutool.json.JSONObject(param);
            CDTMSAPI.setRTSMCOMPParam(paramObj, studyId);
            String dataId = paramObj.get("id").toString();
            paramObj.put("recordid", dataId);
            paramObj.put("testyn", "Y");
            customcode = paramObj.get("customcode").toString();
            parmStr = FileUtils.extractInsideBrackets(customcode);
            if(parmStr.isEmpty()){
                return new HashMap<>();
            }
            sascodeStr = FileUtils.extractOutsideBrackets(customcode);
            paramObj.put("parm", parmStr);
            paramObj.put("sascode", sascodeStr);
        }


        RTSMServiceImpl.log.info("获取到随机一致性比对的填写参数是:{}" + paramObj.toString());
        //3.download file from cdtms and upload file to minio storage
        //3.1 rtsm_data file upload
        try {
            String RTSMDataSetPath = fileUtil.uploadLocalFileToMinio(taskId, projectId, "rand_dataset", "_dsrand_informtable.csv");
            RTSMServiceImpl.log.info("---------------------------------------------------------------upload RTSMDataSetPath is:{}", RTSMDataSetPath);

            String rtsmUploadFileName = fileUtil.getDownloadFilesName(taskId, "rand_dataset");
            Map<String, String> originTag = minioUtil.getObjectTags("sascomp", "doc/" + studyId + "_dsrand_informtable.csv");
            Map<String, String> modifiableTag = new HashMap<>(originTag);
            //提取文件名日期
            String date = FileUtils.extractDate(rtsmUploadFileName);
            log.info("-------------------------随机一致性比对文件录入的时间戳date为:" + date + "----------------------------------");
            if (!ObjectUtils.isEmpty(date) || !date.isEmpty()) {
                modifiableTag.put("date", date);
                //添加文件日期tag
                minioUtil.setObjectTags("sascomp", "doc/" + studyId + "_dsrand_informtable.csv", modifiableTag);
            }


        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        //3.2 rtms_drugdata file upload
        try {
            String RTSMDrugDataSetPath = fileUtil.uploadLocalFileToMinio(taskId, projectId, "rtms_drugdata", "_dsrand_druginformtable.csv");
            RTSMServiceImpl.log.info("---------------------------------------------------------------upload RTSMDrugDataSetPath is:{}", RTSMDrugDataSetPath);
            String rtsmUploadFileName = fileUtil.getDownloadFilesName(taskId, "rtms_drugdata");
            if(!StringUtils.isEmpty(rtsmUploadFileName)){
                Map<String, String> originTag = minioUtil.getObjectTags("sascomp", "doc/" + studyId + "_dsrand_druginformtable.csv");
                Map<String, String> modifiableTag = new HashMap<>(originTag);
                //提取文件名日期
                String date = FileUtils.extractDate(rtsmUploadFileName);
                log.info("-------------------------随机一致性比对文件录入的时间戳date为:" + date + "----------------------------------");
                if (!ObjectUtils.isEmpty(date) || !date.isEmpty()) {
                    modifiableTag.put("date", date);
                    //添加文件日期tag
                    minioUtil.setObjectTags("sascomp", "doc/" + studyId + "_dsrand_informtable.csv", modifiableTag);
                }
            }


        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        //4.call the python script to get file from edc server and upload the file into minio
        List<Map<String, String>> filesFromEDC = new ArrayList<>();
        Map<String, String> fileObjectA = new HashMap<>();
        fileObjectA.put("fid", "edc_dataset");
        fileObjectA.put("fileType", ".zip");
        Map<String, String> fileObjectB = new HashMap<>();
        fileObjectB.put("fid", "postamend");
        fileObjectB.put("fileType", ".zip");
        filesFromEDC.add(fileObjectA);

        String uuid = ULIDGenerator.generateULID();
        Map<String, String> ENVInfo = new HashMap<>();
        //获取als同步到minio
         String queryALSResult = minioUtil.uploadALSUATFile(ENVInfo, studyId, taskId, formId, projectId);
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("ENV", "UAT");
        ENVInfo.put("data_format", "SAS");
        ENVInfo.put("data_type", "data_set");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", recordId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", tableId);
        ENVInfo.put("fileSuffix", "a".toString());
        //4.1.upload the latest report and second latest report to cdtms API
        //4.2 upload second latest report to cdtms
        ENVInfo.put("isLatest", "");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        Map<String, String> pyResult = callPython.downloadEDCServerFile(ENVInfo, filesFromEDC);
        String edcFileName = pyResult.get("original_name");
        String dataIsTwoDays = pyResult.get("dataIsTwoDays");
        if (dataIsTwoDays.equals("否")) {
            result.put("dataIsTwoDays", "数据是否在两天之内：" + dataIsTwoDays);
            return result;
        }
        String edcTime = FileUtil.transferEDCFIleName(edcFileName);
        if (edcFileName == null || edcFileName.isEmpty()) {
            //没有下载到数据集，使用表单上传的附件，上传到minio raw下,tag  example env:uat, key1:RAVE, key2:SHR-2010-201, key3:2024/09/27/13/17
            cdtmsapi.uploadSAS(studyId);
        }
        RTSMServiceImpl.log.info("获取到的edc数据集的时间为:{}", edcTime);
        //4.3 call python to move file from minio/raw to  minio/compsas/data/sas7bat
        callPython.executeLatestFIleTransfer(studyId);
        //5.call sas program
        formInfo.put("uuid", uuid);
        String paramFileName = formInfo.get("studyId").toString() + "_rtsmedc.json";
        //5.1 submit to sas via json params and define the json path on the minio storage platform
        formInfo.put("jsonMinioPath", SASOnlieConstant.PREFIX_PRO_MINIO+"/sascomp/json/" + paramFileName);
        formInfo.put("sasCodePath", SASOnlieConstant.RTSM_SAS_COMP_CODE_UAT_PATH);
        formInfo.put("paramFileName", paramFileName);
        formInfo.put("param", paramObj.toString());
        formInfo.put("language", used_language);
        //6. define the output report name on minio
        String outputName = "";
        if (used_language.equals("CH")) {
            outputName = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.RTSM_COMP_CH__INIT_SUFFIX;
        } else {
            outputName = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.RTSM_COMP_EN_INIT_SUFFIX;
        }
        List<Map<String, String>> sasOutputFilesInfo = new ArrayList<>();
        formInfo.put("outputName", outputName);
        formInfo.put("bucket", "sascomp");

        //7.upload the  sas output report to cdtms API
        Map<String, String> sasOutputFile = new HashMap<>();
        sasOutputFile.put("fid", "output");
        sasOutputFile.put("outputName", outputName);
        sasOutputFilesInfo.add(sasOutputFile);


        //查询ecrf设计与搭建 uat审核版的文件，将该文件上传到minio doc目录下
        if(queryALSResult.equals("fail")){
            cdtmsapi.uploadDBS(studyId);
        }


        //8. call sas program
        formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        ENVInfo.put("formId", formId);
        result = submitSAS.submitToSAS(ENVInfo, filesFromEDC, formInfo, sasOutputFilesInfo);
        //9.upload the sas log report to cdtms API
        formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String logPath = result.get("sasLogPath").toString();
        File logFile = new File(logPath);
        FileUtil.uploadSASOutputFile(taskId, formId, "uat_log", logPath, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.REMOTE_SERVER_API_PREFIX, logFile.getName(), "log");

        result.put("edcFileDate", edcTime);
        result.put("edcFileName", edcFileName);
        result.put("dataIsTwoDays", "数据是否在两天之内：" + dataIsTwoDays);
        return result;
    }

    @Override
    public Map<String, String> getApplyInfo(String studyId, String batchNum) {
        if (!studyId.isEmpty()) {
            return onlyOfficeFileMapper.getApplyInfo(studyId, batchNum);
        } else {
            return null;
        }
    }

    @Override
    public Map<String, String> sendReviewEmail(String param, String studyId, String batchNum) {
        //2.通过studyId查询该项目的统计师邮箱
        Map<String, String> rtsmAccountInfo = CDTMSAPI.getRTSMAccountEmail(studyId);
        //获取邮件标题和正文
        JSONObject mailInfo = JSON.parseObject(param);
        //邮件标题
        String title = mailInfo.get("title").toString();
        String content = mailInfo.get("content").toString();
        content = content.replaceAll("<p>", "").replaceAll("</p>", "").replaceAll("<br>", "\r\n").replaceAll("<br/>", "\r\n");
        String receivers = mailInfo.get("accountInfo").toString();
        JSONArray accountList = JSONArray.parseArray(receivers);
        //3.生成邮件主题，正文，附件
        String accountEmail = "";
        for (int i = 0; i < accountList.size(); i++) {
            accountEmail = JSONObject.parseObject(accountList.get(i).toString()).get("account").toString();
            String accountName = JSONObject.parseObject(accountList.get(i).toString()).get("accountName").toString();
           String newContent =  content;
            newContent = newContent.replaceAll("&amp;NAME&amp;", accountName).replaceAll("&amp;EMAIL&amp;",accountEmail);
            mailUtil.sendValidMail(accountEmail, title, newContent);
        }

        //4.发送邮件
        return rtsmAccountInfo;
    }

    @Override
    public String accountApprove(String studyId, String fileKey, String userName) {
        //查询有无这条记录
        int count = onlyOfficeFileMapper.getApplyInfoByVersion(studyId, fileKey);
        //变更该条记录的状态为已审核
        if (count > 0) {
            int numPrefix = onlyOfficeFileMapper.getVersionNumPrefix(studyId);
            //更新该条记录的版本号
            String versionNum = "V" + (numPrefix + 1) + ".0";
            //查询该条记录的文件路径
            String fileName = onlyOfficeFileMapper.getApprovedFilePath(studyId, fileKey);
            //判断下多个统计师是否都点了通过，否则不更新状态且不转换pdf
            //更新签字人审核状态
            onlyOfficeFileMapper.updateReviewerApproveStatus(studyId, fileKey, userName);
            //如果是多个统计师都点了通过，则更新状态，否则不更新
            int approveStatus = onlyOfficeFileMapper.judgeIsAllApprove(studyId, fileKey);
            if (approveStatus > 0) {
                //执行excel转pdf命令
                String command = "/home/<USER>/xlsx2pdf/venv/bin/python /home/<USER>/xlsx2pdf/xlsx2pdf.py /home/<USER>/8087/onlyOfficeFile/record/ " + fileName;
                CallPython.executeLatestFill(command);
                //更新pdf的文件路径
                // 获取最后一个斜杠的位置
                int lastSlashIndex = fileName.lastIndexOf('/');
                // 获取文件名（带扩展名）
                String fileNameWithExtension = fileName.substring(lastSlashIndex + 1);
                // 获取不带扩展名的文件名
                String desiredString = fileNameWithExtension.substring(0, fileNameWithExtension.lastIndexOf('.'));
                String signFilePath = "/home/<USER>/8087/onlyOfficeFile/record/" + desiredString + ".pdf";
                RTSMServiceImpl.log.info("---------------------------------------------------------------转换的签字文件位置 signFilePath is:{}", signFilePath);
                int result = onlyOfficeFileMapper.updateApproveStatus(studyId, fileKey, userName, versionNum, signFilePath);
            }
            return "success";
        } else {
            return "fail";
        }
    }

    @Override
    public String makeRTSMTemplate(String studyId, String accountVersion,String batchNum, String accountName, int fileType,int applyType,String fileName,JSONArray accountList,String applyType2) {
        Map<String, String> studyInfo = CDTMSAPI.getStudyInfo(studyId);
        //2.通过studyId查询该项目的统计师邮箱
//       Map<String, String> rtsmAccountManagerInfo = CDTMSAPI.getRTSMAccountManagerEmail(studyId);
        Map<String, String> rtsmAccountManagerInfo =getSignerInfoByRole("统计负责人",accountList);
        String accountManagerName = "";
        if (!ObjectUtils.isEmpty(rtsmAccountManagerInfo)) {
            accountManagerName = rtsmAccountManagerInfo.get("name");
        }

        Map<String, String> randManagerInfo =getSignerInfoByRole("随机负责人",accountList);
        String randManagerManagerName = "";
        if (!ObjectUtils.isEmpty(randManagerInfo)) {
            randManagerManagerName = randManagerInfo.get("name");
        }




        //根据模板类型获取模板文件,根据fileType判断
        String rmfilePath = "/home/<USER>/8087/onlyOfficeFile/rtsmTemplate/随机化与研究药物分配申请表-模板.xlsx";
        String medfilePath = "/home/<USER>/8087/onlyOfficeFile/rtsmTemplate/随机化与研究药物分配申请表-药物分配管理-模板.xlsx";
        String randfilePath = "/home/<USER>/8087/onlyOfficeFile/rtsmTemplate/随机化与研究药物分配申请表-随机分配管理-模板.xlsx";
        String templateFileName = "";
        if (fileType == 1) {
            //选择随机分配管理模板
            templateFileName = randfilePath;
        } else if (fileType == 2) {
            //选择药物分配管理模板
            templateFileName = medfilePath;
        } else if (fileType == 3) {
            //选择随机+药物管理模板
            templateFileName = rmfilePath;
        }

        Map<String, Object> map = MapUtils.newHashMap();
        if (applyType == 1) {
            map.put("applyType", "初始");
        } else if (applyType == 2) {
            map.put("applyType", "扩展");
        } else if (applyType == 3) {
            map.put("applyType", "修订/替换"+applyType2);
        }
        map.put("title", studyInfo.get("title").toString());
        map.put("caseNum", studyId);
        map.put("caseVersionNum", studyInfo.get("caseVersionNum").toString());
        map.put("caseVersionDate", studyInfo.get("caseVersionDate").toString().substring(0, 10));
        map.put("accountName", accountName);
        //获取申请表版本
        //2.文件批次号去重查询，获取版本号
 //       String latestVersion=onlyOfficeFileMapper.getLatestVersion(studyId);
        map.put("versionNum", accountVersion);
        map.put("accountManagerName", accountManagerName);
        if (fileType == 2) {
            String rtsmRole = "RandSpecialist";
            Map<String, String> rtsmRandInfo = CDTMSAPI.getRTSMRandInfo(studyId, rtsmRole);
            log.info(rtsmRandInfo.toString());
            String rtsmRole2 = "RandSpecialist2";
            Map<String, String> rtsmRandInfo2 = CDTMSAPI.getRTSMRandInfo(studyId, rtsmRole2);
            log.info(rtsmRandInfo2.toString());
            map.put("RandSpecialist", rtsmRandInfo.get("name"));
            map.put("RandSpecialist2", rtsmRandInfo2.get("name"));
            map.put("RandManager",randManagerManagerName);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // Parse current time to date
        LocalDate currentDate = LocalDate.now();
        String currentDateStr = currentDate.format(formatter);
        map.put("currentDate", currentDateStr);
        EasyExcel.write(fileName).withTemplate(templateFileName).sheet(0).doFill(map);
        return "success";
    }


    //创建随机申请表的批次记录
    @Override
    public String addBatchRecord(String studyId, String userName, String applyDate, String param) {
        if (!ObjectUtils.isEmpty(studyId) && !ObjectUtils.isEmpty(userName) && !studyId.isEmpty() && !userName.isEmpty()) {
            //填充模板内容,及动态生成
            //formResult 转换成json对象
            JSONObject jsonObject = JSONObject.parseObject(param);
            String accountInfo = jsonObject.get("accountInfo").toString();

            JSONArray accountList = JSONArray.parseArray(accountInfo);

            String uuid = ULIDGenerator.generateULID();
            //1.查询统计师名称

            //2.文件批次号去重查询，获取版本号
            int numPrefix = onlyOfficeFileMapper.getVersionNumPrefix(studyId);
            int num = onlyOfficeFileMapper.getVersionNum(studyId, "V" + numPrefix + ".");
            String latestVersion=onlyOfficeFileMapper.getLatestVersion(studyId);
            Boolean isIndexZero=false;
            if(!StringUtils.isEmpty(latestVersion)&&latestVersion.contains(".0")){
                isIndexZero=true;
            }
            int versionNumber=0;
            if(num==1&&isIndexZero){
                versionNumber = 1;
            }else if(numPrefix==0){
                versionNumber = num+1;
            }else{
                versionNumber = num;
            }


            String versionNum = "V" + numPrefix + "." + versionNumber;
            onlyOfficeFileMapper.insertApplyInfo(uuid, "", userName, "", studyId, "", versionNum, applyDate, param);
            //多个统计师审核记录新增
            String accountName = "";
            String accountEmail = "";
            String accountNameCover= "";
            for (int i = 0; i < accountList.size(); i++) {
                accountName = JSONObject.parseObject(accountList.get(i).toString()).get("accountName").toString();
                accountNameCover=JSONObject.parseObject(accountList.get(0).toString()).get("accountName").toString();
                accountEmail = JSONObject.parseObject(accountList.get(i).toString()).get("account").toString();
                String reviewerId = ULIDGenerator.generateULID();
                onlyOfficeFileMapper.insertApplyReviewers(reviewerId, uuid, accountName, accountEmail);
            }


            int fileType = (int) jsonObject.get("rmdm");
            //创建随机化与研究药物分配申请表模板
            //获取表单信息
            int applyType = (int) jsonObject.get("applyType");
            String applyType2= jsonObject.get("applyType2").toString();
            String fileName = "/home/<USER>/8087/onlyOfficeFile/" + studyId + "_" + uuid + "_随机化与研究药物分配申请表.xlsx";
            makeRTSMTemplate(studyId,"", uuid, accountNameCover, fileType,applyType,fileName,accountList,applyType2);
            FileUtils.dynamicLoadExcel(jsonObject, fileName);
            return uuid;
        }
        return "fail";
    }

    @Override
    public List<Map<String, String>> getStudyBatchRecords(String studyId) {
        List<Map<String, String>> result = onlyOfficeFileMapper.getBatchRecords(studyId);
        return result;
    }


    @Override
    public List<Map<String, String>> getAABatchRecords(String studyId) {
        List<Map<String, String>> result = onlyOfficeFileMapper.getAABatchRecords(studyId);
        return result;
    }

    @Override
    public ResponseResult<?> sendAccountSign(String param, String studyId) {
        String fileKey = ULIDGenerator.generateULID();

        JSONObject signInfo = JSON.parseObject(param);
        String userName = signInfo.get("userName").toString();
        int applyType = (int) signInfo.get("applyType");
        String applyType2= signInfo.get("applyType2").toString();
        int fileType = (int) signInfo.get("rmdm");
        String accountVersion= signInfo.get("accountVersion").toString();
        String accountInfo = signInfo.get("accountInfo").toString();
        JSONArray accountList = JSONArray.parseArray(accountInfo);
        String fileName = "/home/<USER>/8087/onlyOfficeFile/" + studyId + "_" + fileKey + "_随机化与研究药物分配申请表.xlsx";
        Map<String, String> rtsmAccountInfo =getSignerInfoByRole("统计",accountList);
        String rtsmAccountName = "";
        if (!ObjectUtils.isEmpty(rtsmAccountInfo)) {
            rtsmAccountName = rtsmAccountInfo.get("name");
        }
        makeRTSMTemplate(studyId, accountVersion,fileKey, rtsmAccountName, fileType,applyType,fileName,accountList,applyType2);
        //选择随机分配管理模板
        String coverXlsxPath = fileUtils.addSuffixToFileName(fileName, "_signPage");
        fileUtils.extractFirstSheetByRemovingOthers(fileName,coverXlsxPath);
        String command = "/home/<USER>/xlsx2pdf/venv/bin/python /home/<USER>/xlsx2pdf/xlsx2pdf.py /home/<USER>/8087/onlyOfficeFile/record/ " + coverXlsxPath;
        CallPython.executeLatestFill(command);

        //2.下载审核表url pdf文件
        String fileUrl = signInfo.get("fileUrl").toString();
        String localFilePath = "/home/<USER>/8087/onlyOfficeFile/" + studyId + "_" + fileKey + "_随机化与研究药物分配申请表_参数.pdf";
        try {
             fileUtils.downloadRTSMGenFile(fileUrl, localFilePath);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        String coverPDFPath="/home/<USER>/8087/onlyOfficeFile/record/" + studyId + "_" + fileKey + "_随机化与研究药物分配申请表_signPage.pdf";
        String outputPath = "/home/<USER>/8087/onlyOfficeFile/" + studyId +  "_随机化与研究药物分配申请表"+accountVersion+".pdf";
        fileUtils.combinePdfFiles(coverPDFPath,localFilePath,outputPath);

        signInfo.put("signFilePath",outputPath);
        onlyOfficeFileMapper.insertApplyInfo(fileKey, "", userName, "", studyId, "", accountVersion, "", param);
        onlyOfficeFileMapper.updateApproveStatus(studyId, fileKey, userName, accountVersion, outputPath);
         ResponseResult<?> responseResult = sendSign(signInfo.toString(), studyId, fileKey, userName);
         if(responseResult.getCode()==200){
             //更新该条记录的状态为已审核
             return new ResponseResult<>(200, "success", responseResult.getData());
         }else{
             return responseResult;
         }


    }

    @Override
    public Map<String, String> getAASignFilePathByTaskId(String taskId) {
        if(StringUtils.isEmpty(taskId)){
            return null;
        }else{
            Map<String, String> paths= onlyOfficeFileMapper.getAASignFilePathByTaskId(taskId);
            if(null==paths ||paths.isEmpty()){
                Map<String, String> accountFilepaths= onlyOfficeFileMapper.getSignFilePathByTaskId(taskId);

                String accountFilePath = accountFilepaths.get("sign_file_path");
                String accountSignedFilePath = accountFilepaths.get("signed_file_path");
                Map<String, String> result = new HashMap<>();
                if(StringUtils.isEmpty(accountFilePath)&&StringUtils.isEmpty(accountSignedFilePath)){
                    return null;
                }else{
                    if(StringUtils.isEmpty(accountFilePath)){
                        accountFilePath="";
                    }else{
                        accountFilePath=SASOnlieConstant.SASONLINE_SERVER_DOMAIN+"files"+extractPathAfterKeyword(accountFilePath,"onlyOfficeFile");
                    }


                    if(StringUtils.isEmpty(accountSignedFilePath)||null==accountSignedFilePath){
                        accountSignedFilePath="";
                    }else{
                        accountSignedFilePath=SASOnlieConstant.SASONLINE_SERVER_DOMAIN+"RTSMSignedFiles"+extractPathAfterKeyword(accountSignedFilePath,"rtsm_file");
                    }


                    result.put("signFilePath",accountFilePath);
                    result.put("SignedFilePath",accountSignedFilePath);
                    return result;
                }

            }else{
                String signFilePath = paths.get("sign_file_path");
                String SignedFilePath = paths.get("signed_file_path");
                Map<String, String> result = new HashMap<>();
                if(StringUtils.isEmpty(signFilePath)&&StringUtils.isEmpty(SignedFilePath)){
                    return null;
                }else{
                    if(StringUtils.isEmpty(signFilePath)){
                        signFilePath="";
                    }else{
                        signFilePath=SASOnlieConstant.SASONLINE_SERVER_DOMAIN+"RTSMSignedFiles"+extractPathAfterKeyword(signFilePath,"rtsm_file");
                    }


                    if(StringUtils.isEmpty(SignedFilePath)){
                        SignedFilePath="";
                    }else{
                        SignedFilePath=SASOnlieConstant.SASONLINE_SERVER_DOMAIN+"RTSMSignedFiles"+extractPathAfterKeyword(SignedFilePath,"rtsm_file");
                    }


                    result.put("signFilePath",signFilePath);
                    result.put("SignedFilePath",SignedFilePath);
                    return result;
                }
            }

        }
    }

    




    public static String extractPathAfterKeyword(String fullPath,String keyword) {
        Path path = Paths.get(fullPath);

        // Find the index of the "rtsm_file" directory
        int onlyOfficeFileIndex = -1;
        for (int i = 0; i < path.getNameCount(); i++) {
            if (path.getName(i).toString().equals(keyword)) {
                onlyOfficeFileIndex = i;
                break;
            }
        }

        // Extract the subpath after "onlyOfficeFile"
        if (onlyOfficeFileIndex != -1 && onlyOfficeFileIndex < path.getNameCount() - 1) {
            return "/" + path.subpath(onlyOfficeFileIndex + 1, path.getNameCount());
        }

        return "";
    }

    @Override
    public String checkValidCode(String studyId, String batchNum, String code) {
        // 1. 参数校验
        if (StringUtils.isEmpty(studyId) || StringUtils.isEmpty(batchNum) || StringUtils.isEmpty(code)) {
            return "fail";
        }

        try {
            // 2. 获取验证码记录
            List<Map<String, Object>> validCodes = onlyOfficeFileMapper.getValidCode(studyId, batchNum);
            if (validCodes.isEmpty()) {
                return "fail";
            }

            // 3. 查找匹配的验证码
            Map<String, Object> matchedCode = validCodes.stream()
                    .filter(validCodeMap -> code.equals(String.valueOf(validCodeMap.get("valid_code"))))
                    .findFirst()
                    .orElse(null);

            if (matchedCode == null) {
                return "fail";
            }

            // 4. 验证时间
            Date validTime = (Date) matchedCode.get("validTime");
            if (validTime == null) {
                return "fail";
            }

            LocalDateTime now = LocalDateTime.now();
            LocalDateTime codeTime = LocalDateTime.ofInstant(validTime.toInstant(), ZoneId.systemDefault());
            LocalDateTime expirationTime = codeTime.plusMinutes(5);

            return now.isAfter(expirationTime) ? "expire" : "success";

        } catch (Exception e) {
            log.error("验证码校验失败", e);
            return "fail";
        }
    }

    @Override
    public Map<String, String> getValidCode(String studyId, String batchNum, String rtsmAccountEmail, String accountName) {
        //2.通过studyId查询该项目的统计师邮箱
        Map<String, String> rtsmAccountInfo = CDTMSAPI.getRTSMAccountEmail(studyId);
        //发送验证码
        int num = (int) ((Math.random() * 9 + 1) * 100000);
        String code = String.valueOf(num);
        String validCodeMailTitle = studyId + "随机申请表在线审核验证码";
        StringBuilder validCodeMailContent = new StringBuilder();
        validCodeMailContent.append(accountName).append(" 您好:");
        validCodeMailContent.append("\r\n");
        validCodeMailContent.append("\r\n");
        validCodeMailContent.append("您的验证码是：").append(code);
        validCodeMailContent.append("\r\n");
        validCodeMailContent.append("您可以复制此验证码并返回页面进行填写，以验证您的权限。");
        validCodeMailContent.append("\r\n");
        validCodeMailContent.append("此验证码只能使用一次，在5分钟内有效。验证成功则自动失效。");
        validCodeMailContent.append("\r\n");
        validCodeMailContent.append("如果您没有进行上述操作，请忽略此邮件。");
        validCodeMailContent.append("\r\n");
        validCodeMailContent.append("\r\n");
        validCodeMailContent.append("随机生成器平台");
        //更新验证码字段
        onlyOfficeFileMapper.updateValidCodeAndTime(studyId, batchNum, code);
        mailUtil.sendValidMail(rtsmAccountEmail, validCodeMailTitle, validCodeMailContent.toString());
        return rtsmAccountInfo;
    }

    @Override
    public String updateApplyDate(String studyId, String batchNum, String applyDate) {
        if (!ObjectUtils.isEmpty(studyId) && !ObjectUtils.isEmpty(batchNum) && !ObjectUtils.isEmpty(applyDate) && !studyId.isEmpty() && !batchNum.isEmpty() && !applyDate.isEmpty()) {
            RTSMServiceImpl.log.info("更新的的信息为：studyId=" + studyId + " batchNum=" + batchNum + " applyDate=" + applyDate);
            onlyOfficeFileMapper.updateApplyDate(studyId, batchNum, applyDate);
            return "success";
        }
        return "fail";
    }

    @Override
    public Map<String, Object> getRTSMFileNames(String taskId, String projectId) {

        LocalDate currentDate = LocalDate.now();
        String dateString = currentDate.format(DateTimeFormatter.ISO_DATE);
        //获取studyId
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId = "";
        String tableId = "";
        if (!ObjectUtils.isEmpty(formInfo.get("recordId"))) {
            recordId = formInfo.get("recordId");
            tableId = formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }
        String studyId = formInfo.get("studyId");
        //获取模板文件
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");
        String filePath = SASOnlieConstant.SAS_DATA_LOCAL_FOLDER + studyId + "_随机化计划_" + dateString + ".docx";
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "xsht", "obj.studyid='" + studyId + "'", "", "");
        String temp = JSON.parseArray(dataListInfo).get(0).toString();
        String id = JSON.parseObject(temp).get("id").toString();
        String getFileInfo = CDTMSAPI.getDataListInfo(token, "rand_protocol_design", "obj.studyid='" + id + "'", "edit", "obj.submit_date desc");
        String fileInfo = JSON.parseArray(getFileInfo).get(0).toString();
        String input = JSON.parseObject(fileInfo).get("rand_protocol_design_file").toString();
        String regex = "\\*([A-Z0-9]+\\.docx)\\|";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        String ufn = "";
        if (matcher.find()) {
            ufn = matcher.group(1);
            RTSMServiceImpl.log.info(ufn);
        } else {
            RTSMServiceImpl.log.info("No match found");
        }
        RTSMServiceImpl.log.info("-------------------------------------------------------found the file name is {}---------------------------", ufn);
        if (!ufn.isEmpty()) {
            try {
                CDTMSAPI.downloadDataByUserSync("rand_protocol_design", token, ufn, filePath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }


        Map<String, Object> rtsmFileName = new HashMap<>();
        String tableNum = rtsmFileMapper.getTableId(studyId);
        if (!ObjectUtils.isEmpty(tableNum) && !tableNum.isEmpty()) {

            //获取子方案名
            List<String> subtrialNames = rtsmFileMapper.getSubtrial(studyId);
            //获取组别名
            List<String> groupNames = rtsmFileMapper.getRandGroupInfo(studyId);
            RTSMServiceImpl.log.info("获取到的子方案名是：{}", subtrialNames);
            //获取药物盲底文件名
            List<String> medNames = rtsmFileNameMapper.getMedicalFileName("tbl_files_" + tableNum + "_dev", "tbl_cure_object_" + tableNum + "_dev");
            RTSMServiceImpl.log.info("获取到的药物盲底 文件名是：{}", medNames);
            //获取随机盲底文件名
            List<String> randNames = rtsmFileNameMapper.getRandFileName("tbl_files_" + tableNum + "_dev", "tbl_randlist_" + tableNum + "_dev");
            RTSMServiceImpl.log.info("获取到的随机盲底 文件名是：{}", randNames);
            rtsmFileName.put("medNames", medNames);
            rtsmFileName.put("randNames", randNames);
            //文件处理
            try {
                String savePath = "/home/<USER>/8087/rtsm_file/" + studyId + "_随机工作计划_" + dateString + ".docx";
                FileInputStream in = new FileInputStream(filePath);
                FileOutputStream out = new FileOutputStream(savePath);
                XWPFDocument doc = new XWPFDocument(in);

                List<XWPFTable> tables = doc.getTables();
                XWPFTable table0 = tables.get(5);
                //设置第一行
                List<XWPFTableRow> subtrialsRows = table0.getRows();

                for (int i = 0; i < subtrialsRows.size() - 1; i++) {
                    if (i < subtrialNames.size() && !ObjectUtils.isEmpty(subtrialNames.get(i))) {
                        subtrialsRows.get(i + 1).getCell(0).setText(subtrialNames.get(i));
                        if (groupNames.size() > 0 && !ObjectUtils.isEmpty(groupNames.get(i))) {
                            subtrialsRows.get(i + 1).getCell(1).setText(groupNames.get(i));
                        }

                    }

                }
                if (subtrialNames.size() > subtrialsRows.size()) {
                    for (int i = subtrialNames.size() - subtrialsRows.size() - 2; i < subtrialNames.size(); i++) {
                        table0.createRow();//这是新增的一行
                        if (i < subtrialNames.size() && !ObjectUtils.isEmpty(subtrialNames.get(i))) {
                            subtrialsRows.get(i + 1).getCell(0).setText(subtrialNames.get(i));
                            if (groupNames.size() > 0 && !ObjectUtils.isEmpty(groupNames.get(i))) {
                                subtrialsRows.get(i + 1).getCell(1).setText(groupNames.get(i));
                            }

                        }

                    }
                }


                XWPFTable table1 = tables.get(9);
                //设置第一行
                List<XWPFTableRow> rows = table1.getRows();

                for (int i = 0; i < rows.size() - 1; i++) {
                    if (i < medNames.size() && !ObjectUtils.isEmpty(medNames.get(i))) {
                        rows.get(i + 1).getCell(0).setText(medNames.get(i));


                    }

                }
                if (medNames.size() > rows.size()) {
                    for (int i = medNames.size() - rows.size() - 2; i < medNames.size(); i++) {
                        table1.createRow();//这是新增的一行
                        if (i < medNames.size() && !ObjectUtils.isEmpty(medNames.get(i))) {
                            rows.get(i + 1).getCell(0).setText(medNames.get(i));
                        }

                    }
                }


                XWPFTable table2 = tables.get(8);
                //设置第一行
                List<XWPFTableRow> randRows = table2.getRows();

                for (int i = 0; i < randRows.size() - 1; i++) {
                    if (i < randNames.size() && !ObjectUtils.isEmpty(randNames.get(i))) {
                        randRows.get(i + 1).getCell(0).setText(randNames.get(i));
                    }

                }
                if (randNames.size() > randRows.size()) {
                    for (int i = randNames.size() - randRows.size() - 2; i < randNames.size(); i++) {
                        table2.createRow();//这是新增的一行
                        if (i < randNames.size() && !ObjectUtils.isEmpty(randNames.get(i))) {
                            rows.get(i + 1).getCell(0).setText(randNames.get(i));
                        }
                    }
                }

                doc.write(out);
                out.flush();
                out.close();
                //文件上传到cdtms上
                String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
                File saveFile = new File(savePath);
                FileUtil.uploadSASOutputFile(taskId, formId, "rand_protocol_design_file", savePath, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.REMOTE_SERVER_API_PREFIX, saveFile.getName(), "file");
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        } else {
            String savePath = SASOnlieConstant.SAS_DATA_LOCAL_FOLDER + studyId + "_随机化计划_" + dateString + ".docx";
            //文件上传到cdtms上 SOP-CDSC-031F1随机化工作计划.docx
            String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
            File saveFile = new File(savePath);
            FileUtil.uploadSASOutputFile(taskId, formId, "rand_protocol_design_file", savePath, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.REMOTE_SERVER_API_PREFIX, saveFile.getName(), "file");
        }

        return rtsmFileName;
    }

    @Override
    public void getRTSMFile(String taskId, String projectId) {
        //查询项目id
        //获取studyId
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId = "";
        String tableId = "";
        if (!ObjectUtils.isEmpty(formInfo.get("recordId"))) {
            recordId = formInfo.get("recordId");
            tableId = formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }
        String studyId = formInfo.get("studyId");
        //下载文件
        String randfilePath = RTSMAPI.downloadRTSMFile(studyId, "随机信息揭盲报告");
        if (!randfilePath.isEmpty()) {
            File randFile = new File(randfilePath);
            if (randFile.exists()) {
                //回传文件
                uploadFile(randfilePath, "rtsm_dsrand_file", taskId, projectId);

            }
        }

        //下载文件
        String medfilePath = RTSMAPI.downloadRTSMFile(studyId, "药物信息揭盲报告");
        if (!medfilePath.isEmpty()) {
            File medFile = new File(medfilePath);
            if (medFile.exists()) {
                //回传文件
                uploadFile(medfilePath, "rtsm_drug_file", taskId, projectId);

            }
        }

        //下载文件
        String medtrailfilePath = RTSMAPI.downloadRTSMFile(studyId, "药物稽查轨迹报告");
        if (!medtrailfilePath.isEmpty()) {
            File medtrailFile = new File(medtrailfilePath);
            if (medtrailFile.exists()) {
                //回传文件
                uploadFile(medtrailfilePath, "rtsm_drug_check", taskId, projectId);

            }
        }

        //下载文件
        String subjectFilePath = RTSMAPI.downloadRTSMFile(studyId, "受试者稽查轨迹报告");
        if (!subjectFilePath.isEmpty()) {
            File subjectFile = new File(subjectFilePath);
            if (subjectFile.exists()) {
                //回传文件
                uploadFile(subjectFilePath, "rtsm_subject_check", taskId, projectId);
            }
        }

        //下载文件
        String billFilePath = RTSMAPI.downloadRTSMFile(studyId, "运单稽查轨迹报告");
        if (!billFilePath.isEmpty()) {
            File billFile = new File(billFilePath);
            if (billFile.exists()) {
                //回传文件
                uploadFile(billFilePath, "rtsm_bill_check", taskId, projectId);
            }

        }


        //下载邮件
        String filepath1 = "";
        String filepath2 = "";
        String filepath3 = "";
        String filepath4 = "";
        String filepath5 = "";
        String filepath6 = "";
        String filepath7 = "";
        try {
            filepath1 = mailUtil.saveSendMailFile(studyId, "开发环境", "发药运单");
            filepath2 = mailUtil.saveSendMailFile(studyId, "开发环境", "受试者再发药");
            filepath3 = mailUtil.saveSendMailFile(studyId, "开发环境", "受试者已补药");
            filepath4 = mailUtil.saveSendMailFile(studyId, "开发环境", "新的受试者入组");
            filepath5 = mailUtil.saveSendMailFile(studyId, "开发环境", "新的受试者药物分配");
            filepath6 = mailUtil.saveSendMailFile(studyId, "开发环境", "药物即将过期");
            filepath7 = mailUtil.saveSendMailFile(studyId, "开发环境", "运件接收延迟");
        } catch (MessagingException e) {
            throw new RuntimeException(e);
        }

        List<File> fileList = new ArrayList<>();
        if (!filepath1.isEmpty()) {
            fileList.add(new File(filepath1));
        }
        if (!filepath2.isEmpty()) {
            fileList.add(new File(filepath2));
        }
        if (!filepath3.isEmpty()) {
            fileList.add(new File(filepath3));
        }
        if (!filepath4.isEmpty()) {
            fileList.add(new File(filepath4));
        }
        if (!filepath5.isEmpty()) {
            fileList.add(new File(filepath5));
        }
        if (!filepath6.isEmpty()) {
            fileList.add(new File(filepath6));
        }
        if (!filepath7.isEmpty()) {
            fileList.add(new File(filepath7));
        }

        if (fileList.size() > 0) {
            FileOutputStream fos2 = null;
            String zipPath = SASOnlieConstant.RTSM_FILE_PATH + studyId + "_邮件历史_" + FileUtil.getCurrentDateStr() + ".zip";
            try {
                fos2 = new FileOutputStream(new File(zipPath));
            } catch (FileNotFoundException e) {
                throw new RuntimeException(e);
            }
            fileUtils.toZip(fileList, fos2);
            //回传邮件
            uploadFile(zipPath, "rtsm_email_his", taskId, projectId);
        }


    }

    @Override
    public void getRTSMMonitorReport(String taskId, String projectId) {
        //查询项目id
        //获取studyId
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId = "";
        String tableId = "";
        if (!ObjectUtils.isEmpty(formInfo.get("recordId"))) {
            recordId = formInfo.get("recordId");
            tableId = formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }
        String studyId = formInfo.get("studyId");

        //下载文件
        String randfilePath = RTSMAPI.downloadRTSMFile(studyId, "发药监控报告");
        RTSMServiceImpl.log.info("发药监控报告的文件路径为：{}", randfilePath);
        File file1 = new File(randfilePath);
        String ufn1 = CDTMSAPI.getUfn(randfilePath, recordId, projectId);
        String medfilePath = RTSMAPI.downloadRTSMFile(studyId, "随机监控报告");
        RTSMServiceImpl.log.info("随机监控报告的文件路径为：{}", medfilePath);
        File file2 = new File(medfilePath);
        String ufn2 = CDTMSAPI.getUfn(medfilePath, recordId, projectId);
        String data = formInfo.get("param").toString();
        String dataId = "";
        if (!data.isEmpty()) {
            cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(data);
            cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();
            dataId = formInfoData.get("id").toString();
            cn.hutool.json.JSONObject temp = new cn.hutool.json.JSONObject();
            temp.put("id", dataId);
            if (!ObjectUtils.isEmpty(ufn1) && !ufn1.isEmpty() && !ObjectUtils.isEmpty(ufn2) && !ufn2.isEmpty()) {
                temp.put("rtsm_jc_report", file1.getName() + "*" + ufn1 + "|" + file2.getName() + "*" + ufn2 + "|");
            } else if (!ObjectUtils.isEmpty(ufn1) && !ufn1.isEmpty()) {
                temp.put("rtsm_jc_report", file1.getName() + "*" + ufn1 + "|");
            } else if (!ObjectUtils.isEmpty(ufn2) && !ufn2.isEmpty()) {
                temp.put("rtsm_jc_report", file2.getName() + "*" + ufn2 + "|");
            }
            params.put("data", temp);
            String newFormId = CDTMSAPI.getFormIdByTaskId(recordId, projectId);
            params.put("formId", newFormId);
            params.put("taskId", recordId);
            params.put("projectId", tableId);
            CDTMSAPI.dataSave(params);
        }

    }

    @Override
    public String editFormInfo(String param, String studyId, String batchNum, String userName) {
        //formResult 转换成json对象
        JSONObject jsonObject = JSONObject.parseObject(param);
        log.info("获取到的参数是：" + jsonObject.toString());

        String applyType2=jsonObject.get("applyType2").toString();
        String accountManagerName = jsonObject.get("account").toString();
        String accountInfo = jsonObject.get("accountInfo").toString();
        JSONArray accountList = JSONArray.parseArray(accountInfo);


        int fileType = (int) jsonObject.get("rmdm");
        int applyType = (int) jsonObject.get("applyType");
        String fileName = "/home/<USER>/8087/onlyOfficeFile/" + studyId + "_" + batchNum + "_随机化与研究药物分配申请表.xlsx";
        makeRTSMTemplate(studyId, "",batchNum, accountManagerName, fileType,applyType,fileName,accountList,applyType2);

        File file = new File(fileName);
        if (file.exists()) {
            file.delete();
        }

        FileUtils.dynamicLoadExcel(jsonObject, fileName);
        onlyOfficeFileMapper.updateFormInfo(batchNum, studyId, param, userName);
        return "success";
    }

    @Override
    public String deleteBatchRecord(String studyId, String batchNum, String userName) {
        onlyOfficeFileMapper.deleteBatchRecord(studyId, batchNum, userName);
        return "success";
    }

    @Override
    public String forceSave(String fileKey, String userId) {
        String baseUrl = "https://oos-tst.hengrui.com";
        String result = "";
        try {
            URL url = new URL(baseUrl + "/command?token=" + fileKey);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // Set up the HTTP request
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setDoOutput(true);

            // Create JSON request body
            JSONObject requestBody = new JSONObject();
            requestBody.put("c", "forcesave");
            requestBody.put("key", fileKey);
            requestBody.put("userdata", userId);

            // Write the request body
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.toString().getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            // Get response
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // Success response handling
                try (InputStream responseStream = connection.getInputStream();
                     BufferedReader reader = new BufferedReader(new InputStreamReader(responseStream))) {
                    String line;
                    StringBuilder response = new StringBuilder();
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    log.info("Response: " + response.toString());
                    result = response.toString();
                }
            } else {
                // Error response handling
                log.info("保存失败");
            }

            connection.disconnect();

        } catch (Exception e) {
            log.info("保存失败: " + e.getMessage());
        }
        return result;
    }

    @Override
    public String saveAsRecord(String studyId, String userName, String filePath) {
        //创建记录，模板文件用复制的形式，存入到file_name字段
        Map<String, String> recordInfo = onlyOfficeFileMapper.getRecordByFileName(studyId, filePath);
        String uuid = ULIDGenerator.generateULID();
        //1.查询统计师名称
        String rtsmAaplyId = recordInfo.get("id");
        List<Map<String, String>> reviewersInfo = onlyOfficeFileMapper.getReviewersByRtsmApplyId(rtsmAaplyId);
        String applyDate = recordInfo.get("apply_date");
        String param = recordInfo.get("remark");
        String sourcePath = filePath;
        String targetPath = "/home/<USER>/8087/onlyOfficeFile/" + studyId + "_" + uuid + "_随机化与研究药物分配申请表.xlsx";
        try {
            //复制一份另存的文件，到指定目录
            Files.copy(Paths.get(sourcePath), Paths.get(targetPath));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        //2.文件批次号去重查询，获取版本号
        int numPrefix = onlyOfficeFileMapper.getVersionNumPrefix(studyId);
        int num = onlyOfficeFileMapper.getVersionNum(studyId, "V" + numPrefix + ".");
        int versionNumber = 0;
        versionNumber = num + 1;
        String versionNum = "V" + numPrefix + "." + versionNumber;
        onlyOfficeFileMapper.insertApplyInfo(uuid, "", userName, "", studyId, "", versionNum, applyDate, param);
        //新增审核人员信息
        if (reviewersInfo.size() > 0) {
            for (int i = 0; i < reviewersInfo.size(); i++) {
                String reviewerId = ULIDGenerator.generateULID();
                onlyOfficeFileMapper.insertApplyReviewers(reviewerId, uuid, reviewersInfo.get(i).get("name"), reviewersInfo.get(i).get("email"));
            }
        }

        return "success";
    }

    @Override
    public String RTSMESignCallBack(cn.hutool.json.JSONObject param) {
        try {
            String taskId = param.getStr("task_id");
            int type = param.getInt("type");
            String email = param.getStr("email");
            if (!StringUtils.isEmpty(email)) {
                // 解码为字节数组
                byte[] decodedBytes = Base64.getDecoder().decode(email);
                // 将字节数组转换为字符串
                email = new String(decodedBytes);
            }
            String fileName = onlyOfficeFileMapper.getFileNameByTaskId(taskId);
            String mailFileName= com.hengrui.blind_back.utils.FileUtils.processFileName(fileName);
            Map<String, String> userInfo = onlyOfficeFileMapper.getAuthorEmailByTaskId(taskId);
            String authorEmail = userInfo.get("email");
            String userName = userInfo.get("name");
            String fileCode = userInfo.get("fileCode");
            List<Map<String, String>> signerList = onlyOfficeFileMapper.getSignerList(taskId);
            // 根据类型处理回调
            switch (type) {
                case 1: // 用户签署回调
                    String reason = param.getStr("reason");
                    String signTime = param.getStr("sign_time");
                    log.info("用户 {} 于 {} 签署，原因：{}", email, signTime, reason);
                    // 更新签署人状态
                    Map<String, Object> signerParam = new HashMap<>();
                    signerParam.put("taskId", taskId);
                    signerParam.put("email", email);
                    signerParam.put("status", 1);
                    signerParam.put("reason", reason);
                    onlyOfficeFileMapper.updateSignerStatus(signerParam);
                    break;
                case 2: // 文件签署回调
                    String outputUrl = param.getStr("output_url");
                    int status = param.getInt("status");
                    int signStatus = 0;
                    if (status != 2) {
                        signStatus = param.getInt("sign_status");
                    }


                    if (status == 4) {
                        // 流程完成
                        // 下载签署文件
                        if (StringUtils.isEmpty(outputUrl) && signStatus == 1) {
                            // 发送邮件通知
                            Map<String, String> mailMap = mailUtil.sendSignFailNotification(authorEmail, userName, mailFileName);
                            String uuid = UUID.randomUUID().toString();
                            onlyOfficeFileMapper.addSendMailRecord(uuid, userName, authorEmail, taskId, mailMap.get("content"), mailMap.get("subject"));
                        } else if(signStatus == 1) {
                            Map<String, String> rtsmAuditApprovalInfo = onlyOfficeFileMapper.getRtsmAuditApprovalInfo(taskId);
                            String filePath="";
                            if(!org.apache.commons.collections4.MapUtils.isEmpty(rtsmAuditApprovalInfo)){
                                //查询文件名
                                 filePath = downloadSignedFile(outputUrl, fileName,"/home/<USER>/8087/rtsm_file/rtsm_aduit_approve_signed_files/");
                            }else{
                                //查询文件名
                                 filePath = downloadSignedFile(outputUrl, fileName,SASOnlieConstant.RTSM_FILE_PATH);
                            }

                            File Signedfile = new File(filePath);
                            String md5= decode64Util.getMd5(Signedfile);
                            // 更新文件状态
                            Map<String, Object> signFileParam = new HashMap<>();
                            signFileParam.put("taskId", taskId);
                            signFileParam.put("status", 4);
                            signFileParam.put("signStatus", 1);
                            signFileParam.put("outputUrl", outputUrl);
                            signFileParam.put("md5",md5);
                            onlyOfficeFileMapper.updateSignFileStatus(signFileParam);
                            Boolean isAuthorInSignners=false;
                            //发送邮件通知-签字人-签署完成
                            for (Map<String, String> signer : signerList) {
                                String signerEmail = signer.get("email");
                                String signerName = signer.get("username");
                                if (signerEmail.equals(authorEmail)) {
                                    isAuthorInSignners=true;
                                }
                                // 发送邮件通知
                                mailUtil.sendSignAllDoneWithFile(signerEmail, signerName, mailFileName, cn.hutool.core.io.FileUtil.file(filePath), outputUrl);
                            }
                            if(isAuthorInSignners){
                                Map<String, String> mailMap = mailUtil.sendSignAllDoneWithFile(authorEmail, userName, mailFileName, cn.hutool.core.io.FileUtil.file(filePath), outputUrl);
                                String uuid = UUID.randomUUID().toString();
                                onlyOfficeFileMapper.addSendMailRecord(uuid, userName, authorEmail, taskId, mailMap.get("content"), mailMap.get("subject"));
                            }

                            //更新rtsm_apply表签字状态
                            onlyOfficeFileMapper.updateRtsmApplySignStatus(taskId,filePath);
                            onlyOfficeFileMapper.updateRtsmAuditApprovalSignStatus(taskId,filePath);

                             if(!org.apache.commons.collections4.MapUtils.isEmpty(rtsmAuditApprovalInfo)){
                                 String studyId=rtsmAuditApprovalInfo.get("study_name");
                                 String params=rtsmAuditApprovalInfo.get("remark");
                                 JSONObject parameter=new JSONObject();
                                 parameter.put("projectCode", studyId);
                                 //查询参数获取文件名
                                 if(!StringUtils.isEmpty(params)){
                                     JSONObject remarkObj = JSONObject.parseObject(params);
                                     parameter.put("fileType", remarkObj.get("fileType").toString());
                                     parameter.put("randFilename", remarkObj.get("randFilename"));
                                     parameter.put("medicFilename", remarkObj.get("medicFilename"));
                                     parameter.put("medType", remarkObj.get("medType"));
                                 }
                                 //添加签署人-作者
                                 String callAPIResult = EsignAPI.RTSMPostRequest(SASOnlieConstant.RTSMGEN_API_PREFIX+"tbProject/updateStatus" , parameter.toString());
                                 log.info("-------调用随机生成器更改状态，返回结果-------" + callAPIResult);
                                 //更新发起签字的文件和签署完成的文件地址




                             }else{
                                 Map<String, String> rtsmAuditAccountInfo = onlyOfficeFileMapper.getRtsmAccountInfo(taskId);
                                 if(!org.apache.commons.collections4.MapUtils.isEmpty(rtsmAuditAccountInfo)){
                                     String params=rtsmAuditAccountInfo.get("remark");
                                     JSONObject remarkObj = JSONObject.parseObject(params);
                                     String pid=remarkObj.get("pid").toString();
                                     String uid=remarkObj.get("uuid").toString();
                                     EsignAPI.RTSMGetRequest(SASOnlieConstant.RTSMGEN_API_PREFIX+"tbAppParameter/updateStatus?pid="+pid+"&uuid="+uid);
                                 }

                             }

                        }
                    } else if (status == 2) { // 签署过期
                        Map<String, Object> signFileParam = new HashMap<>();
                        signFileParam.put("taskId", taskId);
                        signFileParam.put("status", 2);
                        signFileParam.put("signStatus", -1);
                        onlyOfficeFileMapper.updateSignFileStatus(signFileParam);
                        for (Map<String, String> signer : signerList) {
                            String signerEmail = signer.get("email");
                            signerParam = new HashMap<>();
                            signerParam.put("taskId", taskId);
                            signerParam.put("email", signerEmail);
                            signerParam.put("status", -1);
                            signerParam.put("reason", null);
                            onlyOfficeFileMapper.updateSignerStatus(signerParam);
                        }

                        // 发送邮件通知
                        Map<String, String> mailMap = mailUtil.sendSignExpireNotification(authorEmail, userName, mailFileName);
                        //邮件记录
                        String uuid = UUID.randomUUID().toString();
                        onlyOfficeFileMapper.addSendMailRecord(uuid, userName, authorEmail, taskId, mailMap.get("content"), mailMap.get("subject"));
                        onlyOfficeFileMapper.updateRtsmApplySignStatus(taskId,"");
                        onlyOfficeFileMapper.updateRtsmAuditApprovalSignStatus(taskId,"");
                        log.warn("文件签署已过期，taskId: {}", taskId);
                    } else if (status == 1) {
                        //更新任务状态
                        signerParam = new HashMap<>();
                        signerParam.put("taskId", taskId);
                        signerParam.put("email", email);
                        signerParam.put("status", 1);
                        signerParam.put("signStatus", signStatus);
                        onlyOfficeFileMapper.updateTaskStatus(signerParam);
                        onlyOfficeFileMapper.updateSingerStatus(signerParam);
                        Map<String, String> signInfo = onlyOfficeFileMapper.getSingTimeByTaskId(taskId, email);
                        String signerName = signInfo.get("name");
                        String singFinishTime = signInfo.get("signTime");
                        String signURL= signInfo.get("signURL");
                    //    String filePath = downloadSignedFile(outputUrl, fileName);
                        //邮件通知签署人，签字完成
                            Map<String, String> mailMap = mailUtil.sendSignNotificationWithSignURL(email, signerName, mailFileName, signURL, singFinishTime);
                            String uuid = UUID.randomUUID().toString();
                            onlyOfficeFileMapper.addSendMailRecord(uuid, signerName, email, taskId, mailMap.get("content"), mailMap.get("subject"));



                    }
                    break;

                case 3: // 任务下发回调
                    cn.hutool.json.JSONArray items = param.getJSONArray("items");
                    for (Object item : items) {
                        cn.hutool.json.JSONObject signer = (cn.hutool.json.JSONObject) item;
                        String signerEmail = signer.get("email").toString();
                        byte[] decodedBytes = Base64.getDecoder().decode(signerEmail);
                        signerEmail = new String(decodedBytes);
                        String signerName = signer.get("name_zh").toString();
                        // 更新签署人状态为待签署
                        signerParam = new HashMap<>();
                        signerParam.put("taskId", taskId);
                        signerParam.put("email", signerEmail);
                        signerParam.put("status", 2);
                        signerParam.put("reason", null);
                        onlyOfficeFileMapper.updateSignerStatus(signerParam);
                        //更新任务状态
                        signerParam.put("status", 1);
                        signerParam.put("signStatus", 0);
                        onlyOfficeFileMapper.updateTaskStatus(signerParam);
                        //查询任务过期时间
                        String expireDate = onlyOfficeFileMapper.getTaskExpireDate(taskId);
                        String signUrl = onlyOfficeFileMapper.getSignUrlByTaskId(taskId, signerEmail);
                        // 发送邮件通知
                        Map<String, String> mailInfo = onlyOfficeFileMapper.getMailInfoByFileName(fileName);
                        if (org.apache.commons.collections4.MapUtils.isEmpty(mailInfo)) {
                            mailInfo = onlyOfficeFileMapper.getAAMailInfoByFileName(fileName);
                        }
                        Map<String, String> mailMap = mailUtil.sendSignNotificationDIY(signerEmail, userName, mailFileName, expireDate, fileCode, signUrl, signerName, mailInfo.get("subject"), mailInfo.get("content"));
                        String uuid = UUID.randomUUID().toString();
                        onlyOfficeFileMapper.addSendMailRecord(uuid, userName, signerEmail, taskId, mailMap.get("content"), mailMap.get("subject"));


                    }
                    Map<String, String> rtsmAuditApprovalInfo = onlyOfficeFileMapper.getRtsmAuditApprovalInfo(taskId);
                    if (!org.apache.commons.collections4.MapUtils.isEmpty(rtsmAuditApprovalInfo)) {
                        String studyId = rtsmAuditApprovalInfo.get("study_name");
                        String params = rtsmAuditApprovalInfo.get("remark");
                        JSONObject parameter = new JSONObject();
                        parameter.put("projectCode", studyId);
                        //查询参数获取文件名
                        String pid="";
                        String uuidList="";
                        if (!StringUtils.isEmpty(params)) {
                            JSONObject remarkObj = JSONObject.parseObject(params);
                            pid=remarkObj.get("pid").toString();
                            uuidList=remarkObj.get("uuidList").toString();
                        }
                        //添加签署人-作者
                        cn.hutool.json.JSONObject callAPIResult = EsignAPI.getRequest(SASOnlieConstant.RTSMGEN_API_PREFIX + "tbParameter/updateStatus?pid=" + pid + "&uuidList=" + uuidList);
                        log.info("-------调用随机生成器更改状态，返回结果-------" + callAPIResult.toString());
                    }
                    break;

                default:
                    log.warn("未知的回调类型: {}", type);
                    return "未知的回调类型";
            }
            return "success";
        } catch (Exception e) {
            log.error("处理电子签名回调失败", e);
            return "处理回调失败";
        }
    }


    public  String incrementVersion(String filename) {
        // Regular expression to match version pattern like V2.1
        String regex = "(V)(\\d+)\\.(\\d+)";

        // Use StringBuilder for string manipulation
        StringBuilder result = new StringBuilder(filename);

        // Find the version pattern in the filename
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
        java.util.regex.Matcher matcher = pattern.matcher(filename);

        // 检查是否已经包含日期格式 yyyy-MM-dd
        Pattern datePattern = Pattern.compile("-\\d{4}-\\d{2}-\\d{2}");

        int lastDotIndex = filename.lastIndexOf(".");
        String nameToCheck = lastDotIndex > 0 ? filename.substring(0, lastDotIndex) : filename;

        // 如果已经包含日期格式，直接返回
        if (datePattern.matcher(nameToCheck).find()) {
            return filename;
        }


        if (matcher.find()) {
            // Get current date in format yyyy-MM-dd
            java.time.LocalDate currentDate = java.time.LocalDate.now();
            String dateString = currentDate.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // Replace the old version with the date string
            result.replace(matcher.start(), matcher.end(), "_"+dateString);
        }

        return result.toString();
    }
    private String downloadSignedFile(String url, String fileName,String folderPath) throws IOException {
        fileName=incrementVersion(fileName);
        String savePath = folderPath + fileName;
        FileUtils.downloadFileByUrl(url, savePath);
        return savePath;
    }


    private Map<String, String> getSignerInfoByRole(String role, JSONArray signerList) {
        Map<String, String> result = new HashMap<>();

        for (int i = 0; i < signerList.size(); i++) {
            JSONObject signer = signerList.getJSONObject(i);
            if (role.equals(signer.getString("role"))) {
                result.put("name", signer.getString("accountName"));
                result.put("email", signer.getString("account"));
                break;
            }
        }

        return result;
    }

    private Map<String, String> getSignerInfoByName(String name, JSONArray signerList) {
        Map<String, String> result = new HashMap<>();

        for (int i = 0; i < signerList.size(); i++) {
            JSONObject signer = signerList.getJSONObject(i);
            if (name.equals(signer.getString("accountName"))) {
                result.put("name", signer.getString("accountName"));
                result.put("email", signer.getString("account"));
                break;
            }
        }

        return result;
    }

    @Override
    public ResponseResult<?> sendSign(String param,String studyId, String fileKey, String userName) {

   /*      String approveResult = accountApprove(studyId, fileKey, userName);

         if(!approveResult.equals("success")){
            return new ResponseResult<>(400, "error", "审核失败，未查询到该文件对应的记录！");
         }*/

        //获取邮件标题和正文
        JSONObject mailInfo = JSON.parseObject(param);
        //邮件标题
        String title = mailInfo.get("title").toString();
        String content = mailInfo.get("content").toString();
        //存储邮件标题和正文 fileKey
        if(StringUtils.isEmpty(title)||StringUtils.isEmpty(content)){
            return new ResponseResult<>(401, "error", "邮件正文或内容不能为空!");
        }


        //获取该条文件的参数，判断签字人信息是否为空，如果为空则使用默认的签字人信息
/*        String paramter = onlyOfficeFileMapper.getRecordParamByFileKey(studyId, fileKey);
        if (ObjectUtils.isEmpty(paramter) || paramter.isEmpty()) {
            return new ResponseResult<>(400, "error", "该文件申请表参数未录入!");
        }
        JSONObject paramObj = JSONObject.parseObject(paramter);*/
        int fileType = (int) mailInfo.get("rmdm");

        String receivers = mailInfo.get("accountInfo").toString();
        JSONArray accountList = JSONArray.parseArray(receivers);
        if(accountList.size()==0){
            return new ResponseResult<>(400, "error", "没有设置签字文件接收人信息!");
        }

        //存储签字人信息

        onlyOfficeFileMapper.updateRTSMApplyMailInfo(title,content,receivers,fileKey);



        Map<String, String> applyPosConfig = onlyOfficeFileMapper.getSignPosition("rtsm_apply", "申请人");
        Map<String, String> accountPosConfig = onlyOfficeFileMapper.getSignPosition("rtsm_apply", "统计师");
        Map<String, String> accountManagerPosConfig = onlyOfficeFileMapper.getSignPosition("rtsm_apply", "统计师总监");

        //获取发送签字人的邮箱-拿这个文件的审核人信息匹配
       // List<Map<String, String>> accountInfos = onlyOfficeFileMapper.getAccountEmail(studyId, fileKey);

        //List<Integer> rows5 = new ArrayList<>();
       // rows5 = fileUtils.findAccountRowsByText(filePath, "项目统计师", 1);
        String  reviewer = "";
        if(fileType==2){
                 Map<String, String> randReviewer = getSignerInfoByRole("随机一", accountList);
                 if(!org.apache.commons.collections4.MapUtils.isEmpty(randReviewer)){
                     reviewer= randReviewer.get("name");
                 }
        }else{
                Map<String, String> accountReviewer = getSignerInfoByRole("统计", accountList);
                if(!org.apache.commons.collections4.MapUtils.isEmpty(accountReviewer)){
                    reviewer= accountReviewer.get("name");
                }
            }


        //统计师链接参数获取
        Map<String, String> accountSignEmailParams = onlyOfficeFileMapper.getAccountSignEmailParams(fileKey);
//         String batchNum = accountSignEmailParams.get("batch_num");
        String batchNum =fileKey;
                String accountUrl=SASOnlieConstant.RTSM_GENERATOR_URL+studyId+"&role=account&username="+reviewer+"&batchNum="+batchNum;

      /*  if (accountInfos.size() == 0) {
            return new ResponseResult<>(404, "error", "未查询到该文件的审核人员!");
        }*/
        JSONObject signPosObj = new JSONObject();
        String authorEmail = "";
        Map<String,String> signerInfoByName = getSignerInfoByName(userName, accountList);
        if(!org.apache.commons.collections4.MapUtils.isEmpty(signerInfoByName)){
            authorEmail =signerInfoByName.get("email");
        }


        //获取统计师总监
        // Map<String, String> rtsmAccountManager = CDTMSAPI.getRTSMAccountManagerEmail(studyId);
        Map<String, String> rtsmAccountManager =getSignerInfoByRole("统计负责人",accountList);
        // 如果未找到匹配的邮箱，则使用默认值
        if (authorEmail.isEmpty()) {
            authorEmail = "<EMAIL>";
        }

        //1.1查询有无这条记录
    //    int count = onlyOfficeFileMapper.checkPDFFileIsExist(studyId, fileKey);
        //1.2变更该条记录的状态为已审核
//        if (count > 0) {
            //1.3获取文件位置
          //  String signFilePath = onlyOfficeFileMapper.getSignFilePath(studyId, fileKey);
            String signFilePath=mailInfo.get("signFilePath").toString();
            if (ObjectUtils.isEmpty(signFilePath) || signFilePath.isEmpty()) {
                return new ResponseResult<>(404, "error", "未查询到该签字文件!");
            }
            //1.4调用上传文件接口
            Map<String, String> result = null;
            try {
                result = EsignAPI.uploadFile(signFilePath, SASOnlieConstant.ESIGN_TST_FILE + "/upload");
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            log.info("获取到的文件id为{}，文件名称为{}", result.get("fileId"), result.get("filename"));
            String fileName= result.get("filename");
            //1.5存储到esign_sign_files表
            File file = new File(signFilePath);
            String fileMd5 = decode64Util.getMd5(file);
            //生成一个6位随机数
            String fileCode = com.hengrui.blind_back.utils.FileUtils.getRandomString(6);
            onlyOfficeFileMapper.insertSignFile(result.get("fileId"), result.get("filename"), signFilePath, fileMd5, userName, studyId, fileCode);
            //2.创建任务
            JSONObject taskParam = new JSONObject();
            taskParam.put("file_id", result.get("fileId"));
            taskParam.put("expiration_date", com.hengrui.blind_back.utils.FileUtils.getExpirationTime());
            taskParam.put("callback_url", SASOnlieConstant.SAS_ONLINE + "ESignCallBack");
            taskParam.put("file_code", fileMd5);
            taskParam.put("file_author", userName);
            taskParam.put("created_by", accountUrl);
            taskParam.put("file_status", "Under signing");
            taskParam.put("sign_type", 0);
            String taskInfo = EsignAPI.postRequest(SASOnlieConstant.ESIGN_TST_TASK, taskParam.toString());
            String taskData = JSONObject.parseObject(taskInfo).get("data").toString();
            JSONObject taskDataObj = JSONObject.parseObject(taskData);
            String taskId = taskDataObj.get("task_id").toString();
            //生成签字任务记录表记录
            Map<String, Object> params = new HashMap<>();
            params.put("taskId", taskId);
            params.put("fileId", result.get("fileId"));
            params.put("fileCode", fileCode);
            params.put("userName", userName);
            params.put("studyId", studyId);
            params.put("expirationDate", new Timestamp((Long) taskParam.get("expiration_date"))); // 时间戳（毫秒）转换为 Timestamp
            params.put("email", authorEmail);
            params.put("fileKey",fileKey);
            onlyOfficeFileMapper.insertSignTask(params);



                //userid用于设置默认的签署位置用-更新签署人位置
                int applicantId = 0;
                int accountId = 0;
                int accountManagerId = 0;
                String accountName = "";
                String accountEmail = "";
                if (fileType == 1) {
                    // 添加签署人-申请人
                //    applicantId = addSigner(taskId, userName, userName, authorEmail, fileCode, "我是申请人", 1);
                    // 添加签署人-统计师
                       Map<String,String > accountInfo=getSignerInfoByRole("统计",accountList);
                        accountEmail = accountInfo.get("email");
                        accountName = accountInfo.get("name");

                            accountId = addSigner(taskId, accountName, accountName, accountEmail, fileCode, "我审阅该文件", 0);
                            signPosObj = EsignAPI.setDefaultSignerPosition(taskId, accountId,  accountPosConfig.get("name_pos_x"),  // 从数据库读取
                                    accountPosConfig.get("name_pos_y"),
                                    accountPosConfig.get("date_pos_x"),
                                    accountPosConfig.get("date_pos_y"),  1, signPosObj);
                            //统计师
                            String uuid = ULIDGenerator.generateULID();
                            onlyOfficeFileMapper.insertSignUsers(uuid, taskId, accountName, accountEmail, "我审阅该文件", SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(accountEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + accountEmail + "&name=" + accountName+"&fileName="+fileName);
                            log.info("统计师的签署地址是：" + SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(accountEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + accountEmail + "&name=" + accountName+"&fileName="+fileName);



                    //添加统计师总监
                    if (!ObjectUtils.isEmpty(rtsmAccountManager)) {
                        String rtsmAccountManagerEmail = rtsmAccountManager.get("email");
                        String rtsmAccountManagerName = rtsmAccountManager.get("name");
                        if (!userName.equals(rtsmAccountManagerName)) {
                            accountManagerId = addSigner(taskId, rtsmAccountManagerName, rtsmAccountManagerName, rtsmAccountManagerEmail, fileCode, "我批准该文件", 0);
                            signPosObj = EsignAPI.setDefaultSignerPosition(taskId, accountManagerId, accountManagerPosConfig.get("name_pos_x"),  // 从数据库读取
                                    accountManagerPosConfig.get("name_pos_y"),
                                    accountManagerPosConfig.get("date_pos_x"),
                                    accountManagerPosConfig.get("date_pos_y"), 1, signPosObj);
                            //统计师
                             uuid = ULIDGenerator.generateULID();
                            onlyOfficeFileMapper.insertSignUsers(uuid, taskId, rtsmAccountManagerName, rtsmAccountManagerEmail, "我批准该文件", SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(rtsmAccountManagerEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + rtsmAccountManagerEmail + "&name=" + rtsmAccountManagerName+"&fileName="+fileName);
                            log.info("统计师总监的签署地址是：" + SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(rtsmAccountManagerEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + rtsmAccountManagerEmail + "&name=" + rtsmAccountManagerName+"&fileName="+fileName);
                        }

                    }

                } else if (fileType == 2) {
                    // 添加签署人-申请人
                    applicantId = addSigner(taskId, userName, userName, authorEmail, fileCode, "我是该文件作者", 1);
                    signPosObj = EsignAPI.setDefaultSignerPosition(taskId, applicantId, applyPosConfig.get("name_pos_x"),  // 从数据库读取
                            applyPosConfig.get("name_pos_y"),
                            applyPosConfig.get("date_pos_x"),
                            applyPosConfig.get("date_pos_y"),  1, signPosObj);
                    log.info("申请人的签署地址是：" + SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(authorEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + authorEmail + "&name=" + userName+"&fileName="+fileName);
                    //存储签字人信息到esign_users
                    String uuid = ULIDGenerator.generateULID();
                    //申请人
                    onlyOfficeFileMapper.insertSignUsers(uuid, taskId, userName, authorEmail, "我是该文件作者", SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(authorEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + authorEmail + "&name=" + userName+"&fileName="+fileName);
                    //选择药物分配管理模板
                 //   Map<String, String> rtsmRandInfo2 = CDTMSAPI.getRTSMRandInfo(studyId, "RandSpecialist2");
                    Map<String, String> rtsmRandInfo2 =getSignerInfoByRole("随机二",accountList);
                    if (!ObjectUtils.isEmpty(rtsmRandInfo2)) {
                        accountName = rtsmRandInfo2.get("name");
                        accountEmail = rtsmRandInfo2.get("email");
                        if (!userName.equals(accountName)) {
                            // 添加签署人-2nd随机化专员
                            accountId = addSigner(taskId, accountName, accountName, accountEmail, fileCode, "我审阅该文件", 0);
                            signPosObj = EsignAPI.setDefaultSignerPosition(taskId, accountId, accountPosConfig.get("name_pos_x"),  // 从数据库读取
                                    accountPosConfig.get("name_pos_y"),
                                    accountPosConfig.get("date_pos_x"),
                                    accountPosConfig.get("date_pos_y"),1, signPosObj);
                            log.info("--------------------添加签署人2nd随机化专员成功------------------------");
                            //2nd随机化专员
                             uuid = ULIDGenerator.generateULID();
                            onlyOfficeFileMapper.insertSignUsers(uuid, taskId, accountName, accountEmail, "我审阅该文件", SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(accountEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + accountEmail + "&name=" + accountName+"&fileName="+fileName);
                            log.info("2nd随机化专员的签署地址是：" + SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(accountEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + accountEmail + "&name=" + accountName+"&fileName="+fileName);
                        }
                    } else {
                        //手动添加随机化专员
                        log.info("--------------------未查询到2nd随机化专员,添加签署人2nd随机化专员失败------------------------");
                    }
                    Map<String, String> rtsmRandLeader =getSignerInfoByRole("随机负责人",accountList);
                    if (!ObjectUtils.isEmpty(rtsmRandLeader)) {
                        accountName = rtsmRandLeader.get("name");
                        accountEmail = rtsmRandLeader.get("email");
                        if (!userName.equals(accountName)) {
                            // 添加签署人-2nd随机化专员
                            accountId = addSigner(taskId, accountName, accountName, accountEmail, fileCode, "我审阅该文件", 0);
                            signPosObj = EsignAPI.setDefaultSignerPosition(taskId, accountId, accountPosConfig.get("name_pos_x"),  // 从数据库读取
                                    accountPosConfig.get("name_pos_y"),
                                    accountPosConfig.get("date_pos_x"),
                                    accountPosConfig.get("date_pos_y"),1, signPosObj);
                            log.info("--------------------添加签署人随机负责人成功------------------------");
                            //2nd随机化专员
                            uuid = ULIDGenerator.generateULID();
                            onlyOfficeFileMapper.insertSignUsers(uuid, taskId, accountName, accountEmail, "我审阅该文件", SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(accountEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + accountEmail + "&name=" + accountName+"&fileName="+fileName);
                            log.info("随机负责人的签署地址是：" + SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(accountEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + accountEmail + "&name=" + accountName+"&fileName="+fileName);
                        }
                    } else {
                        //手动添加随机化专员
                        log.info("--------------------未查询到随机负责人,添加随机负责人失败------------------------");
                    }
                } else if (fileType == 3) {
                    //选择随机+药物管理模板
                    // 添加签署人-申请人
                   // applicantId = addSigner(taskId, userName, userName, authorEmail, fileCode, "我是申请人", 1);
                    // 添加签署人-统计师
                        Map<String,String > accountInfo=getSignerInfoByRole("统计",accountList);
                        accountEmail = accountInfo.get("email");
                        accountName = accountInfo.get("name");
                            accountId = addSigner(taskId, accountName, accountName, accountEmail, fileCode, "我审阅该文件", 0);
                            signPosObj = EsignAPI.setDefaultSignerPosition(taskId, accountId, accountPosConfig.get("name_pos_x"),  // 从数据库读取
                                    accountPosConfig.get("name_pos_y"),
                                    accountPosConfig.get("date_pos_x"),
                                    accountPosConfig.get("date_pos_y"),1, signPosObj);
                            String uuid = ULIDGenerator.generateULID();
                            onlyOfficeFileMapper.insertSignUsers(uuid, taskId, accountName, accountEmail, "我审阅该文件", SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(accountEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + accountEmail + "&name=" + accountName+"&fileName="+fileName);
                            log.info("统计师的签署地址是：" + SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(accountEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + accountEmail + "&name=" + accountName+"&fileName="+fileName);



                    //添加统计师总监
                    if (!ObjectUtils.isEmpty(rtsmAccountManager)) {
                        String rtsmAccountManagerEmail = rtsmAccountManager.get("email");
                        String rtsmAccountManagerName = rtsmAccountManager.get("name");
                        if (!userName.equals(rtsmAccountManagerName)) {
                            accountManagerId = addSigner(taskId, rtsmAccountManagerName, rtsmAccountManagerName, rtsmAccountManagerEmail, fileCode, "我批准该文件", 0);
                            signPosObj = EsignAPI.setDefaultSignerPosition(taskId, accountManagerId, accountManagerPosConfig.get("name_pos_x"),  // 从数据库读取
                                    accountManagerPosConfig.get("name_pos_y"),
                                    accountManagerPosConfig.get("date_pos_x"),
                                    accountManagerPosConfig.get("date_pos_y"),  1, signPosObj);
                             uuid = ULIDGenerator.generateULID();
                            onlyOfficeFileMapper.insertSignUsers(uuid, taskId, rtsmAccountManagerName, rtsmAccountManagerEmail, "我批准该文件", SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(rtsmAccountManagerEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + rtsmAccountManagerEmail + "&name=" + rtsmAccountManagerName+"&fileName="+fileName);
                            log.info("统计师总监：" + SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(accountEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + rtsmAccountManagerEmail + "&name=" + rtsmAccountManagerName+"&fileName="+fileName);
                        }

                    }
                }

                //任务下发
                JSONObject taskDisParam = new JSONObject();
                taskDisParam.put("type", 1);
                taskDisParam.put("remark", studyId + "_项目随机申请表下发");
              //  String taskDis = EsignAPI.putRequest(SASOnlieConstant.ESIGN_TST_TASK + "/" + taskId, taskDisParam.toString());
              //  log.info(taskDis);

                //签署 地址 https:///clinical-esign-val.hengrui.com/?taskId=任务id&encodeMail=base64加密邮箱&verificationCode=文件码

                return new ResponseResult<>(200, "success", "签名任务发送成功，任务Id：" + taskId+"设置签字位置的url:"+SASOnlieConstant.ESIGN_SET_SIGN +result.get("fileId")+"&task_id="+taskId);

//        }
    }


    @Override
    public ResponseResult<?> checkFirstSign(String email) {
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "esign_account");
        String accountInfo = CDTMSAPI.getDataListInfo(token, "esign_account", "obj.email='" + email + "'", "", "");
        JSONArray objects = JSONObject.parseArray(accountInfo);
        if (objects.size() > 0) {
            return new ResponseResult<>(200, "found", true);
        } else {
            return new ResponseResult<>(404, "not found", false);
        }
    }

    @Override
    public ResponseResult<?> addSigners(EsignEntity param) {
        //获取添加的签署人信息
        String taskId = param.getTaskId();
        String userName = param.getUserName();
        String userEmail = param.getUserEmail();
        String signReason = param.getSignReason();
        Map<String, String> applyPosConfig = onlyOfficeFileMapper.getSignPosition("rtsm_apply", "申请人");
        if (!StringUtils.isEmpty(taskId) && !StringUtils.isEmpty(userName) && !StringUtils.isEmpty(userEmail) && !StringUtils.isEmpty(signReason)) {
            //获取任务状态，如果任务状态为2，则不允许添加签署人
            cn.hutool.json.JSONObject fileInfo = EsignAPI.getRequest(SASOnlieConstant.ESIGN_TST_TASK + "/" + taskId);
            if (ObjectUtils.isEmpty(fileInfo.get("items"))) {
                return new ResponseResult<>(404, "error", "未查询到该任务!");
            } else {
                int status = (int) JSON.parseObject(JSON.parseArray(fileInfo.get("items").toString()).get(0).toString()).get("status");
                if (status > 1) {
                    switch (status) {
                        case 2:
                            return new ResponseResult<>(403, "error", "该任务已过期，不允许添加签署人!");
                        case 3:
                            return new ResponseResult<>(403, "error", "该任务已作废，不允许添加签署人!");
                        case 4:
                            return new ResponseResult<>(403, "error", "该任务已完成，不允许添加签署人!");
                        default:
                            return new ResponseResult<>(403, "error", "该任务状态异常，不允许添加签署人!");
                    }
                } else {
                    int accountId = 0;
                    //获取该任务下当前所有的签署人
                    List<Map<String, String>> signerList = onlyOfficeFileMapper.getSignerList(taskId);
                    //如果该任务下没有签署人，则添加签署人,否则抛出异常
                    if (signerList.size() > 0) {
                        for (Map<String, String> signer : signerList) {
                            if (signer.get("email").equals(userEmail)) {
                                return new ResponseResult<>(403, "error", "该签署人已存在，不允许重复添加!");
                            }
                        }
                    }
                    //获取fileCode
                    Map<String, String> userInfo = onlyOfficeFileMapper.getAuthorEmailByTaskId(taskId);
                    String fileCode = userInfo.get("fileCode");
                    //添加签署人
                    accountId = addSigner(taskId, userName, userName, userEmail, fileCode, signReason, 0);
                    JSONObject signPosObj = new JSONObject();
                    //，设置默认的签署人签字位置
                    signPosObj = EsignAPI.setDefaultSignerPosition(taskId, accountId, applyPosConfig.get("name_pos_x"),  // 从数据库读取
                            applyPosConfig.get("name_pos_y"),
                            applyPosConfig.get("date_pos_x"),
                            applyPosConfig.get("date_pos_y"),1, signPosObj);
                    String uuid = ULIDGenerator.generateULID();
                    onlyOfficeFileMapper.insertSignUsers(uuid, taskId, userName, userEmail, signReason, SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(userEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + userEmail + "&name=" + userName);
                    String fileName = onlyOfficeFileMapper.getFileNameByTaskId(taskId);
                    String expireDate = onlyOfficeFileMapper.getTaskExpireDate(taskId);
                    String signUrl=SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(userEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + userEmail + "&name=" + userName;
                    Map<String, String> mailMap = mailUtil.sendSignNotification(userEmail, userName, fileName, expireDate, fileCode, signUrl, userName);
                    uuid = UUID.randomUUID().toString();
                    onlyOfficeFileMapper.addSendMailRecord(uuid, userName, userEmail, taskId, mailMap.get("content"), mailMap.get("subject"));
                    log.info("新增的签署人的签署地址是：" + SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(userEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + userEmail + "&name=" + userName);
                }
            }
            return new ResponseResult<>(200, "success", "添加签字人成功!");
        }
        return new ResponseResult<>(400, "error", "请确认传入的参数是否为空!");
    }



    @Override
    public ResponseResult<?> deleteSigner(EsignEntity param) {
        if (!ObjectUtils.isEmpty(param)) {
        String taskId = param.getTaskId();
        String userName = param.getUserName();
        String userEmail = param.getUserEmail();

            // 检查签署任务状态
            //获取任务状态，如果任务状态为2，则不允许添加签署人
            cn.hutool.json.JSONObject fileInfo = EsignAPI.getRequest(SASOnlieConstant.ESIGN_TST_TASK + "/" + taskId);
            if (ObjectUtils.isEmpty(fileInfo.get("items"))) {
                return new ResponseResult<>(404, "error", "未查询到该任务!");
            }
            int taskStatus = (int) JSON.parseObject(JSON.parseArray(fileInfo.get("items").toString()).get(0).toString()).get("status");
            log.info("查询的任务的状态为："+taskStatus);
            if (taskStatus > 1) {
                switch (taskStatus) {
                    case 2:
                        return new ResponseResult<>(403, "error", "该任务已过期，不允许删除签署人!");
                    case 3:
                        return new ResponseResult<>(403, "error", "该任务已作废，不允许删除签署人!");
                    case 4:
                        return new ResponseResult<>(403, "error", "该任务已完成，不允许删除签署人!");
                    default:
                        return new ResponseResult<>(403, "error", "该任务状态异常，不允许删除签署人!");
                }
            }
            // 删除签署人信息
            String encode=  Base64.getEncoder().encodeToString(userEmail.getBytes());
            cn.hutool.json.JSONObject  signUsers = EsignAPI.getRequest(SASOnlieConstant.ESIGN_TST_TASK+"/"+taskId+"/users?per_page=100&page=1");
            log.info(signUsers.toString());
            if(!ObjectUtils.isEmpty(signUsers)){
                JSONArray array = JSON.parseArray(signUsers.get("items").toString());
                int userId=-1;
                for(int i=0;i<array.size();i++){
                    if(encode.equals(array.getJSONObject(i).get("email").toString())){
                        userId= (int) array.getJSONObject(i).get("id");
                    }
                }
               if(userId==-1){
                   return new ResponseResult<>(404, "error", "未查询到该签署人!");
               }
                String result = EsignAPI.deleteRequest(SASOnlieConstant.ESIGN_TST_TASK + "/" + taskId + "/users/" + userId);
                onlyOfficeFileMapper.deleteSignUser(taskId, userEmail);
                String fileName=onlyOfficeFileMapper.getFileNameByTaskId(taskId);
                mailUtil.sendSignerRemoveNotification(userEmail,userName,fileName);
                log.info("删除签署人成功 - taskId: {}, userName: {}, userEmail: {}", taskId, userName, userEmail);
                return new ResponseResult<>(200, "success", "删除签署人成功!");
            }else{
                return new ResponseResult<>(404, "error", "未查询到该任务!");
            }

        }
        return new ResponseResult<>(400, "error", "请确认传入的参数是否为空!");
    }

    @Override
    public ResponseResult<?> resendNotification(String taskId, String email, String userName) {
        if(StringUtils.isEmpty(taskId)&&StringUtils.isEmpty(email)&&StringUtils.isEmpty(userName)){
            return new ResponseResult<>(400, "error", "请确认传入的参数是否为空!");
        }
         Map<String, String> mailInfoByTaskIdEmail = onlyOfficeFileMapper.getMailInfoByTaskIdEmail(taskId, userName, email);
        if(!ObjectUtils.isEmpty(mailInfoByTaskIdEmail)){
            String subject=mailInfoByTaskIdEmail.get("subject");
            String content=mailInfoByTaskIdEmail.get("content");
            mailUtil.sendMailWithHtmlFormat(email, subject, content);
            String uuid = UUID.randomUUID().toString();
            onlyOfficeFileMapper.addSendMailRecord(uuid, userName, email, taskId, content, subject);
            return new ResponseResult<>(200, "success", "重发通知成功!");
        }else{
            return new ResponseResult<>(404, "error", "该任务暂无历史邮件通知!");
        }

    }

    @Override
    public String addAuditAndApprovalTable(String studyId, String userName, String param) {
        //解析参数
        if (!ObjectUtils.isEmpty(studyId) && !ObjectUtils.isEmpty(userName) && !studyId.isEmpty() && !userName.isEmpty()&& !ObjectUtils.isEmpty(param)&& !param.isEmpty())  {
            //填充模板内容,及动态生成
            //formResult 转换成json对象
            JSONObject jsonObject = JSONObject.parseObject(param);
            String accountInfo = jsonObject.get("accountInfo").toString();
            JSONArray accountList = JSONArray.parseArray(accountInfo);
            String uuid = ULIDGenerator.generateULID();
            //2.文件批次号去重查询，获取版本号
            int numPrefix = onlyOfficeFileMapper.getAAVersionNumPrefix(studyId);
            int num = onlyOfficeFileMapper.getAAVersionNum(studyId, "V" + numPrefix + ".");
            String latestVersion=onlyOfficeFileMapper.getAALatestVersion(studyId);
            Boolean isIndexZero=false;
            if(!StringUtils.isEmpty(latestVersion)&&latestVersion.contains(".0")){
                isIndexZero=true;
            }
            int versionNumber=0;
            if(num==1&&isIndexZero){
                versionNumber = 1;
            }else if(numPrefix==0){
                versionNumber = num+1;
            }else{
                versionNumber = num;
            }
            String versionNum = "V" + numPrefix + "." + versionNumber;
            onlyOfficeFileMapper.insertAuditApproval(uuid, userName, "", studyId, "", versionNum, param);
            //多个统计师审核记录新增
            String accountName = "";
            String accountEmail = "";
            for (int i = 0; i < accountList.size(); i++) {
                accountName = JSONObject.parseObject(accountList.get(i).toString()).get("accountName").toString();
                accountEmail = JSONObject.parseObject(accountList.get(i).toString()).get("account").toString();
                String reviewerId = ULIDGenerator.generateULID();
                onlyOfficeFileMapper.insertAAReviewers(reviewerId, uuid, accountName, accountEmail);
            }


            int fileType = (int) jsonObject.get("fileType");
            String inputFilePath="";
            String outputFilePath ="";
            //创建随机分配表与药物编号表审核表、审批表模版
            switch (fileType){
                case 1:
                     inputFilePath = "/home/<USER>/8087/rtsm_file/DM-FM-025随机分配列表（测试版）与药物编号列表（测试版）审核表-RandQCV1.docx";
                     outputFilePath = "/home/<USER>/8087/rtsm_file/"+studyId+"_随机分配列表（测试版）与药物编号列表（测试版）审核表-RandQC"+versionNum+".docx";
                    break;
                case 2:
                    inputFilePath = "/home/<USER>/8087/rtsm_file/DM-FM-025随机分配列表（测试版）与药物编号列表（测试版）审核表-RandQCV2.docx";
                    outputFilePath = "/home/<USER>/8087/rtsm_file/"+studyId+"_随机分配列表（测试版）与药物编号列表（测试版）审核表-RandQC"+versionNum+".docx";
                    break;
                case 3:
                    inputFilePath = "/home/<USER>/8087/rtsm_file/DM-FM-025随机分配列表（测试版）与药物编号列表（测试版）审核表-RandQCV3.docx";
                    outputFilePath = "/home/<USER>/8087/rtsm_file/"+studyId+"_随机分配列表（测试版）与药物编号列表（测试版）审核表-RandQC"+versionNum+".docx";
                    break;
                case 4:
                    inputFilePath = "/home/<USER>/8087/rtsm_file/DM-FM-026随机分配列表（测试版）与药物编号列表（测试版）审核表-统计师审核V1.docx";
                    outputFilePath = "/home/<USER>/8087/rtsm_file/"+studyId+"_随机分配列表（测试版）与药物编号列表（测试版）审核表-统计师审核"+versionNum+".docx";
                    break;
                case 5:
                    inputFilePath = "/home/<USER>/8087/rtsm_file/DM-FM-026随机分配列表（测试版）与药物编号列表（测试版）审核表-统计师审核V2.docx";
                    outputFilePath = "/home/<USER>/8087/rtsm_file/"+studyId+"_随机分配列表（测试版）与药物编号列表（测试版）审核表-统计师审核"+versionNum+".docx";
                    break;
                case 6:
                    inputFilePath = "/home/<USER>/8087/rtsm_file/DM-FM-026随机分配列表（测试版）与药物编号列表（测试版）审核表-统计师审核V3.docx";
                    outputFilePath = "/home/<USER>/8087/rtsm_file/"+studyId+"_随机分配列表（测试版）与药物编号列表（测试版）审核表-统计师审核"+versionNum+".docx";
                    break;
                case 7:
                    inputFilePath = "/home/<USER>/8087/DM-FM-027随机分配列表（正式版）与药物编号列表（正式版）生效审批表V1.docx";
                    outputFilePath = "/home/<USER>/8087/rtsm_file/"+studyId+"_随机分配列表（正式版）与药物编号列表（正式版）生效审批表"+versionNum+".docx";
                    break;
                case 8:
                    inputFilePath = "/home/<USER>/8087/rtsm_file/DM-FM-027随机分配列表（正式版）与药物编号列表（正式版）生效审批表V2.docx";
                    outputFilePath = "/home/<USER>/8087/rtsm_file/"+studyId+"_随机分配列表（正式版）与药物编号列表（正式版）生效审批表"+versionNum+".docx";
                    break;
                case 9:
                    inputFilePath = "/home/<USER>/8087/rtsm_file/DM-FM-027随机分配列表（正式版）与药物编号列表（正式版）生效审批表V3.docx";
                    outputFilePath = "/home/<USER>/8087/rtsm_file/"+studyId+"_随机分配列表（正式版）与药物编号列表（正式版）生效审批表"+versionNum+".docx";
                    break;
                    default:
                        log.info("未提供准确的审核、审批表类型！");
                        return "fail";
            }
            String fileName = makeRTSMAATemplate(inputFilePath, outputFilePath, param,studyId,accountList);
            onlyOfficeFileMapper.updateAuditApprovalById(uuid,fileName);

            return uuid;
        }
        return "fail";

    }

    @Override
    public ResponseResult<?> sendAuditAndApprovalSign(String param, String studyId, String fileKey, String userName) {
        //更新审核、审批表主表的审核状态
        onlyOfficeFileMapper.updateAAApproveStatus(studyId, fileKey);
        //获取文件位置
        String filePath= onlyOfficeFileMapper.getAAApprovedFilePath(studyId,fileKey);
        //docx转pdf
        if(StringUtils.isEmpty(filePath)){
            return new ResponseResult<>(404, "error", "未查询到该表的文件路径!");
        }else{
            String pdfPath = filePath.replace(".docx", ".pdf");
            pdfPath=incrementVersion(pdfPath);
            try {
                Doc2Pdf.doc2pdf(
                        filePath,
                        pdfPath
                );
                //更新pdf文件路径
                 onlyOfficeFileMapper.updateAASignFilePath(studyId, fileKey,pdfPath);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }




        //获取邮件标题和正文
        JSONObject mailInfo = JSON.parseObject(param);
        //邮件标题
        String title = mailInfo.get("title").toString();
        String content = mailInfo.get("content").toString();
        //存储邮件标题和正文 fileKey
        if (StringUtils.isEmpty(title) || StringUtils.isEmpty(content)) {
            return new ResponseResult<>(401, "error", "邮件正文或内容不能为空!");
        }


        //获取该条文件的参数，判断签字人信息是否为空，如果为空则使用默认的签字人信息
        String paramter = onlyOfficeFileMapper.getAARecordParamByFileKey(studyId, fileKey);
        if (ObjectUtils.isEmpty(paramter) || paramter.isEmpty()) {
            return new ResponseResult<>(400, "error", "该文件申请表参数未录入!");
        }
        JSONObject paramObj = JSONObject.parseObject(paramter);
        int fileType = (int) paramObj.get("fileType");

        String receivers = mailInfo.get("accountInfo").toString();
        JSONArray accountList = JSONArray.parseArray(receivers);
        if (accountList.size() == 0) {
            return new ResponseResult<>(400, "error", "没有设置签字文件接收人信息!");
        }

        //存储签字人信息

        onlyOfficeFileMapper.updateRTSMAAMailInfo(title, content, receivers, fileKey);


        Map<String, String> randSpecialPosConfig = onlyOfficeFileMapper.getSignPosition("rtsm_audit_approval", "随机专员");
        Map<String, String> randQCPosConfig = onlyOfficeFileMapper.getSignPosition("rtsm_audit_approval", "随机化质量控制专员");
        Map<String, String> randManagerPosConfig = onlyOfficeFileMapper.getSignPosition("rtsm_audit_approval", "随机化负责人");
        Map<String, String> accountPosConfig = onlyOfficeFileMapper.getSignPosition("rtsm_audit_approval", "生物统计师");
        Map<String, String> randSpecialPosConfigApproval = onlyOfficeFileMapper.getSignPosition("rtsm_audit_approval", "随机专员-审批表");
        Map<String, String> randQCPosConfigApproval = onlyOfficeFileMapper.getSignPosition("rtsm_audit_approval", "随机化质量控制专员-审批表");

        String  reviewer = "";
        String approver="";
        String author="";
        Map<String, String> randReviewer = getSignerInfoByRole("随机专员", accountList);
        if(!org.apache.commons.collections4.MapUtils.isEmpty(randReviewer)){
            author= randReviewer.get("name");
        }
        if(fileType==1||fileType==2||fileType==3){
            randReviewer = getSignerInfoByRole("随机化质量控制专员", accountList);
            if(!org.apache.commons.collections4.MapUtils.isEmpty(randReviewer)){
                reviewer= randReviewer.get("name");
            }
        }else if (fileType==4||fileType==5||fileType==6){
            Map<String, String> accountReviewer = getSignerInfoByRole("生物统计师", accountList);
            if(!org.apache.commons.collections4.MapUtils.isEmpty(accountReviewer)){
                reviewer= accountReviewer.get("name");
            }
        }else if(fileType==7||fileType==8||fileType==9){
            Map<String, String> accountReviewer = getSignerInfoByRole("随机化质量控制专员", accountList);
            if(!org.apache.commons.collections4.MapUtils.isEmpty(accountReviewer)){
                reviewer= accountReviewer.get("name");
            }
            Map<String, String> approverInfo = getSignerInfoByRole("随机化负责人", accountList);
            if(!org.apache.commons.collections4.MapUtils.isEmpty(accountReviewer)){
                approver= approverInfo.get("name");
            }
        }


        //统计师链接参数获取
        Map<String, String> accountSignEmailParams = onlyOfficeFileMapper.getAAAccountSignEmailParams(fileKey);
        String batchNum = accountSignEmailParams.get("batch_num");
        String accountUrl=SASOnlieConstant.RTSM_GENERATOR_URL+studyId+"&role=account&username="+reviewer+"&batchNum="+batchNum;

        JSONObject signPosObj = new JSONObject();
        String authorEmail = "";
        Map<String,String> signerInfoByName = getSignerInfoByRole("随机专员", accountList);
        if(!org.apache.commons.collections4.MapUtils.isEmpty(signerInfoByName)){
            authorEmail =signerInfoByName.get("email");
        }



        //1.1查询有无这条记录
        int count = onlyOfficeFileMapper.checkAAPDFFileIsExist(studyId, fileKey);
        //1.2变更该条记录的状态为已审核
        if (count > 0) {
            //1.3获取文件位置
            String signFilePath = onlyOfficeFileMapper.getAASignFilePath(studyId, fileKey);
            if (ObjectUtils.isEmpty(signFilePath) || signFilePath.isEmpty()) {
                return new ResponseResult<>(404, "error", "未查询到该签字文件!");
            }



            //1.4调用上传文件接口
            Map<String, String> result = null;
            try {
                result = EsignAPI.uploadFile(signFilePath, SASOnlieConstant.ESIGN_TST_FILE + "/upload");
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            log.info("获取到的文件id为{}，文件名称为{}", result.get("fileId"), result.get("filename"));
            String fileName= result.get("filename");
            //1.5存储到esign_sign_files表
            File pdfFile = new File(signFilePath);
            String fileMd5 = decode64Util.getMd5(pdfFile);
            //生成一个6位随机数
            String fileCode = com.hengrui.blind_back.utils.FileUtils.getRandomString(6);
            onlyOfficeFileMapper.insertSignFile(result.get("fileId"), result.get("filename"), signFilePath, fileMd5, userName, studyId, fileCode);
            //2.创建任务
            JSONObject taskParam = new JSONObject();
            taskParam.put("file_id", result.get("fileId"));
            taskParam.put("expiration_date", com.hengrui.blind_back.utils.FileUtils.getExpirationTime());
            taskParam.put("callback_url", SASOnlieConstant.SAS_ONLINE + "ESignCallBack");
            taskParam.put("file_code", fileMd5);
            taskParam.put("file_author", userName);
            taskParam.put("created_by", accountUrl);
            taskParam.put("file_status", "Under signing");
            taskParam.put("sign_type", 0);
            String taskInfo = EsignAPI.postRequest(SASOnlieConstant.ESIGN_TST_TASK, taskParam.toString());
            String taskData = JSONObject.parseObject(taskInfo).get("data").toString();
            JSONObject taskDataObj = JSONObject.parseObject(taskData);
            String taskId = taskDataObj.get("task_id").toString();
            //生成签字任务记录表记录
            Map<String, Object> params = new HashMap<>();
            params.put("taskId", taskId);
            params.put("fileId", result.get("fileId"));
            params.put("fileCode", fileCode);
            params.put("userName", userName);
            params.put("studyId", studyId);
            params.put("expirationDate", new Timestamp((Long) taskParam.get("expiration_date"))); // 时间戳（毫秒）转换为 Timestamp
            params.put("email", authorEmail);
            params.put("fileKey",fileKey);
            onlyOfficeFileMapper.insertSignTask(params);



            //userid用于设置默认的签署位置用-更新签署人位置
            int applicantId = 0;
            int accountId = 0;
            int accountManagerId = 0;
            String accountName = "";
            String accountEmail = "";
            if (fileType == 1||fileType==2||fileType==3) {
                // 添加签署人-申请人
                applicantId = addSigner(taskId, author, author, authorEmail, fileCode, "我是该文件作者", 1);
                signPosObj = EsignAPI.setDefaultSignerPosition(taskId, applicantId, randSpecialPosConfig.get("name_pos_x"),  // 从数据库读取
                        randSpecialPosConfig.get("name_pos_y"),
                        randSpecialPosConfig.get("date_pos_x"),
                        randSpecialPosConfig.get("date_pos_y"),  3, signPosObj);
                log.info("作者的签署地址是：" + SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(authorEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + authorEmail + "&name=" + author+"&fileName="+fileName);
                //存储签字人信息到esign_users
                String uuid = ULIDGenerator.generateULID();
                //申请人
                onlyOfficeFileMapper.insertSignUsers(uuid, taskId, userName, authorEmail, "我是该文件作者", SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(authorEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + authorEmail + "&name=" + userName+"&fileName="+fileName);



                // 添加签署人-申请人
                //    applicantId = addSigner(taskId, userName, userName, authorEmail, fileCode, "我是申请人", 1);
                // 添加签署人-统计师
                Map<String,String > accountInfo=getSignerInfoByRole("随机化质量控制专员",accountList);
                accountEmail = accountInfo.get("email");
                accountName = accountInfo.get("name");

                accountId = addSigner(taskId, accountName, accountName, accountEmail, fileCode, "我审阅该文件", 0);
                signPosObj = EsignAPI.setDefaultSignerPosition(taskId, accountId,  randQCPosConfig.get("name_pos_x"),  // 从数据库读取
                        randQCPosConfig.get("name_pos_y"),
                        randQCPosConfig.get("date_pos_x"),
                        randQCPosConfig.get("date_pos_y"),  3, signPosObj);
                //统计师
                uuid = ULIDGenerator.generateULID();
                onlyOfficeFileMapper.insertSignUsers(uuid, taskId, accountName, accountEmail, "我审阅该文件", SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(accountEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + accountEmail + "&name=" + accountName+"&fileName="+fileName);
                log.info("随机质量控制专员的签署地址是：" + SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(accountEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + accountEmail + "&name=" + accountName+"&fileName="+fileName);


            } else if (fileType == 4||fileType==5||fileType==6) {
                // 添加签署人-申请人
                applicantId = addSigner(taskId, author, author, authorEmail, fileCode, "我是该文件作者", 1);
                signPosObj = EsignAPI.setDefaultSignerPosition(taskId, applicantId, randSpecialPosConfig.get("name_pos_x"),  // 从数据库读取
                        randSpecialPosConfig.get("name_pos_y"),
                        randSpecialPosConfig.get("date_pos_x"),
                        randSpecialPosConfig.get("date_pos_y"),  2, signPosObj);
                log.info("作者的签署地址是：" + SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(authorEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + authorEmail + "&name=" + author+"&fileName="+fileName);
                //存储签字人信息到esign_users
                String uuid = ULIDGenerator.generateULID();
                //申请人
                onlyOfficeFileMapper.insertSignUsers(uuid, taskId, userName, authorEmail, "我是该文件作者", SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(authorEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + authorEmail + "&name=" + userName+"&fileName="+fileName);

                // 添加签署人-统计师
                Map<String,String > accountInfo=getSignerInfoByRole("生物统计师",accountList);
                accountEmail = accountInfo.get("email");
                accountName = accountInfo.get("name");

                accountId = addSigner(taskId, accountName, accountName, accountEmail, fileCode, "我审阅该文件", 0);
                signPosObj = EsignAPI.setDefaultSignerPosition(taskId, accountId,  accountPosConfig.get("name_pos_x"),  // 从数据库读取
                        accountPosConfig.get("name_pos_y"),
                        accountPosConfig.get("date_pos_x"),
                        accountPosConfig.get("date_pos_y"),  2, signPosObj);
                //统计师
                uuid = ULIDGenerator.generateULID();
                onlyOfficeFileMapper.insertSignUsers(uuid, taskId, accountName, accountEmail, "我审阅该文件", SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(accountEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + accountEmail + "&name=" + accountName+"&fileName="+fileName);
                log.info("生物统计师的签署地址是：" + SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(accountEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + accountEmail + "&name=" + accountName+"&fileName="+fileName);


            } else if (fileType == 7||fileType==8||fileType==9) {
                // 添加签署人-申请人
                applicantId = addSigner(taskId, author, author, authorEmail, fileCode, "我是该文件作者", 1);
                signPosObj = EsignAPI.setDefaultSignerPosition(taskId, applicantId, randSpecialPosConfigApproval.get("name_pos_x"),  // 从数据库读取
                        randSpecialPosConfigApproval.get("name_pos_y"),
                        randSpecialPosConfigApproval.get("date_pos_x"),
                        randSpecialPosConfigApproval.get("date_pos_y"),  2, signPosObj);
                log.info("作者的签署地址是：" + SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(authorEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + authorEmail + "&name=" + author+"&fileName="+fileName);
                //存储签字人信息到esign_users
                String uuid = ULIDGenerator.generateULID();
                //申请人
                onlyOfficeFileMapper.insertSignUsers(uuid, taskId, userName, authorEmail, "我是该文件作者", SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(authorEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + authorEmail + "&name=" + userName+"&fileName="+fileName);

                Map<String,String > accountInfo=getSignerInfoByRole("随机化质量控制专员",accountList);
                accountEmail = accountInfo.get("email");
                accountName = accountInfo.get("name");
                accountId = addSigner(taskId, accountName, accountName, accountEmail, fileCode, "我审阅该文件", 0);
                signPosObj = EsignAPI.setDefaultSignerPosition(taskId, accountId, randQCPosConfigApproval.get("name_pos_x"),  // 从数据库读取
                        randQCPosConfigApproval.get("name_pos_y"),
                        randQCPosConfigApproval.get("date_pos_x"),
                        randQCPosConfigApproval.get("date_pos_y"),2, signPosObj);
                 uuid = ULIDGenerator.generateULID();
                onlyOfficeFileMapper.insertSignUsers(uuid, taskId, accountName, accountEmail, "我审阅该文件", SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(accountEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + accountEmail + "&name=" + accountName+"&fileName="+fileName);
                log.info("随机化质量控制专员的签署地址是：" + SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(accountEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + accountEmail + "&name=" + accountName+"&fileName="+fileName);


                Map<String,String > randManagerInfo=getSignerInfoByRole("随机化负责人",accountList);
                accountEmail = randManagerInfo.get("email");
                accountName = randManagerInfo.get("name");
                accountId = addSigner(taskId, accountName, accountName, accountEmail, fileCode, "我批准该文件", 0);
                signPosObj = EsignAPI.setDefaultSignerPosition(taskId, accountId, randManagerPosConfig.get("name_pos_x"),  // 从数据库读取
                        randManagerPosConfig.get("name_pos_y"),
                        randManagerPosConfig.get("date_pos_x"),
                        randManagerPosConfig.get("date_pos_y"),2, signPosObj);
                uuid = ULIDGenerator.generateULID();
                onlyOfficeFileMapper.insertSignUsers(uuid, taskId, accountName, accountEmail, "我批准该文件", SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(accountEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + accountEmail + "&name=" + accountName+"&fileName="+fileName);
                log.info("随机化负责人的签署地址是：" + SASOnlieConstant.ESIGN_API_PREFIX + "?taskId=" + taskId + "&encodeMail=" + Base64.getEncoder().encodeToString(accountEmail.getBytes()) + "&verificationCode=" + fileCode + "&email=" + accountEmail + "&name=" + accountName+"&fileName="+fileName);

            }

            //任务下发
            JSONObject taskDisParam = new JSONObject();
            taskDisParam.put("type", 1);
            taskDisParam.put("remark", studyId + "_项目随机申请表下发");
         //   String taskDis = EsignAPI.putRequest(SASOnlieConstant.ESIGN_TST_TASK + "/" + taskId, taskDisParam.toString());
         //   log.info(taskDis);

            //签署 地址 https:///clinical-esign-val.hengrui.com/?taskId=任务id&encodeMail=base64加密邮箱&verificationCode=文件码

            return new ResponseResult<>(200, "success", "签名任务发送成功，任务Id：" + taskId+"设置签字位置的url:"+SASOnlieConstant.ESIGN_SET_SIGN +result.get("fileId")+"&task_id="+taskId);

        }
        return new ResponseResult<>(400, "error", "该文件未查询到审批记录!");
    }

    @Override
    public Map<String, String> getAuditApprovalInfo(String studyId, String batchNum) {
        if (!studyId.isEmpty()) {
            return onlyOfficeFileMapper.getAuditApprovalInfo(studyId, batchNum);
        } else {
            return null;
        }
    }

    private String makeRTSMAATemplate(String inputFile,String outputFile, String param,String studyId,JSONArray accountList) {

        JSONObject jsonObject = JSONObject.parseObject(param);
        //fileType 0 审批表 1 RandQC 审核表 2 统计师审核
        String randFileNames = jsonObject.get("randFilename").toString();
        //转成JSONArray
        JSONArray randArray = JSONArray.parseArray(randFileNames);
        if(randArray.size()>0){
            randFileNames=randArray.stream()
                    .map(Object::toString)
                    .collect(Collectors.joining(","));
        }else {
            randFileNames="";
        }

        String medFileNames = jsonObject.get("medicFilename").toString();
        JSONArray medArray = JSONArray.parseArray(medFileNames);
        if(medArray.size()>0){
            medFileNames=medArray.stream()
                    .map(Object::toString)
                    .collect(Collectors.joining(","));
        }else{
            medFileNames="";
        }
        int applyType= (int) jsonObject.get("applyType");
        Map<String, String> params = new HashMap<>();
        switch (applyType){
            case 1:
                params.put("applyType", "初始Original");
                break;
            case 2:
                params.put("applyType", "扩展Extension");
                break;
            case 3:
                params.put("applyType", "修订/替换Revision/Replacement");
                break;
            default:
                log.info("未提供准确的属性类型；");

        }
        int fileType= (int) jsonObject.get("fileType");
        Map<String, String>  signerInfo=new HashMap<>();
        String reviewer="";
        String author="";
        Map<String, String> randReviewer = getSignerInfoByRole("随机专员", accountList);
        if(!org.apache.commons.collections4.MapUtils.isEmpty(randReviewer)){
            author= randReviewer.get("name");
            params.put("随专", author);
            params.put("RQ", author);
        }

        if(fileType==1||fileType==2||fileType==3){
            signerInfo = getSignerInfoByRole("随机化质量控制专员", accountList);
            if(!org.apache.commons.collections4.MapUtils.isEmpty(signerInfo)){
                reviewer= signerInfo.get("name");
                params.put("随负", reviewer);
                params.put("RL", reviewer);
            }
        }else if (fileType==4||fileType==5||fileType==6){
            Map<String, String> accountReviewer = getSignerInfoByRole("生物统计师", accountList);
            if(!org.apache.commons.collections4.MapUtils.isEmpty(accountReviewer)){
                reviewer= accountReviewer.get("name");
                params.put("随负", reviewer);
                params.put("RL", reviewer);
            }
        }else if(fileType==7||fileType==8||fileType==9){
            if(!org.apache.commons.collections4.MapUtils.isEmpty(randReviewer)){
                author= randReviewer.get("name");
                params.put("随机专", author);
                params.put("RS", author);
            }

            Map<String, String> accountReviewer = getSignerInfoByRole("随机化质量控制专员", accountList);
            if(!org.apache.commons.collections4.MapUtils.isEmpty(accountReviewer)){
                log.info("随机化质量控制专员:"+accountReviewer.get("name"));
                reviewer= accountReviewer.get("name");
                params.put("RQ", reviewer);
                params.put("随专", reviewer);
            }

            Map<String, String> approverInfo = getSignerInfoByRole("随机化负责人", accountList);
            if(!org.apache.commons.collections4.MapUtils.isEmpty(accountReviewer)){
                reviewer= approverInfo.get("name");
                params.put("随负", reviewer);
                params.put("RL", reviewer);
            }
        }

        params.put("RandomFileNames", randFileNames);
        params.put("MedFileNames", medFileNames);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate currentDate = LocalDate.now();
        String currentDateStr = currentDate.format(formatter);
        params.put("applyDate", jsonObject.get("applyDate").toString());
        params.put("rtsmApplyVersion",  jsonObject.get("applyVersion").toString());
        params.put("caseNum",  studyId);
        if(!StringUtils.isEmpty( jsonObject.get("isUploadRand").toString())){
            params.put("isUploadRand", jsonObject.get("isUploadRand").toString());
        }

        if(!StringUtils.isEmpty( jsonObject.get("isUploadMedic").toString())){
            params.put("isUploadMedic", jsonObject.get("isUploadMedic").toString());
        }

        try {
            fileUtils.replaceVariables(inputFile, outputFile, params);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (InvalidFormatException e) {
            throw new RuntimeException(e);
        }
        return  outputFile;
    }


    @Override
    public ResponseResult<?> checkSignPass(String email, String pass, String taskId) {
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "esign_account");
        String accountInfo = CDTMSAPI.getDataListInfo(token, "esign_account", "obj.email='" + email + "'", "", "");
        JSONArray objects = JSONObject.parseArray(accountInfo);
        if (objects.size() > 0) {
            String userPass = objects.getJSONObject(0).get("password").toString();
            String signUrl = onlyOfficeFileMapper.getSignUrlByTaskId(taskId, email);
            if (pass.equals(userPass)) {
                return new ResponseResult<>(200, "success", signUrl);
            } else {
                return new ResponseResult<>(401, "unauthorized", "签字密码错误!");
            }
        } else {
            return new ResponseResult<>(404, "not found", "未查询到用户密码，请确认您已经在CDTMS中设置过签字密码!");
        }
    }

    @Override
    public ResponseResult<?> getEsignInfo(EsignEntity param) {
        List<Map<String, Object>> esignInfo = onlyOfficeFileMapper.getEsignInfo(param);
        return new ResponseResult<>(200, "success", esignInfo);
    }


    /**
     * 补签
     *
     * @param param
     * @return
     */
    @Override
    public ResponseResult<?> resign(EsignEntity param) {
        Boolean isAccountSign=true;
        String studyId = param.getStudyId();
        String fileKey = param.getFileKey();
        String userName = param.getAuthor();
       String signers=onlyOfficeFileMapper.getSignerListByFileKey(studyId,fileKey);
       if(StringUtils.isEmpty(signers)){
            signers=onlyOfficeFileMapper.getAASignerListByFileKey(studyId,fileKey);
           isAccountSign=false;
       }
       Map<String,String> mailInfo=onlyOfficeFileMapper.getMailInfoByFileKey(studyId,fileKey);
       if(org.apache.commons.collections4.MapUtils.isEmpty(mailInfo)){
           mailInfo=onlyOfficeFileMapper.getAAMailInfoByFileKey(studyId,fileKey);
           isAccountSign=false;
       }
       String title=mailInfo.get("title");
       String content=mailInfo.get("content");
       String remark=mailInfo.get("remark");

       String signFilePath=mailInfo.get("signFilePath");

       JSONObject remarkInfo = JSON.parseObject(remark);
        int rmdm =0;
       if(null!=remarkInfo.get("fileType")){
           rmdm = (int) remarkInfo.get("fileType");
       }else{
           //随机申请表
           rmdm = (int) remarkInfo.get("rmdm");
       }

        // 组装最终的JSON对象
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("accountInfo", signers);
        jsonObject.put("title", title);
        jsonObject.put("content", content);
        jsonObject.put("rmdm", rmdm);
        jsonObject.put("signFilePath", signFilePath);

        // 转换为JSON字符串
        String jsonString = jsonObject.toJSONString();
        if(isAccountSign){
            return sendSign(jsonString,studyId, fileKey, userName);
        }else{
            return sendAuditAndApprovalSign(jsonString,studyId, fileKey, userName);
        }



    }

    @Override
    public ResponseResult<?> setSignPass(String email, String pass) {
        //cdtms esign账户新增该邮箱账户密码
        if (StringUtils.isEmpty(email) || StringUtils.isEmpty(pass)) {
            return new ResponseResult<>(500, "error", "邮箱或密码不能为空！");
        }
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "esign_account");
        String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
        com.alibaba.fastjson.JSONObject formData = new JSONObject();
        formData.put("email", email);
        formData.put("password", pass);
        String esignAccount = CDTMSAPI.usersyndataSave(token, "esign_account", formId, "", "", formData.toString());
        return new ResponseResult<>(200, "success", esignAccount);
    }


    public void uploadFile(String savePath, String fid, String taskId, String projectId) {
        //上传附件
        File saveFile = new File(savePath);
        if (saveFile.exists()) {
            String latestFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
            FileUtil.uploadSASOutputFile(taskId, latestFormId, fid, savePath, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.REMOTE_SERVER_API_PREFIX, saveFile.getName(), "csv");
        }
    }


    @Override
    public ResponseResult<?> cancelSignTask(EsignEntity param) {
        String taskId = param.getTaskId();
        String userName = param.getUserName();
        String userEmail = param.getUserEmail();
        String fileKey= param.getFileKey();
        if (StringUtils.isEmpty(taskId) || StringUtils.isEmpty(userName) || StringUtils.isEmpty(userEmail) || StringUtils.isEmpty(fileKey)) {
            return new ResponseResult<>(400, "error", "请确认传入的参数是否为空!");
        }
        //获取任务状态，如果任务状态为2，则不允许添加签署人
        cn.hutool.json.JSONObject fileInfo = EsignAPI.getRequest(SASOnlieConstant.ESIGN_TST_TASK + "/" + taskId);
        if (ObjectUtils.isEmpty(fileInfo.get("items"))) {
            return new ResponseResult<>(404, "error", "未查询到该签字任务!");
        } else {
            int status = (int) JSON.parseObject(JSON.parseArray(fileInfo.get("items").toString()).get(0).toString()).get("status");
            if (status <1) {
                return new ResponseResult<>(403, "error", "仅可以终止处理中的任务!");
            } else {
                JSONObject taskDisParam = new JSONObject();
                taskDisParam.put("type", 2);
                taskDisParam.put("remark", userName + "操作取消签署任务:" + taskId + ",邮箱:" + userEmail);
                String taskDis = EsignAPI.putRequest(SASOnlieConstant.ESIGN_TST_TASK + "/" + taskId, taskDisParam.toString());
                JSONObject cancelResult = JSONObject.parseObject(taskDis);
                if (200 == (int) cancelResult.get("status")) {
                    onlyOfficeFileMapper.updateCancelSign(taskId, userName, userEmail);
                    //更新申请表的审核状态
                    onlyOfficeFileMapper.updateApproveStatusByFileKey(fileKey);
                    onlyOfficeFileMapper.updateAAApproveStatusByFileKey(fileKey);
                    log.info(taskDis);
                    return new ResponseResult<>(200, "success", "取消签署任务成功!");
                }else{
                    String msg = (String) cancelResult.get("message");
                    return new ResponseResult<>(400, "error", "取消签署任务失败:"+msg);
                }

            }
        }
    }

    @Override
    public String getApproveStatusByName(String studyId, String batchNum, String userName) {
         return onlyOfficeFileMapper.getApproveStatusByName(studyId, batchNum, userName);
    }




//24小时临期邮件通知
    @Override
    public void checkExpiringSignTasks() {
        try {
            // 获取所有状态为1（进行中）的签字任务
            List<Map<String, String>> expiringTasks = onlyOfficeFileMapper.getExpiringSignTasks();

            for (Map<String, String> task : expiringTasks) {
                String taskId = task.get("taskId");
                String expireTime = task.get("expirationDate");
                String fileName = task.get("fileName");

                // 获取该任务下所有未签署的签署人
                List<Map<String, String>> unsignedSigners = onlyOfficeFileMapper.getUnsignedSigners(taskId);

                for (Map<String, String> signer : unsignedSigners) {
                    String email = signer.get("email");
                    String name = signer.get("username");
                    String signUrl= signer.get("signUrl");

                    // 发送临期通知邮件
                    Map<String, String> mailMap = mailUtil.sendSignExpiringNotification(email, name, fileName, expireTime,signUrl);

                    // 记录邮件发送记录
                    String uuid = UUID.randomUUID().toString();
                    onlyOfficeFileMapper.addSendMailRecord(uuid, name, email, taskId, mailMap.get("content"), mailMap.get("subject"));
                    // 更新通知状态，避免重复发送
                    onlyOfficeFileMapper.updateSignerNotifyStatus(taskId, email);
                }
            }
        } catch (Exception e) {
            log.error("检查临期签字任务失败", e);
        }
    }




}
