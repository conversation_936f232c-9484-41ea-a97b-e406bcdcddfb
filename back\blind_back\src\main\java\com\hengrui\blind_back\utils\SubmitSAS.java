package com.hengrui.blind_back.utils;

import cn.hutool.json.JSONObject;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.hengrui.blind_back.blind.constant.BlindConstant;
import com.hengrui.blind_back.blind.mapper.EDMCDTMSInfoMapper;
import com.hengrui.blind_back.blind.utils.Decode64Util;
import com.hengrui.blind_back.blind.utils.MinioUtil;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.ecrf_unlock.mapper.ECRFUnlockMapper;
import com.hengrui.blind_back.ecrf_unlock.utils.EcrfSASTrigger;
import com.hengrui.blind_back.ecrf_unlock.utils.FileUtil;
import com.sas.services.connection.ConnectionFactoryException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;


/**
 * @ClassName SubmitSAS
 * @Description abstract the submit to sas code to be a common function
 * <AUTHOR>
 * @Date 2024/5/21 14:29
 * @Version 1.0
 **/
@Component
@Slf4j
public class SubmitSAS {

    @Autowired
    MinioUtil minioUtil;

    @Autowired
    FileUtil fileUtil;

    @Autowired

    Decode64Util decode64Util;


    @Autowired
    ECRFUnlockMapper ecrfUnlockMapper;

    @Autowired
    EDMCDTMSInfoMapper edmcdtmsInfoMapper;

    @Autowired
    EDCServerFile edcServerFile;

    @Autowired
    CallPython callPython;
    @Autowired
    EcrfSASTrigger sasTrigger;

    public Map<String, String> submitToSAS(Map<String, String> ENVInfo, List<Map<String, String>> uploadFilesFromEDC, Map<String, String> formInfo, List<Map<String, String>> sasOutputFilesInfo) {
        //1.call python to download UAT测试数据集(SAS)、UAT测试数据集Excel and upload the files to CDTMS API
        if ("".equals(ENVInfo.get("isLatest").toString())&&!"".equals(ENVInfo.get("data_type"))) {
            callPython.downloadEDCServerFile(ENVInfo, uploadFilesFromEDC);
        }
        String param = formInfo.get("param");
        SubmitSAS.log.info("--------------需要传的json参数是：" + param);
        com.alibaba.fastjson.JSONObject object = JSON.parseObject(param);
        //转换sas参数,从cdtms获取的表单信息中获取
        String sasParam = getSASParam(object);
        SubmitSAS.log.info("-----------------------------------------sas程序所需的参数，经过转换为:" + sasParam + "-----------------------------------------");

        //get bioknow url prefix by sysType
        String urlPrefix = "";
        urlPrefix = ENVInfo.get("requestPrefix").toString();
        //init the API return result
        Map<String, String> submitResult = new HashMap<>();
        //the specific content in the result
        String operateId = "";
        // param jsion 文件如何生成的？
        String fileName = formInfo.get("paramFileName").toString();
        String sasLogPath = "";
        String userName = "test";

        //先把除了file_id以外的数据存储到历史操作记录中
        insertBlindRecords(param, userName, formInfo.get("studyId"), formInfo.get("uuid"));

        //json文件存储sas网盘位置
        String paramsPath = SASOnlieConstant.SAS_BLIND_LOG_PATH + BlindConstant.FILE_SEPARATOR + fileName;
        //将json参数文件上传到sas服务器读取的指定位置
        // 标记文件生成是否成功
        boolean flag = true;
        if (("".equals(ENVInfo.get("isLatest").toString()) || "YES".equals(ENVInfo.get("isLatest").toString())) &&(!"UAT".equals(ENVInfo.get("ENV"))||"SAS".equals(ENVInfo.get("data_format"))) )  {
            try {
                // 保证创建一个新文件
                File file = new File(paramsPath);
                if (!file.getParentFile().exists()) {
                    // 如果父目录不存在，创建父目录
                    file.getParentFile().mkdirs();
                }
                if (file.exists()) { // 如果已存在,删除旧文件
                    file.delete();
                }
                file.createNewFile();
                // 将格式化后的字符串写入文件
                Writer write = new OutputStreamWriter(new FileOutputStream(file), "UTF-8");
                write.write(sasParam);
                write.flush();
                write.close();
                String md5 = decode64Util.getMd5(file);
                //上传调用sas的参数到minio
                minioUtil.uploadObject(file, md5, formInfo.get("studyId"));
            } catch (Exception e) {
                flag = false;
                log.info("json paramter file upload minio failed,please check and the exception time is :" + System.currentTimeMillis());
                //update the error operation record into table
                ecrfUnlockMapper.updateUnlockFailRecords(operateId, "json paramter file upload minio failed", "");
                e.printStackTrace();
            }
        }
        //2.调用sas程序，提供json文件路径
        if (flag == true) {
            String sasCodePath = formInfo.get("sasCodePath");
            Map<String, Object> sasCallResult = new HashMap<>();
            sasCallResult = sasTrigger.runCreateSAS(sasCodePath, formInfo);
            sasLogPath = sasCallResult.get("logPath").toString();
            //record this success sas call operation
            ecrfUnlockMapper.updateUnlockSuccessRecords(operateId, formInfo.get("jsonMinioPath"), paramsPath, sasCallResult.get("logPath").toString());
        }


        //3.download the output file after sas call which is from minio
        boolean isSuccess = false;
        String newRTSMOutputName = "";
        String newProtoolChartName="";
        SubmitSAS.log.info("---------------------------------------the downloaded file name is ：" + formInfo.get("outputName") + "  bucket is :" + formInfo.get("bucket") + "---------------------------------");
        //下载编码utr报告
        if(formInfo.get("bucket").toString().equals("utr") &&
                StringUtils.isEmpty(formInfo.get("outputName"))){
            for(Map<String, String> sasOutputFile: sasOutputFilesInfo){
                log.info("---------------------------下载minio上的urt文件： " + sasOutputFile.get("outputName"));
                //从minio下载utrmedra和utrwhodrug
                minioUtil.downloadGetObject("utr", sasOutputFile.get("outputName"));
            }

            for (Map<String, String> sasOutputFile : sasOutputFilesInfo) {
                log.info("---------------------------上传minio上的urt文件： " + sasOutputFile.get("outputName"));
                FileUtil.uploadSASOutputFile(ENVInfo.get("taskId").toString(), ENVInfo.get("formId").toString(), sasOutputFile.get("fid").toString(), SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + sasOutputFile.get("outputName"), ENVInfo.get("projectId").toString(), ENVInfo.get("requestPrefix").toString(), sasOutputFile.get("outputName"), ENVInfo.get("data_type").toString());
            }
        }

        if (!ObjectUtils.isEmpty(formInfo.get("outputName"))) {
            isSuccess = minioUtil.downloadGetObject(formInfo.get("bucket"), formInfo.get("outputName"));
            //随机一致性比对
            if(formInfo.get("outputName").contains(".xlsx")&&(formInfo.get("outputName").contains("随机化与供应管理数据一致性比对报告")||formInfo.get("outputName").contains("RTSM Data Reconciliation Report"))){
                //获取tag,
                Map<String,String> tagInfo= minioUtil.getObjectTags(formInfo.get("bucket"), formInfo.get("outputName"));
                String comnum= tagInfo.get("comnum");
                String date= tagInfo.get("date");
                if(formInfo.get("outputName").contains("随机化与供应管理数据一致性比对报告")){
                    //获取文件名
                    newRTSMOutputName=formInfo.get("studyId").toString()+"_随机化与供应管理数据一致性比对报告#"+comnum+"_"+date+".xlsx";
                }else{
                    newRTSMOutputName=formInfo.get("studyId").toString()+"_RTSM Data Reconciliation Report#"+comnum+"_"+date+".xlsx";
                }


            }else if(formInfo.get("outputName").contains(".xlsx")&&(formInfo.get("outputName").contains("ProTree_input"))){
                Map<String,String> tagInfo= minioUtil.getObjectTags(formInfo.get("bucket"), formInfo.get("outputName"));
                String dateStr= tagInfo.get("date");
                DateTimeFormatter inputFormatter = new DateTimeFormatterBuilder()
                        .parseCaseInsensitive()
                        .appendPattern("ddMMMyy")
                        .toFormatter(Locale.ENGLISH);
                LocalDate date = LocalDate.parse(dateStr, inputFormatter);
                String formatted = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                 newProtoolChartName=formInfo.get("studyId").toString()+"_ProTree_input"+"_"+formatted+".xlsx";
            }
        }

        if (isSuccess) {
            String fileNameClear = formInfo.get("outputName");
            if (fileNameClear.contains("/")) {
                fileNameClear = fileNameClear.split("/")[1];
            }
            int count = 0;
            for (Map<String, String> sasOutputFile : sasOutputFilesInfo) {
                // uat 上传输出文件需要重新获取formId
                String formId = CDTMSAPI.getFormIdByTaskId(ENVInfo.get("taskId").toString(), ENVInfo.get("projectId").toString());
                ENVInfo.put("formId", formId);
                String result = "";
                //PD 输出文件加日期
                if (ENVInfo.get("data_type").equals("Query_Summary_for_Protocol_Violation")) {
                    String dateString = fileUtil.getCurrentDateStr();
                    result = FileUtil.uploadSASOutputFile(ENVInfo.get("taskId").toString(), ENVInfo.get("formId").toString(), sasOutputFile.get("fid").toString(), SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + fileNameClear, ENVInfo.get("projectId").toString(), urlPrefix, formInfo.get("studyId").toString() + "_PD" + "_" + dateString + ".xlsx", ENVInfo.get("data_type").toString());
                } else if(ENVInfo.get("data_type").equals("Overall_Progress_Report")){
                    Map<String,String> tagInfo= minioUtil.getObjectTags("datamanagement", "output/"+formInfo.get("studyId").toString() + SASOnlieConstant.PROGRESS_REPORT_SUFFIX);
                    String date=tagInfo.get("date");
                    result = FileUtil.uploadSASOutputFile(ENVInfo.get("taskId").toString(), ENVInfo.get("formId").toString(), sasOutputFile.get("fid").toString(), SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + fileNameClear, ENVInfo.get("projectId").toString(), urlPrefix, formInfo.get("studyId").toString()+"_进展报告"+"_"+date+".xlsx", ENVInfo.get("data_type").toString());
                }else if(formInfo.get("outputName").contains("随机化与供应管理数据一致性比对报告")||formInfo.get("outputName").contains("RTSM Data Reconciliation Report")){
                    result = FileUtil.uploadSASOutputFile(ENVInfo.get("taskId").toString(), ENVInfo.get("formId").toString(), sasOutputFile.get("fid").toString(), SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + fileNameClear, ENVInfo.get("projectId").toString(), urlPrefix, newRTSMOutputName, ENVInfo.get("data_type").toString());
                }else if(formInfo.get("outputName").contains(".xlsx")&&(formInfo.get("outputName").contains("ProTree_input"))){
                    result = FileUtil.uploadSASOutputFile(ENVInfo.get("taskId").toString(), ENVInfo.get("formId").toString(), sasOutputFile.get("fid").toString(), SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + fileNameClear, ENVInfo.get("projectId").toString(), urlPrefix, newProtoolChartName, ENVInfo.get("data_type").toString());
                } else {
                    result = FileUtil.uploadSASOutputFile(ENVInfo.get("taskId").toString(), ENVInfo.get("formId").toString(), sasOutputFile.get("fid").toString(), SASOnlieConstant.SAS_OUTPUT_PATH + System.getProperty("file.separator") + fileNameClear, ENVInfo.get("projectId").toString(), urlPrefix, fileNameClear, ENVInfo.get("data_type").toString());
                }

                if (!result.equals("fail")) {
                    count++;
                }
            }
            //upload sas output file into cdtms via API but not minio,and use List<Map> to transfer
            if (count == sasOutputFilesInfo.size()) {
                submitResult.put("SASCallResult", "success");
                submitResult.put("sasLogPath", sasLogPath);
            } else {
                submitResult.put("SASCallResult", "fail");
                submitResult.put("sasLogPath", sasLogPath);
            }
        } else {
            //4.返回调用结果
            submitResult.put("SASCallResult", "fail");
            submitResult.put("sasLogPath", sasLogPath);
        }
        return submitResult;
    }

    @Transactional
    public String insertBlindRecords(String param, String userName, String studyId, String uuid) {
        int result = 0;
        //新增编盲记录
        //非空校验
        if (!param.isEmpty() && !userName.isEmpty() && !studyId.isEmpty()) {
            String fileName = studyId + "_" + uuid + ".json";
            result = ecrfUnlockMapper.insertUnlockRecords(uuid, param, userName, fileName);
        }
        return uuid + "_" + result;
    }

    //uat 数据格式转换
    public String getSASParam(com.alibaba.fastjson.JSONObject param) {
        //传递页面参数进行转换
        JSONObject newParam = new JSONObject();

        if (param.get("role") != null) {
            newParam.put("role", param.get("role"));
        }
        if (param.get("pro") != null) {
            newParam.put("pro", param.get("pro"));
        }
        if (param.get("crfv") != null) {
            newParam.put("crfv", param.get("crfv"));
        }
        if (param.get("crfdat") != null) {
            newParam.put("crfdat", param.get("crfdat"));
        }
        if (param.get("uatv") != null) {
            newParam.put("uatv", param.get("uatv"));
        }
        if (param.get("uatstd") != null) {
            newParam.put("uatstd", param.get("uatstd"));
        }
        if (param.get("uatend") != null) {
            newParam.put("uatend", param.get("uatend"));
        }

        if (param.get("drug_name") != null) {
            newParam.put("drug_name", param.get("drug_name"));
        }


        if (param.get("drug_allday") != null) {
            newParam.put("drug_allday", param.get("drug_allday"));
        }

        if (param.get("dose_level") != null) {
            newParam.put("dose_level", param.get("dose_level"));
        }

        if (param.get("factor") != null) {
            newParam.put("factor", param.get("factor"));
        }
        if (param.get("system") != null) {
            newParam.put("system", param.get("system"));
        }

        if (param.get("isadd") != null) {
            newParam.put("isadd", param.get("isadd"));
        }
        if (param.get("exo") != null) {
            newParam.put("exo", param.get("exo"));
        }
        if (param.get("exi") != null) {
            newParam.put("exi", param.get("exi"));
        }
        if (param.get("cohort") != null) {
            newParam.put("cohort", param.get("cohort"));
        }

        if (param.get("server") != null) {
            newParam.put("server", param.get("server"));
        }
        if (param.get("projectId") != null) {
            newParam.put("projectId", param.get("projectId"));
        }
        if (param.get("parm") != null) {
            newParam.put("parm", param.get("parm"));
        }
        if (param.get("sascode") != null) {
            newParam.put("sascode", param.get("sascode"));
        }

        if (param.get("pmname") != null) {
            newParam.put("pmname", param.get("pmname"));
        }

        if (param.get("pmmail") != null) {
            newParam.put("pmmail", param.get("pmmail"));
        }


        if (param.get("recname") != null) {
            newParam.put("recname", param.get("recname"));
        }

        if (param.get("recmail") != null) {
            newParam.put("recmail", param.get("recmail"));
        }

        if (param.get("randname") != null) {
            newParam.put("randname", param.get("randname"));
        }

        if (param.get("randmail") != null) {
            newParam.put("randmail", param.get("randmail"));
        }



        if (param.get("recqname") != null) {
            newParam.put("recqname", param.get("recqname"));
        }

        if (param.get("recqmail") != null) {
            newParam.put("recqmail", param.get("recqmail"));
        }

        if (param.get("recordid") != null) {
            newParam.put("recordid", param.get("recordid"));
        }

        if (param.get("testyn") != null) {
            newParam.put("testyn", param.get("testyn"));
        }




        return newParam.toString();
    }


}
