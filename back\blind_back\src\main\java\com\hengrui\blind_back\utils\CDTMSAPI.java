package com.hengrui.blind_back.utils;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.hengrui.blind_back.blind.constant.BlindConstant;
import com.hengrui.blind_back.blind.mapper.EDMCDTMSInfoMapper;
import com.hengrui.blind_back.blind.utils.Decode64Util;
import com.hengrui.blind_back.blind.utils.MinioUtil;
import com.hengrui.blind_back.blind.utils.MyX509TrustManager;
import com.hengrui.blind_back.blind.utils.NullHostNameVerifier;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.ecrf_unlock.utils.FileUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.TestOnly;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.KeyManagementException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * @ClassName CDTMSAPI
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/22 13:25
 * @Version 1.0
 **/
@Slf4j
@Component
public class CDTMSAPI {


    @Autowired
    Decode64Util decode64Util;

    @Autowired
    MinioUtil minioUtil;



    public static String notify(String taskId) {
        log.info("the request taskId is:" + taskId);
        String responseStr = "";
        String requestURL = SASOnlieConstant.REMOTE_SERVER_API + "remoteButtonTask/notify?taskId=" + taskId + "&projectId=" + BlindConstant.TABLE_ID;
        try {
            URL url = new URL(requestURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            log.info("Response Code: " + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                JSONObject jsonResponse = new JSONObject(response.toString());
                responseStr = response.toString();
                // Extract the study_id
                log.info("response is : " + responseStr);
                return responseStr;
            } else {
                log.info("GET request not worked");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "fail";
    }


    public String uploadSASOutputFile(String taskId, String formId, String filePath) {
        String boundary = Long.toHexString(System.currentTimeMillis()); // Random boundary for multipart
        String CRLF = "\r\n"; // Line separator required by multipart/form-data.
        File file = new File(filePath);
        String fileName = file.getName();
        String urlString = SASOnlieConstant.REMOTE_SERVER_API + "remoteButtonTask/upload"
                + "?taskId=" + taskId + "&formId=" + formId + "&fid=" + fileName + "&fn=" + fileName + "&projectId=" + BlindConstant.TABLE_ID;
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);

            try (OutputStream output = connection.getOutputStream();
                 PrintWriter writer = new PrintWriter(new OutputStreamWriter(output, "UTF-8"), true);
                 FileInputStream fileInputStream = new FileInputStream(file)) {
                // Send file data.
                writer.append("--" + boundary).append(CRLF);
                writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"" + fileName + "\"").append(CRLF);
                writer.append("Content-Type: application/octet-stream").append(CRLF); // Change accordingly
                writer.append(CRLF).flush();
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                    output.write(buffer, 0, bytesRead);
                }
                output.flush(); // Important! Output cannot be closed. Close of writer will close output as well.
                writer.append(CRLF).flush(); // CRLF is important! It indicates end of boundary.
                // End of multipart/form-data.
                writer.append("--" + boundary + "--").append(CRLF).flush();
            }

            // Request is lazily fired whenever you need to obtain information about response.
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (InputStream responseStream = connection.getInputStream();
                     BufferedReader reader = new BufferedReader(new InputStreamReader(responseStream))) {
                    String line;
                    StringBuilder response = new StringBuilder();
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    log.info("Response: " + response.toString());
                }
                //更新
                String notify = notify(taskId);
                if (!notify.equals("fail")) {
                    return "success";
                }

            } else {
                log.info("Server returned non-OK status: " + responseCode);
            }
        } catch (MalformedURLException e) {
            log.info("URL was malformed.");
        } catch (IOException e) {
            log.info("An I/O error occurred: " + e.getMessage());
        }
        return "fail";
    }


    public static String getFormIdByTaskId_back(String taskId, String projectId) {
        boolean validJson = isValidJson(projectId);
        log.info("the request taskId is:" + taskId);
        String formId = "";
        if(!validJson){
            String requestURL = SASOnlieConstant.REMOTE_SERVER_API + "remoteButtonTask/formId?taskId=" + taskId + "&projectId=" + projectId;
            try {
                URL url = new URL(requestURL);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                int responseCode = connection.getResponseCode();
                log.info("Response Code: " + responseCode);
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    String inputLine;
                    StringBuilder response = new StringBuilder();
                    while ((inputLine = in.readLine()) != null) {
                        response.append(inputLine);
                    }
                    in.close();
                    // Convert response to JSONObject for parsing
                    JSONObject jsonResponse = new JSONObject(response.toString());
                    // Extract the study_id
                    if( !ObjectUtils.isEmpty(jsonResponse.getJSONObject("data"))){
                        formId = jsonResponse.getJSONObject("data").getStr("formId");
                    }
                    log.info("formId: " + formId);
                    return formId;
                } else {
                    log.info("GET request not worked");
                    return "falil";
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return formId;
    }

    //定时调度，获取任务信息
    public static String getFormIdByTaskId(String paramA, String paramB) {
        String formId = getFormIdByTaskId_back(paramA, paramB);
        if (ObjectUtils.isEmpty(formId) || formId.isEmpty()) {
            //没有查到formId
            String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "crf_handover");
            formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
        }
        return formId;
    }


    public static String getUrlPrefix(String sysType) {
        Map<String, String> prefixMap = new HashMap<>();
        prefixMap.put("cdtms-pro", "cdtms.hengrui.com");
        prefixMap.put("cdtms-test", "cdtms-tst.hengrui.com");
        prefixMap.put("cdtms-val", "meduap-tst.hengrui.com");
        prefixMap.put("edc-pro", "clinical.hengruipharma.com:1818");
        prefixMap.put("edc-test", "cdtms-tst.hengrui.com:82");
        return prefixMap.getOrDefault(sysType, "Invalid Param"); // Return "Invalid Param" if no match found
    }

    //支持自动调度的创建表单记录，并获取相应的表单信息
    public static Map<String, String> getFormInfoByTaskId(String paramA, String paramB) {
        Map<String, String> formInfo = getFormInfoByTaskId_back(paramA, paramB);
        CDTMSAPI.log.info("formInfo: " + formInfo);
        //如果差不到表单信息，就执行自动调度创建记录
        if (ObjectUtils.isEmpty(formInfo.get("param"))) {
            CDTMSAPI.log.info("---------------------------paramJson is : " + paramB);
            com.alibaba.fastjson.JSONObject paramJson = com.alibaba.fastjson.JSONObject.parseObject(paramB);
            String tableId = paramJson.get("tableId").toString();
            String studyid = paramJson.get("studyid").toString();
            com.alibaba.fastjson.JSONObject formData = paramJson.getJSONObject("formData");
            //根据userSync接口查询表单信息
            //1.通过paramA<-->token, paramB<--->studyId   调用getformid
            String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, paramA);
            //2.根据表单接口，查询对应的项目的studyId 整数值
            String studyIdNum = CDTMSAPI.getDataListInfo(paramA, "Xsht", "obj.studyid='" + studyid + "'", "edit", "");
            String studyId = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            //3.先判断formData是否存在记录id
            formData.put("studyid", studyId);
            String id1="";
            if(!formData.containsKey("id")){
                //创建一条表单记录
                String recordId = CDTMSAPI.usersyndataSave(paramA, tableId, formId, "", "", formData.toString());
                com.alibaba.fastjson.JSONObject saveRestult = com.alibaba.fastjson.JSONObject.parseObject(recordId);
                //4.获取记录ID
                id1= saveRestult.get("id").toString();
            }else{
                id1= formData.get("id").toString();
            }

            //5.获取新建记录的表单信息
            String param = CDTMSAPI.getDataListInfo(paramA, tableId, "obj.studyid='" + studyId + "'" + "and obj.id='" + id1 + "'", "edit", "");
            //6.获取新增的表单信息
            String addInfo = com.alibaba.fastjson.JSONArray.parseArray(param).getJSONObject(0).toString();
            formInfo.put("studyId", studyid);
            formInfo.put("param", addInfo);
            formInfo.put("recordId", id1);
            formInfo.put("tableId", tableId);
        }
        return formInfo;
    }

    //getFormInfo By taskId and projectId
    public static Map<String, String> getFormInfoByTaskId_back(String taskId, String projectId) {
        log.info("the request taskId is:" + taskId + ",and projectId is :" + projectId);
        boolean validJson = isValidJson(projectId);
        Map<String, String> formInfoMap = new HashMap<>();
        String studyId = "";
        String param = "";
        String dataVersion = "";
        String tableId="";
        if(!validJson){
            String requestURL = SASOnlieConstant.REMOTE_SERVER_API + "remoteButtonTask/getInfo?taskId=" + taskId + "&projectId=" + projectId;
            try {
                URL url = new URL(requestURL);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                int responseCode = connection.getResponseCode();
                log.info("Response Code: " + responseCode);
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    String inputLine;
                    StringBuilder response = new StringBuilder();
                    while ((inputLine = in.readLine()) != null) {
                        response.append(inputLine);
                    }
                    in.close();
                    // Convert response to JSONObject for parsing
                    JSONObject jsonResponse = new JSONObject(response.toString());
                    // Extract the study_id
                    if(!ObjectUtils.isEmpty(jsonResponse.getJSONObject("data"))){
                        studyId = jsonResponse.getJSONObject("data").getJSONObject("data").getStr("studyid");
                        dataVersion = jsonResponse.getJSONObject("data").getJSONObject("data").getStr("protocol_v");
                        param = jsonResponse.getJSONObject("data").getJSONObject("data").toString();
                        tableId=jsonResponse.getJSONObject("data").get("tid").toString();
                        log.info("param to String is: " + param);
                        log.info("studyId is: " + studyId);

                    }


                } else {
                    log.info("GET request not worked");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        formInfoMap.put("studyId", studyId);
        formInfoMap.put("dataVersion", dataVersion);
        formInfoMap.put("param", param);
        formInfoMap.put("tableId", tableId);
        return formInfoMap;
    }

    public static String getDataListInfoWithPage(String token, String tableId, String filtersParam, String type, String orderColumn,int pageSize) {
        // Log the request parameters
        try {
            filtersParam = URLEncoder.encode(filtersParam, "UTF-8");
//             filtersParam = Base64.getEncoder().encodeToString(filtersParam.getBytes());
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        log.info("the request token is:" + token + ",and tableId is :" + tableId + ",and filtersParam is :" + filtersParam);
        String data = "";
        String requestURL = "";
        if (!type.isEmpty()) {
            requestURL = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/datalist?token=" + token + "&tableid=" + tableId
                    + "&type=" + type
                    + "&where=" + filtersParam;
        } else if (!orderColumn.isEmpty()) {
            requestURL = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/datalist?token=" + token + "&tableid=" + tableId
                    + "&where=" + filtersParam + "&orderby=" + orderColumn;
        }else if(pageSize>0){
            requestURL = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/datalist?token=" + token + "&tableid=" + tableId
                    + "&where=" + filtersParam + "&pagesize=" + pageSize+"&pagecount=1";
        } else {
            requestURL = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/datalist?token=" + token + "&tableid=" + tableId
                    + "&where=" + filtersParam;
        }

        try {
            URL url = new URL(requestURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            log.info("Response Code: " + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                JSONArray jsonResponse = new JSONArray(response.toString());
                // Extract the data field
                data = jsonResponse.toString();
                log.info("data: " + data);
                return data;
            } else {
                log.info("GET request not worked");
                return "fail";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }


    /**
     * This method retrieves information from the UAP API based on the provided token, tableId, and filtersParam.
     *
     * @param token        The token for authentication
     * @param tableId      The table ID for the request
     * @param filtersParam The filters to be applied to the request
     * @return The data retrieved from the API
     */
    public static String getDataListInfo(String token, String tableId, String filtersParam, String type, String orderColumn) {
        // Log the request parameters
        try {
            filtersParam = URLEncoder.encode(filtersParam, "UTF-8");
//             filtersParam = Base64.getEncoder().encodeToString(filtersParam.getBytes());
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        log.info("the request token is:" + token + ",and tableId is :" + tableId + ",and filtersParam is :" + filtersParam);
        String data = "";
        String requestURL = "";
        if (!type.isEmpty()) {
            requestURL = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/datalist?token=" + token + "&tableid=" + tableId
                    + "&type=" + type
                    + "&where=" + filtersParam;
        } else if (!orderColumn.isEmpty()) {
            requestURL = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/datalist?token=" + token + "&tableid=" + tableId
                    + "&where=" + filtersParam + "&orderby=" + orderColumn;
        } else {
            requestURL = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/datalist?token=" + token + "&tableid=" + tableId
                    + "&where=" + filtersParam;
        }

        try {
            URL url = new URL(requestURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            log.info("Response Code: " + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                JSONArray jsonResponse = new JSONArray(response.toString());
                // Extract the data field
                data = jsonResponse.toString();
                log.info("data: " + data);
                return data;
            } else {
                log.info("GET request not worked");
                return "fail";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }


    //get token by projectid and secret
    public static String getToken(String requestPrefix, String projectid, String secret, String tableId) {
        log.info("the request projectid is:" + projectid + ",and tableId is :" + tableId + ",and secret is :" + secret);
        String token = "";
        String requestURL = requestPrefix + "usersyn/gettoken?projectid=" + projectid + "&secret=" + secret
                + "&tableid=" + tableId;
        try {
            URL url = new URL(requestURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            log.info("Response Code: " + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                JSONObject jsonResponse = new JSONObject(response.toString());
                // Extract the study_id
                token = jsonResponse.get("token").toString();
                log.info("token: " + token);
                return token;
            } else {
                log.info("GET request not worked");
                return "falil";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return token;
    }


    public static void dataSave(JSONObject param) {
        String token = "";
        String taskId = param.get("taskId").toString();
        String formId=param.get("formId").toString();
        String paramData=param.get("data").toString();
        //2.call getSubjectsInfo API
        CDTMSAPI.log.info("The request taskId is: " + param.get("taskId") + " and formId is: " + formId + " and projectId : " + param.get("projectId") + "and dataId is :" + paramData);
       //判断是否是手动触发
        if(!isValidJson(param.get("projectId").toString())){
            String requestURL = SASOnlieConstant.REMOTE_SERVER_API + "remoteButtonTask/dataSave?taskId=" + param.get("taskId") + "&formId=" + param.get("formId") + "&projectId=" + param.get("projectId");
            CDTMSAPI.log.info("---------------------------------------the dataSave request URL is :" + requestURL);
            try {
                URL url = new URL(requestURL);
                HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
                connection.setDoOutput(true); // Set this before setting the request method
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
                connection.setRequestProperty("Accept", "application/json");
                connection.setRequestProperty("Connection", "keep-alive");
                connection.setRequestProperty("token", token);
                connection.setDoOutput(true);
                // Pass the parameters to the API
                String jsonStr = String.format(param.get("data").toString());
                CDTMSAPI.log.info("-----------------dataSave API  post body data is :" + jsonStr);
                // Write the request body
                try (OutputStream os = connection.getOutputStream()) {
                    byte[] input = jsonStr.getBytes(StandardCharsets.UTF_8);
                    os.write(input, 0, input.length);
                }
                int responseCode = connection.getResponseCode();
                String requestMethod = connection.getRequestMethod();
                CDTMSAPI.log.info("requestMethod is : " + requestMethod);
                CDTMSAPI.log.info("Response Code: " + responseCode);
                if (responseCode == HttpsURLConnection.HTTP_OK) {
                    BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    String inputLine;
                    StringBuilder response = new StringBuilder();
                    while ((inputLine = in.readLine()) != null) {
                        response.append(inputLine);
                    }
                    in.close();
                    // Convert response to JSONObject for parsing
                    CDTMSAPI.log.info("call CDTMSAPI.dataSave API response is :" + response.toString());
                }
            } catch (ProtocolException e) {
                throw new RuntimeException(e);
            } catch (MalformedURLException e) {
                throw new RuntimeException(e);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }else{
            JSONObject jsonObject = new JSONObject(param.get("projectId").toString());
            String tableId= jsonObject.getStr("tableId");
            CDTMSAPI.usersyndataSave(taskId,tableId,formId,"","",paramData);
        }



    }

    public static String usersyndataSave(String token, String tableid, String formid, String username, String format, String param) {
        StringBuilder response = new StringBuilder();
        String requestURL = "";
        if (format.isEmpty() || username.isEmpty()) {
            requestURL = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/datasave?token=" + token + "&tableid=" + tableid + "&formid=" + formid;
        } else {
            requestURL = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/datasave?token=" + token + "&tableid=" + tableid + "&formid=" + formid + "&username=" + username + "&format=" + format;
        }

        CDTMSAPI.log.info("---------------------------------------the dataSave request URL is :" + requestURL);
        try {
            URL url = new URL(requestURL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setDoOutput(true); // Set this before setting the request method
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Connection", "keep-alive");
            connection.setDoOutput(true);
            // Pass the parameters to the API
            String jsonStr = param;
            CDTMSAPI.log.info("-----------------usersyn dataSave API  post body data is :" + jsonStr);
            // Write the request body
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonStr.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            int responseCode = connection.getResponseCode();
            String requestMethod = connection.getRequestMethod();
            CDTMSAPI.log.info("requestMethod is : " + requestMethod);
            CDTMSAPI.log.info("Response Code: " + responseCode);
            if (responseCode == HttpsURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                CDTMSAPI.log.info("call usersyn dataSave API response is :" + response.toString());

            }
        } catch (ProtocolException e) {
            throw new RuntimeException(e);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return response.toString();
    }


    //get info from cdtms
    public static String getInfo(String requestPrefix, String taskId, String projectId, String type) {
        log.info("the request projectid is:" + projectId + ",and taskId is :" + taskId + ",and type is :" + type);
        String data = "";
        String requestURL = requestPrefix + "remoteButtonTask/getInfo?taskId=" + taskId + "&projectId=" + projectId
                + "&type=" + type;
        try {
            URL url = new URL(requestURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            log.info("Response Code: " + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                JSONObject jsonResponse = new JSONObject(response.toString());
                // Extract the study_id
                data = jsonResponse.get("data").toString();
                log.info("data: " + data);
                return data;
            } else {
                log.info("GET request not worked");
                return "falil";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    /**
     * zip包解压缩到当前目录下
     * @param zipFilePath
     * @throws IOException
     */
    private static void extractZipFile(String zipFilePath) throws IOException {
        Path destinationDir = Paths.get(zipFilePath).getParent();
        if (destinationDir == null) {
            destinationDir = Paths.get(".");
        }

        log.info("解压缩 zip 文件到: " + destinationDir);

        try (ZipInputStream zipIn = new ZipInputStream(new FileInputStream(zipFilePath))) {
            ZipEntry entry = zipIn.getNextEntry();

            while (entry != null) {
                Path filePath = destinationDir.resolve(entry.getName());

                // Create directories if needed
                if (entry.isDirectory()) {
                    Files.createDirectories(filePath);
                } else {
                    // Create parent directories if needed
                    Files.createDirectories(filePath.getParent());

                    // Extract file
                    try (BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(filePath.toFile()))) {
                        byte[] buffer = new byte[4096];
                        int read;
                        while ((read = zipIn.read(buffer)) != -1) {
                            bos.write(buffer, 0, read);
                        }
                    }
                    log.info("解压缩: " + entry.getName());
                }

                zipIn.closeEntry();
                entry = zipIn.getNextEntry();
            }
        }

        log.info("Zip 完成解压缩!");
    }


    @SneakyThrows
    public static void downloadDataByUserSync(String tableId, String token, String ufn, String filePath) throws IOException {
        // Call the download interface
        String fileUrl = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/download?tableid=" + tableId + "&token=" + URLEncoder.encode(token, "UTF-8") + "&ufn=" + URLEncoder.encode(ufn, "UTF-8");
        log.info("----------------文件下载的url是："+fileUrl+"----------------------------------------------");
        // Set up to access the https request through ip address
        HttpsURLConnection.setDefaultHostnameVerifier(new NullHostNameVerifier());
        TrustManager[] tm = {new MyX509TrustManager()};
        SSLContext sslContext = SSLContext.getInstance("TLS");
        try {
            sslContext.init(null, tm, new java.security.SecureRandom());
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        }
        // Get the SSLSocketFactory object from the above SSLContext object
        SSLSocketFactory ssf = sslContext.getSocketFactory();
        String urlStr = fileUrl;
        URL url = null;
        try {
            url = new URL(urlStr);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
        HttpsURLConnection con = null;
        try {
            con = (HttpsURLConnection) url.openConnection();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        con.setSSLSocketFactory(ssf);
        try {
            con.setRequestMethod("GET"); // Set to submit data using POST method
        } catch (ProtocolException e) {
            throw new RuntimeException(e);
        }
        con.setDoInput(true); // Open input stream to get data from the server
        con.setDoOutput(true);// Open output stream to send data to the server
        // Set the sending parameters
        PrintWriter out = null;
        try {
            out = new PrintWriter(new OutputStreamWriter(con.getOutputStream(), "UTF-8"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        out.flush();
        out.close();
        try {
            InputStream in = con.getInputStream();
            Files.copy(in, Paths.get(filePath), StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }


    public static void updateWorkFlowStatus(String taskId, String projectId, String status, String statusValue) {
        CDTMSAPI.log.info("--------------------------------------------------调用了状态更新接口 !!!" );
        Map<String, String> formInfoByTaskId = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        CDTMSAPI.log.info("--------------------------------------------------get formInfoByTaskId is :" + formInfoByTaskId);
        String data = formInfoByTaskId.get("param");
        String dataId = "";
        if (!data.isEmpty()) {
            JSONObject formInfoData = new JSONObject(data.toString());
            dataId = formInfoData.get("id").toString();
        }
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        JSONObject param = new JSONObject();
        param.put("taskId", taskId);
        param.put("formId", formId);
        param.put("projectId", projectId);
        JSONObject temp = new JSONObject();
        temp.put(status, statusValue);
        temp.put("id", dataId);
        param.put("data", temp);
        CDTMSAPI.dataSave(param);
    }

    public static String queryFileDownload(String input, String token, String suffix,String tableId) {
        log.info("-------------------下载的文件名是:"+input+"后缀是:"+suffix+"表名是:"+tableId+"-------------------");
        String regex = "\\*([A-Z0-9]+\\." + suffix + ")\\|";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        String[] split = input.split("\\*");
        String filePath = SASOnlieConstant.ECRF_LIST_FOLDER +System.getProperty("file.separator") + split[0];
        log.info("--------------------------------------------------------返回的下载的文件路径是："+filePath+"---------------------------------------------------------");
        String ufn = "";
        if (matcher.find()) {
            ufn = matcher.group(1);
            CDTMSAPI.log.info(ufn);
        } else {
            CDTMSAPI.log.info("No match found");
        }
        CDTMSAPI.log.info("-------------------------------------------------------found the file name is {}---------------------------", ufn);
        if (!ufn.isEmpty()) {
            try {
                CDTMSAPI.downloadDataByUserSync(tableId, token, ufn, filePath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return filePath;
    }

    //随机生成表单formId
    public static String getFormIdByToken(String requestPrefix, String token) {
        String formid = "";
        String requestURL = requestPrefix + "usersyn/getformid?token=" + token;
        log.info("-----------------------requestURL is : " + requestURL);
        try {
            URL url = new URL(requestURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            log.info("Response Code: " + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                JSONObject jsonResponse = new JSONObject(response.toString());
                // Extract the study_id
                formid = jsonResponse.get("formid").toString();
                log.info("formid: " + formid);
                return formid;
            } else {
                log.info("GET request not worked");
                return "falil";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return formid;
    }

    public static boolean isValidJson(String jsonString) {
        try {
            new JSONObject(jsonString);
            return true;
        } catch (Exception e) {
            return false;
        }
    }




    //定期审核的接口调用
    public static String callScheduleReview(String requestPrefix, String uuid,String param) {
        String requestURL = requestPrefix + "cdtms/regular_review?taskId=" + uuid + "&type=" + URLEncoder.encode(param);
        log.info("-----------------------requestURL is : " + requestURL);
        String command= "curl -X GET -H \"Content-Type: application/json\" " + requestURL;
        String result = CallPython.executeLatestFill(command);
        CDTMSAPI.log.info("-------------定期审核接口的getInputStream："+result);

        return result;
    }


    public static String callDataCleanPyAPI(String requestPrefix, String taskId,String studyid,String file_type,String start_date,String end_date) {
        String requestURL = (requestPrefix + "cdtms/regular_review?taskId=" + taskId +"&studyid="+studyid+ "&file_type=" + file_type+ "&start_date=" + start_date+ "&end_date=" + end_date).toString();
        log.info("-----------------------requestURL is : " + requestURL);
        String command= "curl -X GET -H \"Content-Type: application/json\" \"" + requestURL + "\"";
        String result = CallPython.executeLatestFill(command);
        CDTMSAPI.log.info("-------------定期审核接口的getInputStream："+result);

        return result;
    }


    public static Map<String,String> getRTSMAccountEmail(String studyId){
        Map<String,String> accountInfo=new HashMap<>();
        String craEmail="";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "crf_handover");
        String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");

        if(!StringUtils.isEmpty(studyIdNum)&&com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size()>0){
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "cra", "obj.studyid='" + studyInt+"'"+"and obj.limitnum=6", "edit", "");
            com.alibaba.fastjson.JSONArray array=JSON.parseArray(result);
            if(array.size()>0){
                craEmail = JSON.parseObject(JSON.parseArray(result).get(0).toString()).get("cra_email").toString();
                String userName= JSON.parseObject(JSON.parseArray(result).get(0).toString()).get("cra_name").toString();
                accountInfo.put("email",craEmail);
                accountInfo.put("name",userName);
            }

        }
        return accountInfo;
    }


    //获取随机化专员邮箱和姓名
    public static Map<String,String> getRTSMRandInfo(String studyId,String randSpecialistValue){
        Map<String,String> rtsmInfo=new HashMap<>();
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "crf_handover");
        String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
        //获取数据中心人员-rtsm
        String RTSMIdresult = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'" + "and obj.limitnum='" + randSpecialistValue + "' and  obj.active='1'  ", "edit", "");

        //获取DM姓名、邮箱
        List<String> rtsmUserIds=new ArrayList<>();
        List<String> rtsmEmails=new ArrayList<>();
        List<String> rtsmNames=new ArrayList<>();
        com.alibaba.fastjson.JSONArray dms=JSON.parseArray(RTSMIdresult);
        for(int i=0;i< dms.size();i++){
            rtsmUserIds.add(JSON.parseObject(JSON.parseArray(RTSMIdresult).get(i).toString()).get("member").toString());
        }

        for(String userid:rtsmUserIds){
            String DMResult= CDTMSAPI.getDataListInfo(token, "ryjbzl", "obj.id='" + userid + "'", "edit", "");

            rtsmNames.add(JSON.parseObject(JSON.parseArray(DMResult).get(0).toString()).get("xm").toString());
            rtsmEmails.add(JSON.parseObject(JSON.parseArray(DMResult).get(0).toString()).get("email").toString());
        }
        String[] rtsmEmailsStr = rtsmEmails.toArray(new String[0]);
        String rtsmEmail=String.join(", ", rtsmEmailsStr);
        String[] rtsmNamesStr = rtsmNames.toArray(new String[0]);
        String rtsmName=String.join(", ", rtsmNamesStr);
        log.info(rtsmEmail);
        log.info(rtsmName);
        rtsmInfo.put("email",rtsmEmail);
        rtsmInfo.put("name",rtsmName);
        return rtsmInfo;
    }


    public static Map<String,String> getRTSMAccountManagerEmail(String studyId){
        Map<String,String> accountInfo=new HashMap<>();
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "crf_handover");
        String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if(com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size()>0){
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "cra", "obj.studyid='" + studyInt+"'"+"and obj.limitnum=14", "edit", "");
            com.alibaba.fastjson.JSONArray array=JSON.parseArray(result);
            if(array.size()>0){
                String craEmail = JSON.parseObject(JSON.parseArray(result).get(0).toString()).get("cra_email").toString();
                String userName= JSON.parseObject(JSON.parseArray(result).get(0).toString()).get("cra_name").toString();
                if(!craEmail.isEmpty()||!userName.isEmpty()){
                    accountInfo.put("email",craEmail);
                    accountInfo.put("name",userName);
                }else{
                    accountInfo.put("email","");
                    accountInfo.put("name","");
                }

            }

        }

        return accountInfo;

    }







    public static Map<String, String> getRTSMRandInfo(String studyId, String randSpecialistValue, String userName) {
        Map<String, String> rtsmInfo =new HashMap<>();
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "crf_handover");
        String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if(!StringUtils.isEmpty(studyIdNum)&&com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size()>0){
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();

            // 获取数据中心人员-rtsm
            String RTSMIdresult = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'" + "and obj.limitnum='" + randSpecialistValue + "' and  obj.active='1'  ", "edit", "");

            // 获取DM姓名、邮箱
            List<String> rtsmUserIds = new ArrayList<>();

            com.alibaba.fastjson.JSONArray dms = JSON.parseArray(RTSMIdresult);
            for (int i = 0; i < dms.size(); i++) {
                rtsmUserIds.add(JSON.parseObject(JSON.parseArray(RTSMIdresult).get(i).toString()).get("member").toString());
            }

            // 遍历用户ID，获取对应的姓名和邮箱
            for (String userid : rtsmUserIds) {
                String DMResult = CDTMSAPI.getDataListInfo(token, "ryjbzl", "obj.id='" + userid + "'", "edit", "");
                com.alibaba.fastjson.JSONArray userInfoArray = JSON.parseArray(DMResult);

                // 判断是否包含指定的userName
                for (int i = 0; i < userInfoArray.size(); i++) {
                    String currentUserName = JSON.parseObject(userInfoArray.get(i).toString()).get("xm").toString();
                    if (currentUserName.equals(userName)) {
                        // 如果找到匹配的userName，直接返回对应的name和email
                        String email = JSON.parseObject(userInfoArray.get(i).toString()).get("email").toString();
                        rtsmInfo.put("email", email);
                        rtsmInfo.put("name", currentUserName);
                        return rtsmInfo;
                    }
                }

            }

            return rtsmInfo;
        }else{
            return rtsmInfo;
        }

    }




    public static Map<String,String> getStudyInfo(String studyId){
        Map<String,String> accountInfo=new HashMap<>();
        String version="";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "crf_handover");
        String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if(com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size()>0){
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "xshtbbxx", "obj.studyid='" + studyInt+"'", "edit", "obj.revised_date desc");
            String titleInfo = CDTMSAPI.getDataListInfo(token, "xsht", "obj.studyid='" + studyId + "'", "edit", "");
            CDTMSAPI.log.info("-------------xshtbbxx的查询结果结果：",result);
            version = JSON.parseObject(JSON.parseArray(result).get(0).toString()).get("xsht_version").toString();
            String goal= JSON.parseObject(JSON.parseArray(titleInfo).get(0).toString()).get("project_title").toString();
            String revised_date= JSON.parseObject(JSON.parseArray(result).get(0).toString()).get("revised_date").toString();
            String bbh= JSON.parseObject(JSON.parseArray(result).get(0).toString()).get("studyid").toString();

            accountInfo.put("caseVersionNum",version);
            if(StringUtils.isEmpty(goal)){
                accountInfo.put("title","");
            }else{
                accountInfo.put("title",goal);
            }

            accountInfo.put("caseNum",bbh);
            accountInfo.put("caseVersionDate",revised_date);
        }

        return accountInfo;

    }


    public static String getStudyLanguage(String studyId){
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "xsht", "obj.studyid='" + studyId + "') ", "edit", URLEncoder.encode("createtime desc"));
        com.alibaba.fastjson.JSONArray jsonArray = com.alibaba.fastjson.JSONArray.parseArray(dataListInfo);
        String tmp = jsonArray.get(0).toString();
        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(tmp);
        String original_language = jsonObject.get("used_language").toString();
        String used_language="";
        if(original_language.equals("zh_CN")){
            used_language="CH";
        }else if(original_language.equals("en_US")){
            used_language="EN";
        }
        return  used_language;
    }


    public static String getUfn(String filePath,String taskId,String projectId){
        String ufn="";
        if(!filePath.isEmpty()){
            File file = new File(filePath);
            if(file.exists()){
                //回传文件
                String latestFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
                ufn = FileUtil.uploadFileByRemoteAPI(taskId,latestFormId,file.getName(),filePath,SASOnlieConstant.REMOTE_SERVER_PROJECTID,SASOnlieConstant.REMOTE_SERVER_API_PREFIX,file.getName(),"csv");

            }
        }
        return ufn;
    }




    public static String getLoginUserRole(String studyId,String taskId){
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "crf_handover");
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        String  role= "";
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'"+"and obj.limitnum='TDM' and  obj.active='1' ", "", "");
            String resultA = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'"+"and obj.limitnum='DM' and  obj.active='1' ", "", "");
            String results = CDTMSAPI.getInfo(SASOnlieConstant.REMOTE_SERVER_API, taskId, SASOnlieConstant.REMOTE_SERVER_PROJECTID, "edit");
            com.alibaba.fastjson.JSONObject loginInfo = JSON.parseObject(results).getJSONObject("user");
            String userName = loginInfo.get("username").toString();
            com.alibaba.fastjson.JSONArray objects = com.alibaba.fastjson.JSONArray.parseArray(result);
            for(int i=0;i<objects.size();i++){
                if (objects.getJSONObject(i).get("member").toString().equals(userName)){
                    role="TDM";
                    break;
                }
            }

            com.alibaba.fastjson.JSONArray objectsA = com.alibaba.fastjson.JSONArray.parseArray(resultA);
            for(int i=0;i<objectsA.size();i++){
                if (objectsA.getJSONObject(i).get("member").toString().equals(userName)){
                    role="DM";
                    break;
                }
            }


        }
        return role;
    }




    public  void uploadDBS(String studyId){
        String DBSpecificationPath= SASOnlieConstant.SAS_DATA_LOCAL_FOLDER+studyId+"_als.xlsx";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "ecrf_build", "obj.studyid=(select obj2.id from Xsht as obj2 where obj2.studyid='" + studyId + "') and obj.version_zt=2 and obj.note_ecrf is not null", "edit", URLEncoder.encode("createtime desc"));
        com.alibaba.fastjson.JSONArray jsonArray = com.alibaba.fastjson.JSONArray.parseArray(dataListInfo);
        if(jsonArray.size()>0){
            String tmp = jsonArray.get(0).toString();
            String input = JSON.parseObject(tmp).get("note_ecrf").toString();
            String regex = "\\*([A-Z0-9]+\\.xlsx)\\|";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(input);
            String ufn = "";
            if (matcher.find()) {
                ufn = matcher.group(1);
                log.info(ufn);
            } else {
                log.info("No match found");
            }
            log.info("-------------------------------------------------------found the file name is {}---------------------------", ufn);
            if (!ufn.isEmpty()) {
                try {
                    CDTMSAPI.downloadDataByUserSync("ecrf_build", token, ufn, DBSpecificationPath);
                    File file=new File(DBSpecificationPath);
                    String md5 = decode64Util.getMd5(file);
                    //上传到minio doc 目录下
                    minioUtil.uploadObjectWithTag(file,md5,studyId,"uat","doc","");
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }



    public static String  getCraNameAndEmail(String studyId,String roleValue,String tableId){
        String accountInfo="";
        String userName="";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, tableId);
        String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if(com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size()>0){
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "cra", "obj.studyid='" + studyInt+"'"+"and obj.syzt=1 and obj.limitnum="+roleValue, "edit", "");
            com.alibaba.fastjson.JSONArray objects = JSON.parseArray(result);
            if(objects.size()>0){
                userName= JSON.parseObject(objects.get(0).toString()).get("cra_name").toString();
            }

            accountInfo=userName;
        }
        return accountInfo;
    }

    public static  String getTDMName(String studyId) {
        String TDMName="";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "crf_handover");
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            //String result = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'", "edit", "obj.statr_date desc");
            String result = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'" + "and obj.limitnum='TDM' and  obj.active='1' ", "", "");
            com.alibaba.fastjson.JSONArray objects = com.alibaba.fastjson.JSONArray.parseArray(result);
            if (objects.size()>0){
                TDMName= JSON.parseObject(objects.get(0).toString()).get("member").toString();
            }
        }
        return TDMName;
    }

    public static  String getMDMName(String studyId) {
        String TDMName="";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "crf_handover");
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            //String result = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'", "edit", "obj.statr_date desc");
            String result = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'" + "and obj.limitnum='MDM' and  obj.active='1' ", "", "");
            com.alibaba.fastjson.JSONArray objects = com.alibaba.fastjson.JSONArray.parseArray(result);
            if (objects.size()>0){
                TDMName= JSON.parseObject(objects.get(0).toString()).get("member").toString();
            }
        }
        return TDMName;
    }


    public static  String getDMName(String studyId) {
        String TDMName="";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "crf_handover");
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            //String result = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'", "edit", "obj.statr_date desc");
            String result = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'" + "and obj.limitnum='DM' and  obj.active='1' ", "", "");
            com.alibaba.fastjson.JSONArray objects = com.alibaba.fastjson.JSONArray.parseArray(result);
            if (objects.size()>0){
                TDMName= JSON.parseObject(objects.get(0).toString()).get("member").toString();
            }
        }
        return TDMName;
    }


    public  void uploadSAS(String studyId){
        String DBSpecificationPath= SASOnlieConstant.SAS_DATA_LOCAL_FOLDER+studyId+"_sas.zip";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "manual_rev_prog", "obj.studyid=(select obj2.id from Xsht as obj2 where obj2.studyid='" + studyId + "')  and obj.edc_dataset is not null", "edit", URLEncoder.encode("createtime desc"));
        com.alibaba.fastjson.JSONArray jsonArray = com.alibaba.fastjson.JSONArray.parseArray(dataListInfo);

        if(jsonArray.size()>0){
            String tmp = jsonArray.get(0).toString();
            String input = JSON.parseObject(tmp).get("edc_dataset").toString();
            String regex = "\\*([A-Z0-9]+\\.zip)\\|";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(input);
            String ufn = "";
            if (matcher.find()) {
                ufn = matcher.group(1);
                log.info(ufn);
            } else {
                log.info("No match found");
            }
            log.info("-------------------------------------------------------found the file name is {}---------------------------", ufn);
            if (!ufn.isEmpty()) {
                try {
                    CDTMSAPI.downloadDataByUserSync("manual_rev_prog", token, ufn, DBSpecificationPath);
                    File file=new File(DBSpecificationPath);
                    String md5 = decode64Util.getMd5(file);
                    //上传到minio doc 目录下
                    minioUtil.uploadObjectWithTag(file,md5,studyId,"uat","raw","");
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }


    public static JSONObject setRTSMCOMPParam(JSONObject param,String studyId){
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "crf_handover");
        String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
        String pmResult = CDTMSAPI.getDataListInfo(token, "cra", "obj.studyid='" + studyInt + "'" + "and obj.limitnum=3 and obj.blinding=1", "edit", "");
        //获取项目经理姓名、邮箱
        com.alibaba.fastjson.JSONArray pms=JSON.parseArray(pmResult);
        List<String> pmEmails=new ArrayList<>();
        List<String> pmNames=new ArrayList<>();
        for(int i=0;i< pms.size();i++){
            pmEmails.add(JSON.parseObject(JSON.parseArray(pmResult).get(i).toString()).get("cra_email").toString());
            pmNames.add(JSON.parseObject(JSON.parseArray(pmResult).get(i).toString()).get("cra_name").toString());
        }

        String[] pmEmailsStr = pmEmails.toArray(new String[0]);
        String pmEmail=String.join(", ", pmEmailsStr);
        String[] pmNamesStr =  pmNames.toArray(new String[0]);
        String pmName=String.join(", ", pmNamesStr);


        log.info(pmEmail);
        log.info(pmName);


        param.put("pmname",pmName);
        param.put("pmmail",pmEmail);


        //获取数据中心人员-DM
        String DMIdresult = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'" + "and obj.limitnum='DM' and  obj.active='1' and obj.blinding='1' ", "edit", "");
        //获取DM姓名、邮箱
        List<String> dmUserIds=new ArrayList<>();
        List<String> dmEmails=new ArrayList<>();
        List<String> dmNames=new ArrayList<>();
        com.alibaba.fastjson.JSONArray dms=JSON.parseArray(DMIdresult);
        for(int i=0;i< dms.size();i++){
            dmUserIds.add(JSON.parseObject(JSON.parseArray(DMIdresult).get(i).toString()).get("member").toString());
        }

        for(String userid:dmUserIds){
            String DMResult= CDTMSAPI.getDataListInfo(token, "ryjbzl", "obj.id='" + userid + "'", "edit", "");

            dmNames.add(JSON.parseObject(JSON.parseArray(DMResult).get(0).toString()).get("xm").toString());
            dmEmails.add(JSON.parseObject(JSON.parseArray(DMResult).get(0).toString()).get("email").toString());
        }
        String[] dmEmailsStr = dmEmails.toArray(new String[0]);
        String dmEmail=String.join(", ", dmEmailsStr);
        String[] dmNamesStr = dmNames.toArray(new String[0]);
        String dmName=String.join(", ", dmNamesStr);
        log.info(dmEmail);
        log.info(dmName);

        param.put("recname",dmName);
        param.put("recmail",dmEmail);

        //获取数据中心人员-RandSpecialist
        String RSIdresult = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'" + "and obj.limitnum='RandSpecialist' and  obj.active='1' and obj.blinding='1' ", "edit", "");
        //获取RandSpecialist姓名、邮箱
        List<String> rsUserIds=new ArrayList<>();
        List<String> rsEmails=new ArrayList<>();
        List<String> rsNames=new ArrayList<>();
        com.alibaba.fastjson.JSONArray rss=JSON.parseArray(RSIdresult);
        for(int i=0;i< rss.size();i++){
            rsUserIds.add(JSON.parseObject(JSON.parseArray(RSIdresult).get(i).toString()).get("member").toString());
        }

        for(String userid:rsUserIds){
            String RSResult= CDTMSAPI.getDataListInfo(token, "ryjbzl", "obj.id='" + userid + "'", "edit", "");
            rsNames.add(JSON.parseObject(JSON.parseArray(RSResult).get(0).toString()).get("xm").toString());
            rsEmails.add(JSON.parseObject(JSON.parseArray(RSResult).get(0).toString()).get("email").toString());
        }
        String[] rsEmailsStr = rsEmails.toArray(new String[0]);
        String rsEmail=String.join(", ", rsEmailsStr);
        String[] rsNamesStr = rsNames.toArray(new String[0]);
        String rsName=String.join(", ", rsNamesStr);
        log.info(rsEmail);
        log.info(rsName);

        param.put("randname",rsName);
        param.put("randmail",rsEmail);

        //获取数据中心人员-TDM
        String TDMIdresult = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'" + "and obj.limitnum='TDM' and  obj.active='1' and obj.blinding='1' ", "edit", "");
        //获取RandSpecialist姓名、邮箱
        List<String> tdmUserIds=new ArrayList<>();
        List<String> tdmEmails=new ArrayList<>();
        List<String> tdmNames=new ArrayList<>();
        com.alibaba.fastjson.JSONArray tdms=JSON.parseArray(TDMIdresult);
        for(int i=0;i< tdms.size();i++){
            tdmUserIds.add(JSON.parseObject(JSON.parseArray(TDMIdresult).get(i).toString()).get("member").toString());
        }

        for(String userid:tdmUserIds){
            String TDMResult= CDTMSAPI.getDataListInfo(token, "ryjbzl", "obj.id='" + userid + "'", "edit", "");
            tdmNames.add(JSON.parseObject(JSON.parseArray(TDMResult).get(0).toString()).get("xm").toString());
            tdmEmails.add(JSON.parseObject(JSON.parseArray(TDMResult).get(0).toString()).get("email").toString());
        }
        String[] tdmEmailsStr = tdmEmails.toArray(new String[0]);
        String tdmEmail=String.join(", ", tdmEmailsStr);
        String[] tdmNamesStr = tdmNames.toArray(new String[0]);
        String tdmName=String.join(", ", tdmNamesStr);
        log.info(tdmEmail);
        log.info(tdmName);

        param.put("recqname",tdmName);
        param.put("recqmail",tdmEmail);

        return param;
    }


}
