package com.hengrui.blind_back;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
@MapperScan(basePackages = "com.hengrui.blind_back.blind.mapper,com.hengrui.blind_back.dmplatform.mapper,com.hengrui.blind_back.ecrf_unlock.mapper,com.hengrui.blind_back.parse_excel_toDB.mapper,com.hengrui.blind_back.onlyOffice.mapper,com.hengrui.blind_back.jobconfig.mapper,com.hengrui.blind_back.rtsm.mapper,com.hengrui.blind_back.smo_data.mapper")
@Slf4j
public class BlindBackApplication {

    public static void main(String[] args) {
        SpringApplication.run(BlindBackApplication.class, args);
    }


}
