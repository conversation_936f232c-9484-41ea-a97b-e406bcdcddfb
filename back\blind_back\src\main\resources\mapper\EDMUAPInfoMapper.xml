<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengrui.blind_back.blind.mapper.EDMUAPInfoMapper">
    <select id="getCSVFilePath" resultType="string">
        SELECT
            SUBSTRING_INDEX( SUBSTRING_INDEX(tbl_outboard_data_manager.COL_OUTBOARD_DATA_DOC, '*', -1),'|',1)as filePath
        FROM
            tbl_outboard_data_manager
                LEFT JOIN tbl_xsht ON tbl_outboard_data_manager.COL_STUDYID = tbl_xsht.id
        WHERE COL_PROJECT_NAME=#{projectName} and COL_OUTBOARD_DATA_DOC  like  concat('%',#{fileName},'%')
    </select>
</mapper>
