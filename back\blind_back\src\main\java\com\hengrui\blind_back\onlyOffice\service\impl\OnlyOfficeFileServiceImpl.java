package com.hengrui.blind_back.onlyOffice.service.impl;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.hengrui.blind_back.blind.utils.MyX509TrustManager;
import com.hengrui.blind_back.blind.utils.NullHostNameVerifier;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import com.hengrui.blind_back.onlyOffice.mapper.OnlyOfficeFileMapper;
import com.hengrui.blind_back.onlyOffice.service.OnlyOfficeFileService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.Scanner;

/**
 * @ClassName OnlyOfficeFileServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/8/27 14:02
 * @Version 1.0
 **/
@Service
@Slf4j
public class OnlyOfficeFileServiceImpl implements OnlyOfficeFileService {

    @Autowired
    OnlyOfficeFileMapper onlyOfficeFileMapper;


    @Override
    public void saveOnlyOfficeFile(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // Get the full request URL
        StringBuffer requestURL = request.getRequestURL();
        String queryString = request.getQueryString();
        if (queryString != null) {
            requestURL.append("?").append(queryString);
        }
        String fullUrl = requestURL.toString();

        // Log the full URL
        OnlyOfficeFileServiceImpl.log.info("Full Request URL: " + fullUrl);

        // Extract the studyId parameter
        String studyId = request.getParameter("studyId");
        String userName = request.getParameter("userName");
        String batchNum = request.getParameter("batchNum");
        String fileName= request.getParameter("fileName");


        if (studyId != null && !studyId.isEmpty()) {
            OnlyOfficeFileServiceImpl.log.info("Extracted studyId: " + studyId);
            // You can now use the studyId as needed in your logic
        } else {
            OnlyOfficeFileServiceImpl.log.info("studyId parameter not found in the request");
        }

        PrintWriter writer = response.getWriter();
        Scanner scanner = new Scanner(request.getInputStream()).useDelimiter("\\A");
        String body = scanner.hasNext() ? scanner.next() : "";
        JsonParser parser = new JsonParser();
        JsonElement parse = parser.parse(body);
        JsonObject object = parse.getAsJsonObject();
        OnlyOfficeFileServiceImpl.log.info("-----------------------------------------------onlyOffice保存时调用的接口为:" + object.toString());
        String key = ULIDGenerator.generateULID();
        int status = object.get("status").getAsInt();
        Integer SignStatus= onlyOfficeFileMapper.getApplyFileSignStatusByName(studyId, fileName);
        //手动保存才更新文件
        if ((status == 6|| status == 2)&& 4!=SignStatus)  {
            OnlyOfficeFileServiceImpl.log.info("-----------------------------------------------onlyOffice保存时调用的接口查询到的签字状态:" + SignStatus);
            JsonObject userInfo = object.get("history").getAsJsonObject();
            String userId="";
            if (!userInfo.isEmpty()) {
                userId = userInfo.get("changes").getAsJsonArray().get(0).getAsJsonObject().get("user").getAsJsonObject().get("id").toString();
            }

            String backUpFilePath = "";
            //保存本次的文件信息和文件路径,
                backUpFilePath= "/home/<USER>/8087/onlyOfficeFile/record/" + fileName;
                onlyOfficeFileMapper.insertApplyEditRwecord(key, userId, userName, backUpFilePath, key, studyId,batchNum);

            String downloadUrl = object.get("url").getAsString();
            OnlyOfficeFileServiceImpl.log.info("-----------------------------------------------onlyOffice保存时的文件下载原始链接:" + downloadUrl);
            // Set up SSL context
            SSLContext sslContext = null;
            try {
                sslContext = SSLContext.getInstance("TLS");
            } catch (NoSuchAlgorithmException e) {
                throw new RuntimeException(e);
            }
            TrustManager[] tm = {new MyX509TrustManager()};
            try {
                sslContext.init(null, tm, new java.security.SecureRandom());
            } catch (KeyManagementException e) {
                throw new RuntimeException(e);
            }
            SSLSocketFactory ssf = sslContext.getSocketFactory();

            // Open connection
            URL url = new URL(downloadUrl);
            HttpsURLConnection con = (HttpsURLConnection) url.openConnection();
            con.setSSLSocketFactory(ssf);
            con.setRequestMethod("GET");

            // Set hostname verifier if necessary
            con.setHostnameVerifier(new NullHostNameVerifier());

            // Define the local path where the file will be saved
            String localFilePath = "/home/<USER>/8087/onlyOfficeFile/" + studyId + "_" + key + ".xlsx";

            // Read the input stream and write to both the specified local file path and backup path
            try (InputStream in = new BufferedInputStream(con.getInputStream());
                 FileOutputStream fos1 = new FileOutputStream(localFilePath);
                 FileOutputStream fos2 = new FileOutputStream(backUpFilePath);
                 BufferedOutputStream bout1 = new BufferedOutputStream(fos1, 8192);
                 BufferedOutputStream bout2 = new BufferedOutputStream(fos2, 8192)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    bout1.write(buffer, 0, bytesRead);
                    bout2.write(buffer, 0, bytesRead);
                }

                // Ensure all data is written
                bout1.flush();
                bout2.flush();
            } catch (IOException e) {
                // Handle exception (log it, throw it, etc.)
                e.printStackTrace();
            } finally {
                // Disconnect the connection
                con.disconnect();
            }


        }
        writer.write("{\"error\":0}");
    }


    @Override
    public void saveAuditApprovalFile(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // Get the full request URL
        StringBuffer requestURL = request.getRequestURL();
        String queryString = request.getQueryString();
        if (queryString != null) {
            requestURL.append("?").append(queryString);
        }
        String fullUrl = requestURL.toString();

        // Log the full URL
        OnlyOfficeFileServiceImpl.log.info("Full Request URL: " + fullUrl);

        // Extract the studyId parameter
        String studyId = request.getParameter("studyId");
        String userName = request.getParameter("userName");
        String batchNum = request.getParameter("batchNum");
        String fileName= request.getParameter("fileName");
        if (studyId != null && !studyId.isEmpty()) {
            OnlyOfficeFileServiceImpl.log.info("Extracted studyId: " + studyId);
            // You can now use the studyId as needed in your logic
        } else {
            OnlyOfficeFileServiceImpl.log.info("studyId parameter not found in the request");
        }

        PrintWriter writer = response.getWriter();
        Scanner scanner = new Scanner(request.getInputStream()).useDelimiter("\\A");
        String body = scanner.hasNext() ? scanner.next() : "";
        JsonParser parser = new JsonParser();
        JsonElement parse = parser.parse(body);
        JsonObject object = parse.getAsJsonObject();
        OnlyOfficeFileServiceImpl.log.info("-----------------------------------------------审核表审批表保存时调用的接口为:" + object.toString());
        String key = ULIDGenerator.generateULID();
        int status = object.get("status").getAsInt();
        Integer SignStatus= onlyOfficeFileMapper.getApprovalFileSignStatusByName(studyId,fileName);
        //手动保存才更新文件
        if ((status == 6|| status == 2)&& 4!=SignStatus)  {
            OnlyOfficeFileServiceImpl.log.info("-----------------------------------------------审核表审批表保存时调用的接口查询到的签字状态:" + SignStatus);
            JsonObject userInfo = object.get("history").getAsJsonObject();
            String userId="";
            if (!userInfo.isEmpty()) {
                userId = userInfo.get("changes").getAsJsonArray().get(0).getAsJsonObject().get("user").getAsJsonObject().get("id").toString();
            }
            String backUpFilePath = "";
            //保存本次的文件信息和文件路径,
            backUpFilePath= "/home/<USER>/8087/onlyOfficeFile/record/" + fileName;
            onlyOfficeFileMapper.insertApplyEditRwecord(key, userId, userName, backUpFilePath, key, studyId,batchNum);

            String downloadUrl = object.get("url").getAsString();
            OnlyOfficeFileServiceImpl.log.info("-----------------------------------------------审核表审批表保存时的文件下载原始链接:" + downloadUrl);
            // Set up SSL context
            SSLContext sslContext = null;
            try {
                sslContext = SSLContext.getInstance("TLS");
            } catch (NoSuchAlgorithmException e) {
                throw new RuntimeException(e);
            }
            TrustManager[] tm = {new MyX509TrustManager()};
            try {
                sslContext.init(null, tm, new java.security.SecureRandom());
            } catch (KeyManagementException e) {
                throw new RuntimeException(e);
            }
            SSLSocketFactory ssf = sslContext.getSocketFactory();

            // Open connection
            URL url = new URL(downloadUrl);
            HttpsURLConnection con = (HttpsURLConnection) url.openConnection();
            con.setSSLSocketFactory(ssf);
            con.setRequestMethod("GET");

            // Set hostname verifier if necessary
            con.setHostnameVerifier(new NullHostNameVerifier());

            // Define the local path where the file will be saved
            String localFilePath = "/home/<USER>/8087/onlyOfficeFile/" + studyId + "_AuditApproval_" + key + ".xlsx";

            // Read the input stream and write to both the specified local file path and backup path
            try (InputStream in = new BufferedInputStream(con.getInputStream());
                 FileOutputStream fos1 = new FileOutputStream(localFilePath);
                 FileOutputStream fos2 = new FileOutputStream(backUpFilePath);
                 BufferedOutputStream bout1 = new BufferedOutputStream(fos1, 8192);
                 BufferedOutputStream bout2 = new BufferedOutputStream(fos2, 8192)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    bout1.write(buffer, 0, bytesRead);
                    bout2.write(buffer, 0, bytesRead);
                }

                // Ensure all data is written
                bout1.flush();
                bout2.flush();
            } catch (IOException e) {
                // Handle exception (log it, throw it, etc.)
                e.printStackTrace();
            } finally {
                // Disconnect the connection
                con.disconnect();
            }


        }
        writer.write("{\"error\":0}");
    }
}
