package com.hengrui.blind_back.utils;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.hengrui.blind_back.blind.utils.MyX509TrustManager;
import com.hengrui.blind_back.blind.utils.NullHostNameVerifier;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import java.io.*;
import java.net.MalformedURLException;
import java.net.ProtocolException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName RTSMAPI
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/22 15:02
 * @Version 1.0
 **/
@Slf4j
@Component
public class RTSMAPI {

    //获取token
    public static String getRTSMToken(String studyCode) {
        String data = "";
        String token = "";
        JSONObject param = new JSONObject();
        param.put("studyCode", studyCode);
        param.put("userName", SASOnlieConstant.RTSM_API_USER);
        param.put("pwd", SASOnlieConstant.RTSM_API_PASS);
        StringBuilder response = new StringBuilder();
        String requestURL = "";
        requestURL = SASOnlieConstant.RTSM_API + "rtsm/app/getUatToken.do";


        RTSMAPI.log.info("---------------------------------------the dataSave request URL is :" + requestURL);
        try {
            URL url = new URL(requestURL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setDoOutput(true); // Set this before setting the request method
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Connection", "keep-alive");
            connection.setDoOutput(true);
            // Pass the parameters to the API
            String jsonStr = param.toString();
            RTSMAPI.log.info("-----------------usersyn dataSave API  post body data is :" + jsonStr);
            // Write the request body
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonStr.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            int responseCode = connection.getResponseCode();
            String requestMethod = connection.getRequestMethod();
            RTSMAPI.log.info("requestMethod is : " + requestMethod);
            RTSMAPI.log.info("Response Code: " + responseCode);
            if (responseCode == HttpsURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                RTSMAPI.log.info("call usersyn dataSave API response is :" + response.toString());

            }
        } catch (ProtocolException e) {
            throw new RuntimeException(e);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        JSONObject jsonResponse = new JSONObject(response.toString());
        // Extract the study_id
        data = jsonResponse.get("data").toString();
        JSONObject tokenInfo = new JSONObject(data);
        token = tokenInfo.get("token").toString();
        return token;
    }


    //获取文件列表
    public static Map<String, String> getRTSMFileListInfo(String studyCode, String token, String fileType) {
        String data = "";
        String listInfoStr = "";
        Map<String, String> result = new HashMap<>();
        JSONObject param = new JSONObject();
        param.put("studyCode", studyCode);
        param.put("userName", SASOnlieConstant.RTSM_API_USER);
        param.put("pwd", SASOnlieConstant.RTSM_API_PASS);
        StringBuilder response = new StringBuilder();
        String requestURL = "";
        requestURL = SASOnlieConstant.RTSM_API + "rtsm/app/uatDownLoadReportList.do";


        RTSMAPI.log.info("---------------------------------------the dataSave request URL is :" + requestURL);
        try {
            URL url = new URL(requestURL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setDoOutput(true); // Set this before setting the request method
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Connection", "keep-alive");
            connection.setRequestProperty("token", token);
            connection.setDoOutput(true);
            // Pass the parameters to the API
            String jsonStr = param.toString();
            RTSMAPI.log.info("-----------------usersyn dataSave API  post body data is :" + jsonStr);
            // Write the request body
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonStr.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            int responseCode = connection.getResponseCode();
            String requestMethod = connection.getRequestMethod();
            RTSMAPI.log.info("requestMethod is : " + requestMethod);
            RTSMAPI.log.info("Response Code: " + responseCode);
            if (responseCode == HttpsURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                RTSMAPI.log.info("call usersyn dataSave API response is :" + response.toString());

            }
        } catch (ProtocolException e) {
            throw new RuntimeException(e);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        JSONObject jsonResponse = new JSONObject(response.toString());
        // Extract the study_id
        data = jsonResponse.get("data").toString();
        JSONObject listInfo = new JSONObject(data);
        listInfoStr = listInfo.get("list").toString();
        JSONArray listInfoArray = new JSONArray(listInfoStr);
        for (int i = 0; i < listInfoArray.size(); i++) {
            JSONObject jsonObject = listInfoArray.getJSONObject(i);
            if (!ObjectUtils.isEmpty(jsonObject.get("reportname")) && jsonObject.get("reportname").equals(fileType)) {
                result.put("reportfilepath", jsonObject.get("reportfilepath").toString());
                result.put("reportfilename", jsonObject.get("reportfilename").toString());
                result.put("oid", jsonObject.get("oid").toString());
                return result;
            }
        }
        return result;
    }


    //下载文件
    public static String downloadRTSMFile(String path, String fileName, String oid, String token) throws IOException, NoSuchAlgorithmException {

        JSONObject param = new JSONObject();
        param.put("path", path);
        param.put("fileName", fileName);
        param.put("oid", oid);
        // Call the download interface
        String fileUrl = SASOnlieConstant.RTSM_API + "rtsm/app/download.do";

        // Set up to access the https request through ip address
        HttpsURLConnection.setDefaultHostnameVerifier(new NullHostNameVerifier());
        TrustManager[] tm = {new MyX509TrustManager()};
        SSLContext sslContext = SSLContext.getInstance("TLS");
        try {
            sslContext.init(null, tm, new java.security.SecureRandom());
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        }
        // Get the SSLSocketFactory object from the above SSLContext object
        SSLSocketFactory ssf = sslContext.getSocketFactory();
        String urlStr = fileUrl;
        URL url = null;
        try {
            url = new URL(urlStr);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
        HttpsURLConnection con = null;
        try {
            con = (HttpsURLConnection) url.openConnection();
            con.setDoOutput(true);// Open output stream to send data to the server
            con.setDoInput(true); // Open input stream to get data from the server
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        con.setSSLSocketFactory(ssf);
        try {
            con.setRequestMethod("POST"); // Set to submit data using POST method
            con.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            con.setRequestProperty("Accept", "*/*");
            con.setRequestProperty("Connection", "keep-alive");
            con.setRequestProperty("token", token);
            con.setDoOutput(true);
            // Pass the parameters to the API
            String jsonStr = param.toString();
            // Write the request body
            try (OutputStream os = con.getOutputStream()) {
                byte[] input = jsonStr.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

        } catch (ProtocolException e) {
            throw new RuntimeException(e);
        }


        // Set the sending parameters
        PrintWriter out = null;
        try {
            out = new PrintWriter(new OutputStreamWriter(con.getOutputStream(), "UTF-8"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        out.flush();
        out.close();
        try {
            InputStream in = con.getInputStream();
            if (!ObjectUtils.isEmpty(in)) {
                Files.copy(in, Paths.get(SASOnlieConstant.RTSM_FILE_PATH + fileName), StandardCopyOption.REPLACE_EXISTING);
            }

        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return SASOnlieConstant.RTSM_FILE_PATH + fileName;

    }


    public static String downloadRTSMFile(String studyCode, String fileType) {
        String downloadPath = "";
        String rtsmToken = getRTSMToken(studyCode);
        Map<String, String> rtsmFileListInfo = getRTSMFileListInfo(studyCode, rtsmToken, fileType);
        String filePath = rtsmFileListInfo.get("reportfilepath").replaceAll("[\r\n]+$", "");
        String fileName = rtsmFileListInfo.get("reportfilename").replaceAll("[\r\n]+$", "");
        String oid = rtsmFileListInfo.get("oid");
        try {
            downloadPath = downloadRTSMFile(filePath, fileName, oid, rtsmToken);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        return downloadPath;
    }
}
