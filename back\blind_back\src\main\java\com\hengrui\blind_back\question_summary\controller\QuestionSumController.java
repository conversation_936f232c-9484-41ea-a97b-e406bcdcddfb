package com.hengrui.blind_back.question_summary.controller;

import com.hengrui.blind_back.question_summary.service.QuestionSumService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName QuestionSumController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/7 13:27
 * @Version 1.0
 **/
@RestController
@Slf4j
public class QuestionSumController {


    @Autowired
    QuestionSumService questionSumService;

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getQuestionsSumReport")
    public Map<String, Object> getQuestionSumFile(String taskId,
                                                  String server,
                                                  String projectId) {
        QuestionSumController.log.info("server is :" + server);
        Map<String, String> results = questionSumService.getQuestionsSumReport(taskId, projectId, "permission_his",  "a".toString());
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", results);
        return result;
    }
}
