<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengrui.blind_back.rtsm.mapper.RTSMFileNameMapper">
    <select id="getMedicalFileName" parameterType="string" resultType="string">
        SELECT 	concat(SUBSTRING_INDEX( SUBSTRING_INDEX( f.FILEPATH, '/', - 1 ), 'en', 1 ) ,'.csv')
        FROM ${fileTableName} f
                 JOIN ${medTableName} c ON f.ID = c.uploadfilename;
    </select>

    <select id="getRandFileName" parameterType="string" resultType="string">
        SELECT 	concat(SUBSTRING_INDEX( SUBSTRING_INDEX( f.FILEPATH, '/', - 1 ), 'en', 1 ) ,'.csv')
        FROM ${fileTableName} f
                 JOIN  ${randTableName} c ON f.ID = c.uploadfilename;
    </select>

    <select id="getRandBlindFilePath" parameterType="string" resultType="map">
        select REPORTFILEPATH ,
               REPORTFILENAME from tbl_report where STUDYID =#{studyId} and STATUS ='导出成功' and env='dev' and REPORTNAME ='随机盲底比对报告' order by EXPORTTIME desc limit 1;
    </select>
    <select id="getMedBlindFilePath" parameterType="string" resultType="map">
        select REPORTFILEPATH ,
               REPORTFILENAME from tbl_report where STUDYID =#{studyId} and STATUS ='导出成功' and env='dev' and REPORTNAME ='药物盲底比对报告' order by EXPORTTIME desc limit 1;
    </select>
    <select id="getMedTrailFilePath" parameterType="string" resultType="map">
        select REPORTFILEPATH ,
               REPORTFILENAME from tbl_report where STUDYID =#{studyId} and STATUS ='导出成功' and env='dev' and REPORTNAME ='药物稽查轨迹报告' order by EXPORTTIME desc limit 1;
    </select>
    <select id="getSubjectTrailPath" parameterType="string" resultType="map">
        select REPORTFILEPATH ,
               REPORTFILENAME from tbl_report where STUDYID =#{studyId} and STATUS ='导出成功' and env='dev' and REPORTNAME ='受试者稽查轨迹报告' order by EXPORTTIME desc limit 1;
    </select>
    <select id="getWayBillTrailPath" parameterType="string" resultType="map">
        select REPORTFILEPATH ,
               REPORTFILENAME from tbl_report where STUDYID =#{studyId} and STATUS ='导出成功' and env='dev' and REPORTNAME ='运单稽查轨迹报告' order by EXPORTTIME desc limit 1;
    </select>


</mapper>
