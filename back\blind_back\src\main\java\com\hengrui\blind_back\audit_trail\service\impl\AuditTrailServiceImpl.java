package com.hengrui.blind_back.audit_trail.service.impl;

import com.alibaba.fastjson.JSON;
import com.hengrui.blind_back.audit_trail.service.AuditTrailService;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.hengrui.blind_back.utils.CallPython;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName AuditTrailServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/7 14:53
 * @Version 1.0
 **/


@Service
@Slf4j
public class AuditTrailServiceImpl implements AuditTrailService {
    @Autowired
    CallPython callPython;

    @Override
    public Map<String, String> getAuditTrailFile(String taskId, String projectId) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        //如果是自动调度，则存储新建表单记录之后的记录Id
        String recordId="";
        String tableId="";
        if(!ObjectUtils.isEmpty(formInfo.get("recordId"))){
            recordId= formInfo.get("recordId");
            tableId=formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id",recordId);
            object.put("formData",formData);
            projectId=object.toJSONString();
        }else{
            tableId=projectId;
            recordId=taskId;
        }
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        AuditTrailServiceImpl.log.info("------------获取到的formId是：" + formId + "------------");
        AuditTrailServiceImpl.log.info("------------获取到的studyId是：" + studyId + "------------");
        Map<String, String> result = new HashMap<>();
        List<Map<String, String>> filesFromEDC = new ArrayList<>();
        Map<String, String> fileObject = new HashMap<>();
        fileObject.put("fid", "permission_his");
        fileObject.put("fileType", ".xlsx");
        filesFromEDC.add(fileObject);


        String uuid = ULIDGenerator.generateULID();
        Map<String, String> ENVInfo = new HashMap<>();
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("ENV", "UAT");
        ENVInfo.put("data_format", "Excel");
        ENVInfo.put("data_type", "ins_tra");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", recordId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", tableId);
        ENVInfo.put("fileSuffix", "a".toString());
        ENVInfo.put("isLatest", "");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        Map<String,String> results = callPython.downloadEDCServerFile(ENVInfo, filesFromEDC);
        String dataIsTwoDays= results.get("dataIsTwoDays");
        dataIsTwoDays="数据是否在两天之内："+dataIsTwoDays;
        result.put("result", dataIsTwoDays);
        //call python program
        return result;
    }
}
