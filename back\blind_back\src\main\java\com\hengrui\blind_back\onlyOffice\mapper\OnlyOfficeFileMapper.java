package com.hengrui.blind_back.onlyOffice.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.hengrui.blind_back.rtsm.entity.EsignEntity;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Component
@Mapper
@Repository
@DS("slave_3")
public interface OnlyOfficeFileMapper {
    int insertApplyEditRwecord(String id ,String userId,String userName,String fileName,String key,String studyId,String batchNum);

    Map<String, String> getApplyInfo(String studyId,String batchNum);

    int getApplyInfoByVersion(String studyId, String fileKey);

    int checkPDFFileIsExist(String studyId, String fileKey);

    int updateApproveStatus(String studyId, String fileKey, String userName,String versionNum,String signFilePath);

    int updateAASignFilePath(String studyId, String fileKey,String signFilePath);

    List<Map<String, String>> getBatchRecords(String studyId);

    List<Map<String, String>> getAABatchRecords(String studyId);

    void updateValidCodeAndTime(String studyId, String batchNum, String code);

    List<Map<String,Object>> getValidCode(String studyId, String batchNum);

    Map<String,String> getRtsmAuditApprovalInfo(String taskId);

    int getVersionNum(String studyId,String approveVersion);

    int getVersionNumPrefix(String studyId);

    void insertApplyInfo(String id, String userId, String userName, String fileName,  String studyId,  String accountName, String versionNum,String applyDate,String param);

    void updateApplyDate(String studyId,String batchNum,String applyDate);


    void updateFormInfo(String batchNum,String studyId, String formInfo,String userName);

    String getFormInfo(String batchNum);

    void deleteBatchRecord(String studyId, String batchNum, String userName);

    Map<String, String> getRecordByFileName(String studyId, String filePath);

    String getApprovedFilePath(String studyId, String fileKey);

    String getSignFilePath(String studyId, String fileKey);

    //存储到esign_sign_files表
    int insertSignFile(String fileId,String fileName,String filePath,String fileMd5,String createBy,String studyId,String fileCode );

    String getRecordParamByFileKey(String studyId, String fileKey);

    //创建签字任务
    int insertSignTask(Map<String, Object> params);

    int insertSignUsers(String uuid, String taskId, String userName, String email, String signReason,String signUrl);

    void updateSignerStatus(Map<String, Object> params);

    void updateSignFileStatus(Map<String, Object> params);

    String getFileNameByTaskId(String taskId);

    List<Map<String, String>> getSignerList(String taskId);

    String getTaskExpireDate(String taskId);

    Map<String,String> getSingTimeByTaskId(String taskId,String email);

    Map<String,String> getAuthorEmailByTaskId(String taskId);

    String getSignUrlByTaskId(String taskId, String signerEmail);

    int updateTaskStatus(Map<String, Object> signerParam);

    int updateSingerStatus(Map<String, Object> signerParam);

    List<Map<String,Object>> getEsignInfo(EsignEntity param);

    List<Map<String,String>> getAccountEmail(String studyId, String fileKey);

    int addSendMailRecord(String uuid, String name, String email, String taskId, String content, String subject);

    int insertApplyReviewers(String reviewerId, String uuid, String accountName, String accountEmail);

    List<Map<String, String>> getReviewersByRtsmApplyId(String rtsmAaplyId);

    int judgeIsAllApprove(String studyId, String fileKey);

    void updateReviewerApproveStatus(String studyId, String fileKey, String userName);

    void updateCancelSign(String taskId, String userName, String userEmail);

    void updateRtsmApplySignStatus(String taskId,String signedFilePath);

    Map<String,String> getAccountSignEmailParams(String fileKey);

    String getApproveStatusByName(String studyId, String batchNum, String userName);

    Map<String, String> getSignPosition(String funcName, String type);

    String getLatestVersion(String studyId);

    List<Map<String, String>> getExpiringSignTasks();

    List<Map<String, String>> getUnsignedSigners(String taskId);

    void updateSignerNotifyStatus(String taskId, String email);

    void deleteSignUser(String taskId, String userEmail);

    Map<String,String> getMailInfoByTaskIdEmail(String taskId,String name, String email);

    void updateRTSMApplyMailInfo(String title, String content, String receivers,String fileKey);

    Map<String, String> getMailInfoByFileName(String fileName);

    Map<String, String> getMailInfoByFileKey(String studyId, String fileKey);

    String getSignerListByFileKey(String studyId, String fileKey);

    void updateApproveStatusByFileKey(String fileKey);

    int getAAVersionNumPrefix(String studyId);

    int getAAVersionNum(String studyId, String approveVersion);

    String getAALatestVersion(String studyId);

    void insertAuditApproval(String id, String userName, String fileName,  String studyId,  String accountName, String versionNum,String param);

    void insertAAReviewers(String reviewerId, String uuid, String accountName, String accountEmail);

    void updateAuditApprovalById(String uuid, String fileName);

    void updateAAApproveStatus(String studyId, String fileKey);

    String getAARecordParamByFileKey(String studyId, String fileKey);

    String getAAApprovedFilePath(String studyId, String fileKey);

    Map<String, String> getAAAccountSignEmailParams(String fileKey);

    int checkAAPDFFileIsExist(String studyId, String fileKey);

    String getAASignFilePath(String studyId, String fileKey);

    Map<String, String> getAuditApprovalInfo(String studyId, String batchNum);

    void updateRtsmAuditApprovalSignStatus(String taskId,String signedFilePath);

    void updateAAApproveStatusByFileKey(String fileKey);

    void updateRTSMAAMailInfo(String title, String content, String receivers, String fileKey);

    String getAASignerListByFileKey(String studyId, String fileKey);

    Map<String, String> getAAMailInfoByFileKey(String studyId, String fileKey);

    Map<String, String> getAAMailInfoByFileName(String fileName);

    Integer getApplyFileSignStatusByName(String studyId,String fileName);

    Integer getApprovalFileSignStatusByName(String studyId,String fileName);

    Map<String, String> getRtsmAccountInfo(String taskId);

    Map<String, String>  getAASignFilePathByTaskId(String taskId);

    Map<String, String> getSignFilePathByTaskId(String taskId);
}
