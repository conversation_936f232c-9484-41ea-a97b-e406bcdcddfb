package com.hengrui.blind_back.rtsm.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;

@Component
@Mapper
@Repository
@DS("slave_4")
public interface RTSMFileMapper {
    //获取表id
    String getTableId(String studyId);


    List<String> getRandGroupInfo(String studyId);

    List<String> getSubtrial(String studyId);
}
