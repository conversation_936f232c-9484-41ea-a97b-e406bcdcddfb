package com.hengrui.blind_back.test_file.utils;

import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.sas.iom.SAS.ILanguageService;
import com.sas.iom.SAS.ILanguageServicePackage.CarriageControlSeqHolder;
import com.sas.iom.SAS.ILanguageServicePackage.LineTypeSeqHolder;
import com.sas.iom.SAS.IWorkspace;
import com.sas.iom.SAS.IWorkspaceHelper;
import com.sas.iom.SASIOMDefs.GenericError;
import com.sas.iom.SASIOMDefs.StringSeqHolder;
import com.sas.services.connection.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;


@Component
public class SASForDtaTrigger {


    @Autowired
    SASForDtaIOM SASForDtaIOM;

    public void runCreateSAS(String codePath, String dtaFilePath,String studyName) throws IOEx<PERSON>, ConnectionFactoryException, GenericError {
//        ZeroConfigWorkspaceServer server = new ZeroConfigWorkspaceServer();
//        ManualConnectionFactoryConfiguration config = new ManualConnectionFactoryConfiguration(server);
//        ConnectionFactoryManager manager = new ConnectionFactoryManager();
//        ConnectionFactoryInterface factory = manager.getFactory(config);
//        SecurityPackageCredential cred = new SecurityPackageCredential();
//        ConnectionInterface cx = factory.getConnection(cred);
        // identify the IOM server
        String classID = Server.CLSID_SAS;
        String host = "sas-hrsh-node1.hengrui.com";
        int port = 8591;
        Server server = new BridgeServer(classID,host,port);

        // make a connection factory configuration with the server
        ConnectionFactoryConfiguration cxfConfig =
                new ManualConnectionFactoryConfiguration(server);

        // get a connection factory manager
        ConnectionFactoryManager cxfManager = new ConnectionFactoryManager();

        // get a connection factory that matches the configuration
        ConnectionFactoryInterface cxf = cxfManager.getFactory(cxfConfig);

        // get the administrator interface
        ConnectionFactoryAdminInterface admin = cxf.getAdminInterface();

        // get a connection
        String userName = SASOnlieConstant.SAS_USER;
        String password =SASOnlieConstant.SAS_PASS;
        ConnectionInterface cx = cxf.getConnection(userName,password);
        try {
            // Narrow the connection from the server.
            org.omg.CORBA.Object obj = cx.getObject();
            IWorkspace iWorkspace = IWorkspaceHelper.narrow(obj);
            //insert iWorkspace workspace usage code here
            ILanguageService iLanguageService = iWorkspace.LanguageService();
            iLanguageService.Submit(SASForDtaIOM.readSAS(codePath, dtaFilePath,studyName));

            //sas log 获取
            CarriageControlSeqHolder logCarriageControlHldr = new CarriageControlSeqHolder();
            LineTypeSeqHolder logLineTypeHldr = new LineTypeSeqHolder();
            StringSeqHolder logHldr = new StringSeqHolder();
            iLanguageService.FlushLogLines(Integer.MAX_VALUE, logCarriageControlHldr, logLineTypeHldr, logHldr);


            String[] logLines = logHldr.value;
            for (int i = 0; i < logLines.length; i++) {
                System.out.println(logLines[i]);
            }
        } finally {
            cx.close();
        }
    }


}
