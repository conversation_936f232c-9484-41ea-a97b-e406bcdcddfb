package com.hengrui.blind_back.constant;

public class OperateType {
    private final String OPERATE_TYPE;

    //私有化构造器，为对象赋值
    public OperateType(String OPERATE_TYPE) {
        this.OPERATE_TYPE = OPERATE_TYPE;
    }

    //提供当前枚举的多个对象,为public static final修饰
    public static final OperateType DELETE = new OperateType("删除");
    public static final OperateType RELIVE = new OperateType("激活");

    public static final OperateType KILL = new OperateType("失活");
    public static final OperateType CREATE = new OperateType("创建");

    public static final OperateType SYNC_UAP_USER = new OperateType("同步");
    public static final OperateType UPDATE = new OperateType("修改");
    public static final OperateType UPLOAD = new OperateType("上传");
    public static final OperateType DOWNLOAD = new OperateType("下载");

    public static final OperateType ADD_COMPOUND = new OperateType("新增");

    public static final OperateType DROP_COMPOUND = new OperateType("失效");
    public static final OperateType RECOVER_COMPOUND = new OperateType("激活");

    public static final OperateType ADD_PROJECT = new OperateType("新增");

    public static final OperateType ADD_SITE = new OperateType("新增");

    public static final OperateType EDIT_SITE = new OperateType("编辑");


    public static final OperateType ADD_RECEIVER = new OperateType("新增");

    public static final OperateType EDIT_RECEIVER = new OperateType("编辑");

    public static final OperateType EDIT_COMPOUND = new OperateType("编辑");
    public static final OperateType EDIT_PROJECT = new OperateType("编辑");


    public static final OperateType CRON = new OperateType("修改邮件发送频率");
    public static final OperateType SEND = new OperateType("发送");


    public String getOPERATE_TYPE(){
        return OPERATE_TYPE;
    }
    @Override
    public String toString() {
        return  OPERATE_TYPE;
    }
}
