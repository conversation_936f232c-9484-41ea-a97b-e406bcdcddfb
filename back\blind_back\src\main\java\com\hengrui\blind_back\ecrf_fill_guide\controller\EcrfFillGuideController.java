package com.hengrui.blind_back.ecrf_fill_guide.controller;

import com.hengrui.blind_back.ecrf_fill_guide.service.EcrfFillGuideService;
import com.hengrui.blind_back.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName EcrfFillGuideController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/24 10:03
 * @Version 1.0
 **/

@RestController
@Slf4j
public class EcrfFillGuideController {

    @Autowired
    EcrfFillGuideService ecrfFillGuideService;

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getEcrfFillGuide")
    public Map<String, Object> getEcrfFillGuide(String taskId,
                                                String server,
                                                String projectId) {
        EcrfFillGuideController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> results = ecrfFillGuideService.getEcrfFillGuide(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", results.get("dataIsTwoDays"));
        result.put("data", results);
        return result;
    }
}
