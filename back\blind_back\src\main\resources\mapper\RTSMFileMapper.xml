<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengrui.blind_back.rtsm.mapper.RTSMFileMapper">
    <select id="getTableId" parameterType="string" resultType="string">
        select ID from  tbl_project_info where  COL_STUDYID =#{studyId};
    </select>


    <select id="getSubtrial" parameterType="string" resultType="string">
        select
            COL_SUBTRIALNAME
        from
            tbl_subtrial
        where
                COL_STUDYID in (
                select
                    ID
                from
                    tbl_project_info
                where
                    COL_STUDYID = #{studyId}
            );
    </select>


    <select id="getRandGroupInfo" parameterType="string" resultType="string">
        select
            COL_RAND_GROUP_NAME
        from
            tbl_rand_group_max
        where
                COL_STUDYID in (
                select
                    ID
                from
                    tbl_project_info
                where
                    COL_STUDYID = #{studyId}
            );
    </select>
</mapper>
