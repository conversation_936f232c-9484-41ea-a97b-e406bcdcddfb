package com.hengrui.blind_back.uat.service.impl;

import com.alibaba.fastjson.JSON;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.uat.service.UATService;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.hengrui.blind_back.utils.CallPython;
import com.hengrui.blind_back.utils.SubmitSAS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName UATServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/22 9:48
 * @Version 1.0
 **/
@Slf4j
@Service
public class UATServiceImpl implements UATService {
    @Autowired
    private SubmitSAS submitSAS;

    @Autowired
    CallPython callPython;

    @Override
    public Map<String, String> submitToUATSAS(String taskId, String projectId) {
        //1.从edc server端那文件的 文件信息
        List<Map<String, String>> uploadFilesFromEDC = new ArrayList<>();
        //单独上传excel数据集
        List<Map<String, String>> uploadExcelFilesFromEDC = new ArrayList<>();
        Map<String, String> fileObject1 = new HashMap<>();
        //1.1 UAT测试数据集(SAS)
        fileObject1.put("fid", "test_data_files");
        fileObject1.put("fileType", ".zip");
        Map<String, String> fileObject2 = new HashMap<>();
        //1.2 UAT测试数据集Excel
        fileObject2.put("fid", "uatexcel");
        fileObject2.put("fileType", ".xlsx");
        uploadFilesFromEDC.add(fileObject1);
        uploadExcelFilesFromEDC.add(fileObject2);

        //2.调用的表单所需的部分信息，如参数存放minio的地址和uuid
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId="";
        String tableId="";
        if(!ObjectUtils.isEmpty(formInfo.get("recordId"))){
            recordId= formInfo.get("recordId");
            tableId=formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id",recordId);
            object.put("formData",formData);
            projectId=object.toJSONString();
        }else{
            tableId=projectId;
            recordId=taskId;
        }
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        if(studyId.equals("SHR-1701-II-205")){
            return new HashMap<>();
        }

        String used_language = CDTMSAPI.getStudyLanguage(studyId);

        String uuid = ULIDGenerator.generateULID();
        formInfo.put("uuid", uuid);
        String paramFileName = formInfo.get("studyId").toString() + "_UAT_" + uuid + ".json";
        //2.1 submit to sas via json params and define the json path on the minio storage platform
        formInfo.put("jsonMinioPath", SASOnlieConstant.PREFIX_PRO_MINIO+"/uat/json/" + paramFileName);
        formInfo.put("sasCodePath", SASOnlieConstant.SAS_CODE_PATH);
        formInfo.put("paramFileName", paramFileName);
        formInfo.put("bucket", "uat");
        formInfo.put("language", used_language);
        //3.请求cdtms API所需的环境信息和接口所需的params
        Map<String, String> ENVInfo = new HashMap<>();

        ENVInfo.put("ENV", "UAT");
        ENVInfo.put("data_type", "data_set");
        ENVInfo.put("studyId", studyId);
        ENVInfo.put("taskId", recordId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("projectId", tableId);
        ENVInfo.put("uuid", uuid);
        ENVInfo.put("data_format", "Excel");
        ENVInfo.put("requestPrefix", SASOnlieConstant.REMOTE_SERVER_API_PREFIX);
        ENVInfo.put("isLatest", "");
        ENVInfo.put("fileSuffix", "a".toString());

        callPython.downloadEDCServerFile(ENVInfo,uploadExcelFilesFromEDC);
        formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        ENVInfo.put("formId", formId);
        ENVInfo.put("data_format", "SAS");

        //4.after call sas programe need the cdtms formInfo for upload
        String outputName = "output/" + formInfo.get("studyId").toString() + SASOnlieConstant.UAT_SUFFIX;
        formInfo.put("outputName", outputName);
        List<Map<String, String>> sasOutputFilesInfo = new ArrayList<>();
        Map<String, String> sasOutputFile = new HashMap<>();
        sasOutputFile.put("fid", "dataset_uat");
        sasOutputFile.put("outputName", outputName);
        sasOutputFilesInfo.add(sasOutputFile);
        //5.use above params to call the sas programe
        return submitSAS.submitToSAS(ENVInfo, uploadFilesFromEDC, formInfo, sasOutputFilesInfo);
    }
}
