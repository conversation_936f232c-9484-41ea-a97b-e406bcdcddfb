package com.hengrui.blind_back.parse_excel_toDB.controller;

import com.hengrui.blind_back.parse_excel_toDB.service.ParseExcelToDBService;
import com.hengrui.blind_back.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName ParseExcelToDBController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/1 17:32
 * @Version 1.0
 **/
@RestController
@Slf4j
public class ParseExcelToDBController {
    @Autowired
    ParseExcelToDBService parseExcelToDBService;

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/parseDBDefineDatatoDB")
    public Map<String, Object> getQuestionSumFile(String taskId,
                                                  String server,
                                                  String projectId) {
        ParseExcelToDBController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> results = parseExcelToDBService.parseDBDefineDatatoDB(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", results);
        return result;
    }
}
