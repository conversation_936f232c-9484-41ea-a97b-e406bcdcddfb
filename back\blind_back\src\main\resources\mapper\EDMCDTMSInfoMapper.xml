<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengrui.blind_back.blind.mapper.EDMCDTMSInfoMapper">
    <select id="getEDMMail" parameterType="string" resultType="string">
        SELECT DISTINCT
            COL_EMAIL,
            COL_SJH
        FROM
            (
                WITH TEMP AS (
                    SELECT
                        TBL_XSHT.COL_STUDYID,
                        TBL_XSHT.COL_PROJECT_NAME,
                        TBL_XSHT.COL_STUDY_PHASE,
                        TBL_ROLES.COL_MEMBER,
                        TBL_ROLES.COL_LIMITNUM
                    FROM
                        CDTMSEN_VAL.TBL_XSHT
                            LEFT JOIN CDTMSEN_VAL.TBL_ROLES ON TBL_XSHT.id = TBL_ROLES.COL_STUDYID
                    WHERE
                        TBL_XSHT.COL_STUDYID = #{projectName}
                      AND
                        TBL_ROLES.COL_ACTIVE = '1'
                      AND COL_LIMITNUM = 'EDM'
                )
                SELECT DISTINCT
                    TBL_EMPLOYEE_MANAGER_MAP.COL_EMAIL,
                    TBL_RYJL.COL_SJH
                FROM
                    TEMP
                        LEFT JOIN CDTMSEN_VAL.TBL_EMPLOYEE_MANAGER_MAP ON TEMP.COL_MEMBER = TBL_EMPLOYEE_MANAGER_MAP.COL_ACCOUNT_ID
                        LEFT JOIN CDTMSEN_VAL.TBL_RYJL ON TBL_EMPLOYEE_MANAGER_MAP.COL_EMAIL = TBL_RYJL.COL_EMAIL
                WHERE
                    TBL_EMPLOYEE_MANAGER_MAP.COL_NAME IS NOT NULL
                  AND TBL_EMPLOYEE_MANAGER_MAP.COL_EMAIL IS NOT NULL
                ORDER BY
                    COL_STUDYID
                        DESC
            ) result
    </select>
    <select id="getEDMQCMail" parameterType="string" resultType="string">
        SELECT DISTINCT
            COL_EMAIL
        FROM
            (
                WITH TEMP AS (
                    SELECT
                        TBL_XSHT.COL_STUDYID,
                        TBL_XSHT.COL_PROJECT_NAME,
                        TBL_XSHT.COL_STUDY_PHASE,
                        TBL_ROLES.COL_MEMBER,
                        TBL_ROLES.COL_LIMITNUM
                    FROM
                        CDTMSEN_VAL.TBL_XSHT LEFT JOIN CDTMSEN_VAL.TBL_ROLES ON TBL_XSHT.id = TBL_ROLES.COL_STUDYID
                    WHERE
                        TBL_XSHT.COL_STUDYID = #{projectName}
                      AND TBL_ROLES.COL_ACTIVE = '1'
                      AND COL_LIMITNUM = 'EDM(QC)'
                ) SELECT DISTINCT
                    TBL_EMPLOYEE_MANAGER_MAP.COL_EMAIL
                FROM
                    TEMP LEFT JOIN CDTMSEN_VAL.TBL_EMPLOYEE_MANAGER_MAP ON TEMP.COL_MEMBER = TBL_EMPLOYEE_MANAGER_MAP.COL_ACCOUNT_ID
                WHERE
                    COL_NAME IS NOT NULL
                  AND COL_EMAIL IS NOT NULL
                ORDER BY
                    COL_STUDYID DESC
            ) result
    </select>
    <select id="getProjectMemberMail" parameterType="string" resultType="map">
        WITH TEMP AS (
            SELECT
                TBL_CRA.COL_CRA_NAME AS NAME,
                TBL_CRA.COL_CRA_UNIT AS unit,
                TBL_CRA.COL_CRA_EMAIL AS email,
                TBL_CRA.COL_BLINDING AS blindStatus,
                TBL_CRA.COL_SYZT AS memberStatus,
                CASE

                    WHEN TBL_CRA.COL_LIMITNUM = '1' THEN
                        '医学总监'
                    WHEN TBL_CRA.COL_LIMITNUM = '2' THEN
                        '医学经理'
                    WHEN TBL_CRA.COL_LIMITNUM = '3' THEN
                        '项目经理'
                    WHEN TBL_CRA.COL_LIMITNUM = '4' THEN
                        'CRA'
                    WHEN TBL_CRA.COL_LIMITNUM = '5' THEN
                        '统计师'
                    WHEN TBL_CRA.COL_LIMITNUM = '6' THEN
                        '统计编程经理'
                    WHEN TBL_CRA.COL_LIMITNUM = '7' THEN
                        '药物安全警戒人'
                    WHEN TBL_CRA.COL_LIMITNUM = '8' THEN
                        'CTA'
                    WHEN TBL_CRA.COL_LIMITNUM = '9' THEN
                        '项目总监'
                    WHEN TBL_CRA.COL_LIMITNUM = '10' THEN
                        '药品管理员'
                    WHEN TBL_CRA.COL_LIMITNUM = '11' THEN
                        'EDA'
                    WHEN TBL_CRA.COL_LIMITNUM = '12' THEN
                        '药理经理'
                    WHEN TBL_CRA.COL_LIMITNUM = '13' THEN
                        '稽查组长'
                    WHEN TBL_CRA.COL_LIMITNUM = '14' THEN
                        '统计师总监'
                    WHEN TBL_CRA.COL_LIMITNUM = '15' THEN
                        '统计编程总监'
                    WHEN TBL_CRA.COL_LIMITNUM = '16' THEN
                        '药理总监'
                    WHEN TBL_CRA.COL_LIMITNUM = '17' THEN
                        '转化医学'
                    WHEN TBL_CRA.COL_LIMITNUM = '21' THEN
                        'CRO DM'
                    END ROLE,
                TBL_EXT_DATA.COL_EXT_DATE
            FROM
                CDTMSEN_VAL.TBL_XSHT
                    LEFT JOIN CDTMSEN_VAL.TBL_CRA ON TBL_XSHT.id = TBL_CRA.COL_STUDYID
                    LEFT JOIN CDTMSEN_VAL.TBL_EXT_DATA ON TBL_XSHT.id = TBL_EXT_DATA.COL_STUDYID
            WHERE
                TBL_XSHT.COL_STUDYID = #{projectName}

              AND COL_SYZT = '1'
              AND COL_EXT_VERSION LIKE '%.0%'
              AND TBL_EXT_DATA.COL_CRF_ZT = '10'
            ORDER BY
                COL_EXT_DATE DESC
        ),
             TEMP1 AS (
                 SELECT
                     max(TBL_EXT_DATA.COL_EXT_DATE) as update_time
                 FROM
                     CDTMSEN_VAL.TBL_XSHT
                         LEFT JOIN CDTMSEN_VAL.TBL_CRA ON TBL_XSHT.id = TBL_CRA.COL_STUDYID
                         LEFT JOIN CDTMSEN_VAL.TBL_EXT_DATA ON TBL_XSHT.id = TBL_EXT_DATA.COL_STUDYID
                 WHERE
                     TBL_XSHT.COL_STUDYID = #{projectName}
                   AND COL_SYZT = '1'
                   AND COL_EXT_VERSION LIKE '%.0%' and TBL_EXT_DATA.COL_CRF_ZT='10'
                 ORDER BY
                     COL_EXT_DATE DESC
             ) SELECT NAME,unit,email,blindStatus,memberStatus,CONCAT( CONCAT( email, '   ' ), ROLE ) AS info FROM TEMP,TEMP1 WHERE TEMP.COL_EXT_DATE=TEMP1.update_time
    </select>
    <select id="getUserInfoByMail" parameterType="string" resultType="map">
        SELECT COL_SJH,COL_RY from CDTMSEN_VAL.TBL_RYJL WHERE COL_EMAIL=#{userName}
    </select>

    <select id="getEDCName" parameterType="string" resultType="string">
        SELECT
            EDCNAME
        FROM
            CDTMSEN_VAL.V_25
        WHERE
            STUDYID =#{studyId}
    </select>
</mapper>
