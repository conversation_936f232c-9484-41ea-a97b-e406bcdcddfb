package com.hengrui.blind_back.rtsm.controller;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.hengrui.blind_back.blind.utils.ResponseResult;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import com.hengrui.blind_back.rtsm.entity.EsignEntity;
import com.hengrui.blind_back.rtsm.service.RTSMService;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.hengrui.blind_back.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName RTSMController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/11 11:14
 * @Version 1.0
 **/
@RestController
@Slf4j
public class RTSMController {

    @Autowired
    RTSMService rtsmService;

    /**
     * 随机一致性比对
     * @param taskId
     * @param server
     * @param projectId
     * @return
     */
    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/randCompare")

    public Map<String, Object> submitToUATSAS(String taskId,
                                              String server,
                                              String projectId) {
        RTSMController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> results = rtsmService.submitToRTSMSAS(taskId, projectId);
        if (results.isEmpty()){
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("code", 200);
            result.put("msg", "未检测到参数配置!");
            result.put("data", results);
            return result;
        }
        String edcFileDate = results.get("edcFileDate");
        //1.call getInfo API
        Map<String, String> formInfoByTaskId = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId = "";
        String tableId = "";
        String studyId = formInfoByTaskId.get("studyId");
        if (!ObjectUtils.isEmpty(formInfoByTaskId.get("recordId"))) {
            recordId = formInfoByTaskId.get("recordId");
            tableId = formInfoByTaskId.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }
        //1.1 get id from formInfoByTaskId
        String data = formInfoByTaskId.get("param");
        String dataId = "";
        if (!data.isEmpty()) {
            JSONObject formInfoData = new JSONObject(data);
            dataId = formInfoData.get("id").toString();
        }
        JSONObject temp = new JSONObject();
        JSONObject params = new JSONObject();
        temp.put("id", dataId);
        RTSMController.log.info("----------------------------------------最新的EDC数据集日期是 :" + edcFileDate);
        temp.put("edc_data_dt", edcFileDate);
        params.put("data", temp);
        RTSMController.log.info("call dataSave params is :" + params.toString());
        String newFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        params.put("formId", newFormId);
        params.put("taskId", recordId);
        params.put("projectId", tableId);
        RTSMController.log.info("最新的formId是 :" + newFormId);
        CDTMSAPI.dataSave(params);
        //update status for workflow
        String loginUserRole = CDTMSAPI.getLoginUserRole(studyId, taskId);
        RTSMController.log.info("获取到当前登录人在项目内的角色是 :" + loginUserRole);
        if (loginUserRole.equals("TDM")) {
            CDTMSAPI.updateWorkFlowStatus(taskId, projectId, "status", "01");
        } else if (loginUserRole.equals("DM")) {
            CDTMSAPI.updateWorkFlowStatus(taskId, projectId, "status", "01");
        } else {
            CDTMSAPI.updateWorkFlowStatus(taskId, projectId, "status", "01");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", results.get("dataIsTwoDays"));
        result.put("data", results);
        return result;
    }


    /***
     * 随机一致性比对检查
     * @param taskId
     * @param server
     * @param projectId
     * @return
     */
    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/randCompareCheck")

    public Map<String, Object> randCompareCheck(String taskId,
                                                String server,
                                                String projectId) {
        RTSMController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> results = rtsmService.submitToRTSMSASBack(taskId, projectId);
        if (results.isEmpty()){
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("code", 200);
            result.put("msg", "未检测到参数配置!");
            result.put("data", results);
        }



        String edcFileDate = results.get("edcFileDate");
        String edcFileName = results.get("edcFileName");
        //1.call getInfo API
        Map<String, String> formInfoByTaskId = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId = "";
        String tableId = "";
        if (!ObjectUtils.isEmpty(formInfoByTaskId.get("recordId"))) {
            recordId = formInfoByTaskId.get("recordId");
            tableId = formInfoByTaskId.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }
        //1.1 get id from formInfoByTaskId
        String data = formInfoByTaskId.get("param");
        String dataId = "";
        if (!data.isEmpty()) {
            JSONObject formInfoData = new JSONObject(data);
            dataId = formInfoData.get("id").toString();
        }
        JSONObject temp = new JSONObject();
        JSONObject params = new JSONObject();
        temp.put("id", dataId);
        RTSMController.log.info("----------------------------------------最新的EDC数据集日期是 :" + edcFileDate);
        temp.put("edc_data_dt", edcFileDate);
        temp.put("edc_dataset_t", edcFileName);
        params.put("data", temp);
        RTSMController.log.info("call dataSave params is :" + params.toString());
        String newFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        params.put("formId", newFormId);
        params.put("taskId", recordId);
        params.put("projectId", tableId);
        RTSMController.log.info("最新的formId是 :" + newFormId);
        CDTMSAPI.dataSave(params);
        //update status for workflow
        CDTMSAPI.updateWorkFlowStatus(taskId, projectId, "crf_zt", "05");
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", results.get("dataIsTwoDays"));
        result.put("data", results);
        return result;
    }


    /**
     * 获取申请表信息
     * @param studyId
     * @param batchNum
     * @return
     */
    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getApplyInfo")
    public ResponseResult<?> getApplyInfo(String studyId, String batchNum) {
        Map<String, String> applyInfo = rtsmService.getApplyInfo(studyId, batchNum);
        return new ResponseResult<>(200, "OK", applyInfo);
    }


    /**
     * 获取审核、审批表信息
     * @param studyId
     * @param batchNum
     * @return
     */
    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getAuditApprovalInfo")
    public ResponseResult<?> getAuditApprovalInfo(String studyId, String batchNum) {
        Map<String, String> applyInfo = rtsmService.getAuditApprovalInfo(studyId, batchNum);
        return new ResponseResult<>(200, "OK", applyInfo);
    }


    /**
     * 审核通过
     * @param studyId
     * @param fileKey
     * @param userName
     * @return
     */
    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/accountApprove")
    public ResponseResult<?> accountApprove(String studyId, String fileKey, String userName) {
        String sendInfo = rtsmService.accountApprove(studyId, fileKey, userName);
        return new ResponseResult<>(200, "OK", sendInfo);
    }

    /**
     * 发送签字
     *
     * @param studyId
     * @param fileKey
     * @param userName
     * @return
     */
    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/sendSign")
    public ResponseResult<?> sendSign(@RequestBody String param,String studyId, String fileKey, String userName) {
        return rtsmService.sendSign( param,studyId, fileKey, userName);
    }






    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/sendAccountSign")
    public ResponseResult<?> sendAccountSign(@RequestBody String param,String studyId) {
        SASOnlieConstant.setRemoteServerApi("https://cdtms-tst.hengrui.com/");
        SASOnlieConstant.setRemoteServerApiPrefix("https://cdtms-tst.hengrui.com/");
        SASOnlieConstant.setCdtmsCheckLoginApi("https://cdtms-tst.hengrui.com/extdatabind.checktoken.do?token=");
        return rtsmService.sendAccountSign( param,studyId);
    }


    /**
     * 签字密码校验
     *
     * @param email
     * @param pass
     * @param taskId
     * @return
     */
    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/checkSignPass")
    public ResponseResult<?> checkSignPass(String email, String pass, String taskId) {
        SASOnlieConstant.setRemoteServerApi("https://cdtms-tst.hengrui.com/");
        SASOnlieConstant.setRemoteServerApiPrefix("https://cdtms-tst.hengrui.com/");
        SASOnlieConstant.setCdtmsCheckLoginApi("https://cdtms-tst.hengrui.com/extdatabind.checktoken.do?token=");
        return rtsmService.checkSignPass(email, pass, taskId);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/checkFirstSign")
    public ResponseResult<?> checkFirstSign(String email) {
        SASOnlieConstant.setRemoteServerApi("https://cdtms-tst.hengrui.com/");
        SASOnlieConstant.setRemoteServerApiPrefix("https://cdtms-tst.hengrui.com/");
        SASOnlieConstant.setCdtmsCheckLoginApi("https://cdtms-tst.hengrui.com/extdatabind.checktoken.do?token=");
        return rtsmService.checkFirstSign(email);
    }


    /**
     * 设置签字密码
     * @param email
     * @param pass
     * @return
     */
    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/setSignPass")
    public ResponseResult<?> setSignPass(String email, String pass) {
        return rtsmService.setSignPass(email, pass);
    }


    /**
     * 更新申请日期
     * @param studyId
     * @param batchNum
     * @param applyDate
     * @return
     */
    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/updateApplyDate")
    public ResponseResult<?> updateApplyDate(String studyId, String batchNum, String applyDate) {
        String updateInfo = rtsmService.updateApplyDate(studyId, batchNum, applyDate);
        return new ResponseResult<>(200, "OK", updateInfo);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/makeRTSMTemplate")
    public ResponseResult<?> makeRTSMTemplate(String studyId, String batchNum, String accountManagerName) {
        String result = rtsmService.makeRTSMTemplate(studyId,"", batchNum, accountManagerName, 1,1,"",null,"");
        return new ResponseResult<>(200, "OK", result);
    }


    /**
     * 新增随机申请表
     * @param param
     * @param studyId
     * @param userName
     * @param applyDate
     * @return
     */
    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/addBatchRecord")
    @ResponseBody
    public ResponseResult<?> addBatchRecord(@RequestBody String param, String studyId, String userName, String applyDate) {
        SASOnlieConstant.setRemoteServerApi("https://cdtms-tst.hengrui.com/");
        SASOnlieConstant.setRemoteServerApiPrefix("https://cdtms-tst.hengrui.com/");
        SASOnlieConstant.setCdtmsCheckLoginApi("https://cdtms-tst.hengrui.com/extdatabind.checktoken.do?token=");
        String result = rtsmService.addBatchRecord(studyId, userName, applyDate, param);
        if (result.equals("fail")) {
            return new ResponseResult<>(500, "internal error", result);
        } else {
            return new ResponseResult<>(200, "OK", result);
        }

    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getStudyBatchRecords")
    public ResponseResult<?> getStudyBatchRecords(String studyId) {
        List<Map<String, String>> result = rtsmService.getStudyBatchRecords(studyId);
        return new ResponseResult<>(200, "OK", result);
    }

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getAABatchRecords")
    public ResponseResult<?> getAABatchRecords(String studyId) {
        List<Map<String, String>> result = rtsmService.getAABatchRecords(studyId);
        return new ResponseResult<>(200, "OK", result);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/checkValidCode")
    public ResponseResult<?> checkValidCode(String studyId, String batchNum, String code) {
        String result = rtsmService.checkValidCode(studyId, batchNum, code);
        return new ResponseResult<>(200, "OK", result);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getValidCode")
    public ResponseResult<?> getValidCode(String studyId, String batchNum, String rtsmAccountEmail, String accountName) {
        SASOnlieConstant.setRemoteServerApi("https://cdtms-tst.hengrui.com/");
        SASOnlieConstant.setRemoteServerApiPrefix("https://cdtms-tst.hengrui.com/");
        SASOnlieConstant.setCdtmsCheckLoginApi("https://cdtms-tst.hengrui.com/extdatabind.checktoken.do?token=");
        Map<String, String> sendInfo = rtsmService.getValidCode(studyId, batchNum, rtsmAccountEmail, accountName);
        return new ResponseResult<>(200, "OK", sendInfo);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/sendReviewEmail")
    @ResponseBody
    public ResponseResult<?> sendReviewEmail(@RequestBody String param, String studyId, String batchNum) {
        SASOnlieConstant.setRemoteServerApi("https://cdtms-tst.hengrui.com/");
        SASOnlieConstant.setRemoteServerApiPrefix("https://cdtms-tst.hengrui.com/");
        SASOnlieConstant.setCdtmsCheckLoginApi("https://cdtms-tst.hengrui.com/extdatabind.checktoken.do?token=");
        Map<String, String> maillInfo = rtsmService.sendReviewEmail(param, studyId, batchNum);
        return new ResponseResult<>(200, "OK", maillInfo);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getRTSMFileNames")
    @ResponseBody
    public Map<String, Object> getRTSMFileNames(String taskId,
                                                String server,
                                                String projectId) {
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        CDTMSAPI.updateWorkFlowStatus(taskId, projectId, "status", "05");
        rtsmService.getRTSMFileNames(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> output = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", output);
        return result;
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getRTSMFile")
    @ResponseBody
    public Map<String, Object> getRTSMFile(String taskId,
                                           String server,
                                           String projectId) {
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        CDTMSAPI.updateWorkFlowStatus(taskId, projectId, "crf_zt", "01");
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId="";
        String tableId="";
        if(!ObjectUtils.isEmpty(formInfo.get("recordId"))){
            recordId= formInfo.get("recordId");
            tableId=formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id",recordId);
            object.put("formData",formData);
            projectId=object.toJSONString();
        }else{
            tableId=projectId;
            recordId=taskId;
        }
        String studyId = formInfo.get("studyId");
        FileUtils.setEDCAPIUrl(studyId,server);
        rtsmService.getRTSMFile(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> output = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", output);
        return result;
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getRTSMMonitorReport")
    @ResponseBody
    public Map<String, Object> getRTSMMonitorReport(String taskId,
                                                    String server,
                                                    String projectId) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String recordId="";
        String tableId="";
        if(!ObjectUtils.isEmpty(formInfo.get("recordId"))){
            recordId= formInfo.get("recordId");
            tableId=formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id",recordId);
            object.put("formData",formData);
            projectId=object.toJSONString();
        }else{
            tableId=projectId;
            recordId=taskId;
        }
        String studyId = formInfo.get("studyId");
        FileUtils.setEDCAPIUrl(studyId,server);
        rtsmService.getRTSMMonitorReport(taskId, projectId);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> output = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", output);
        return result;
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/editFormInfo")
    @ResponseBody
    public ResponseResult<?> addFormInfo(@RequestBody String param, String studyId, String batchNum, String userName) {
        SASOnlieConstant.setRemoteServerApi("https://cdtms-tst.hengrui.com/");
        SASOnlieConstant.setRemoteServerApiPrefix("https://cdtms-tst.hengrui.com/");
        SASOnlieConstant.setCdtmsCheckLoginApi("https://cdtms-tst.hengrui.com/extdatabind.checktoken.do?token=");
        String result = rtsmService.editFormInfo(param, studyId, batchNum, userName);
        return new ResponseResult<>(200, "OK", result);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/deleteBatchRecord")
    @ResponseBody
    public ResponseResult<?> deleteBatchRecord(String studyId, String batchNum, String userName) {
        String result = rtsmService.deleteBatchRecord(studyId, batchNum, userName);
        return new ResponseResult<>(200, "OK", result);
    }

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/forceSave")
    @ResponseBody
    public ResponseResult<?> forceSave(String fileKey, String userId) {
        String result = rtsmService.forceSave(fileKey, userId);
        return new ResponseResult<>(200, "OK", result);
    }

    //另存接口，studyId,batchNum,fileKey
    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/saveAsRecord")
    @ResponseBody
    public ResponseResult<?> saveAsRecord(String studyId, String userName, String filePath) {
        String result = rtsmService.saveAsRecord(studyId, userName, filePath);
        return new ResponseResult<>(200, "OK", result);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/ESignCallBack")
    @ResponseBody
    public Map<String, Object> RTSMESignCallBack(@RequestBody JSONObject param) {
        RTSMController.log.info("回填的拿到的参数是:" + param.toString());
        String result = "";
        result = rtsmService.RTSMESignCallBack(param);
        Map<String, Object> results = new HashMap<>();
        results.put("success", true);
        results.put("code", 200);
        results.put("msg", result);
        return results;
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/getEsignInfo")
    @ResponseBody
    public ResponseResult<?> getEsignInfo(@RequestBody EsignEntity param) {
        return rtsmService.getEsignInfo(param);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/resign")
    @ResponseBody
    public ResponseResult<?> resign(@RequestBody EsignEntity param) {
        return rtsmService.resign(param);
    }

    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/addSigners")
    @ResponseBody
    public ResponseResult<?> addSigners(@RequestBody EsignEntity param) {
        return rtsmService.addSigners(param);
    }

    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/deleteSigner")
    @ResponseBody
    public ResponseResult<?> deleteSigner(@RequestBody EsignEntity param) {
        return rtsmService.deleteSigner(param);
    }

    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/cancelSignTask")
    @ResponseBody
    public ResponseResult<?> cancelSignTask(@RequestBody EsignEntity param) {
        return rtsmService.cancelSignTask(param);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getApproveStatusByName")
    @ResponseBody
    public ResponseResult<?> getApproveStatusByName(String studyId, String batchNum, String userName) {
        String result = rtsmService.getApproveStatusByName(studyId, batchNum, userName);
        return new ResponseResult<>(200, "OK", result);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/resendNotification")
    @ResponseBody
    public ResponseResult<?> resendNotification(String taskId, String email, String userName) {
        return rtsmService.resendNotification(taskId,email,userName);
    }




    @CrossOrigin(origins = "*", maxAge = 3600)
    @PostMapping("/addAuditAndApprovalTable")
    @ResponseBody
    public ResponseResult<?> addAuditAndApprovalTable(@RequestBody String param, String studyId, String userName) {
        String result = rtsmService.addAuditAndApprovalTable(studyId, userName, param);
        if (result.equals("fail")) {
            return new ResponseResult<>(500, "internal error", result);
        } else {
            return new ResponseResult<>(200, "OK", result);
        }

    }



    @CrossOrigin(origins = "*", maxAge = 3600)
    // @PostMapping注解用于处理POST请求，路径为/sendAuditAndApprovalSign
    @PostMapping("/sendAuditAndApprovalSign")
    public ResponseResult<?> sendAuditAndApprovalSign(@RequestBody String param,String studyId, String fileKey, String userName) {
        return rtsmService.sendAuditAndApprovalSign( param,studyId, fileKey, userName);
    }



    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getAccouintManager")
    @ResponseBody
    public ResponseResult<?> getAccouintManager(String studyId) {
        Map<String,String> accountManagerInfo =CDTMSAPI.getRTSMAccountManagerEmail(studyId);
        if (accountManagerInfo == null || accountManagerInfo.get("email") == null || accountManagerInfo.get("email").isEmpty()) {
            return new ResponseResult<>(404, "ERROR", "Account manager not found");
        }

        return new ResponseResult<>(200, "OK", accountManagerInfo);
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getAASignFilePathByTaskId")
    public ResponseResult<?> getAASignFilePathByTaskId(String taskId) {
        Map<String, String> result = rtsmService.getAASignFilePathByTaskId(taskId);
        return new ResponseResult<>(200, "OK", result);
    }






}
