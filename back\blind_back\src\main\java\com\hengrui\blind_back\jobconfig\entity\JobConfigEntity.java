package com.hengrui.blind_back.jobconfig.entity;

public class JobConfigEntity {
    //corn字符
    private String corn;
    //开始日期
    private String startDate;
    //结束日期
    private String endDate;
    //时间段1
    private String hourPartOne;
    //时间段2
    private String hourPartTwo;
    //时间段3
    private String hourPartThree;
    //频率
    private String frequency;

    //sas核查程序名称



    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getHourPartOne() {
        return hourPartOne;
    }

    public void setHourPartOne(String hourPartOne) {
        this.hourPartOne = hourPartOne;
    }

    public String getHourPartTwo() {
        return hourPartTwo;
    }

    public void setHourPartTwo(String hourPartTwo) {
        this.hourPartTwo = hourPartTwo;
    }

    public String getHourPartThree() {
        return hourPartThree;
    }

    public void setHourPartThree(String hourPartThree) {
        this.hourPartThree = hourPartThree;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getCorn() {
        return corn;
    }

    public void setCorn(String corn) {
        this.corn = corn;
    }

    public JobConfigEntity() {
    }

    public JobConfigEntity(String startDate, String endDate, String hourPartOne, String frequency) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.hourPartOne = hourPartOne;
        this.frequency = frequency;
    }

    public JobConfigEntity(String startDate, String endDate, String hourPartOne, String hourPartTwo, String frequency) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.hourPartOne = hourPartOne;
        this.hourPartTwo = hourPartTwo;
        this.frequency = frequency;
    }

    public JobConfigEntity(String startDate, String endDate, String hourPartOne, String hourPartTwo, String hourPartThree, String frequency) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.hourPartOne = hourPartOne;
        this.hourPartTwo = hourPartTwo;
        this.hourPartThree = hourPartThree;
        this.frequency = frequency;
    }
}
