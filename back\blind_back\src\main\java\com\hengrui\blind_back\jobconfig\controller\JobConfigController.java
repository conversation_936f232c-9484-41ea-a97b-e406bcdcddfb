package com.hengrui.blind_back.jobconfig.controller;

import com.hengrui.blind_back.jobconfig.service.JobConfigService;
import com.hengrui.blind_back.utils.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

//*
// * 配置xxl-job的调度时间


@RestController
public class JobConfigController {
    @Autowired
    JobConfigService jobConfigService;


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/configSchedule")
    @ResponseBody
    public Map<String, Object> configSchedule(String taskId,
                                            String server,
                                            String projectId) {
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        String results = jobConfigService.configSchedule(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        return result;
    }

}
