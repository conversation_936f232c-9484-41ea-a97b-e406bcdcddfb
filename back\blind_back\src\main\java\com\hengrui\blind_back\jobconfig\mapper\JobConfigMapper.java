package com.hengrui.blind_back.jobconfig.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Component
@Mapper
@Repository
@DS("slave_1")
public interface JobConfigMapper {

    int configSchedule(String cron,String nodeName);

    int getJobId(String nodeName);
}
