package com.hengrui.blind_back.constant;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * @ClassName WhoCN
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/27 15:22
 * @Version 1.0
 **/
public class WhoCN {
    @ExcelProperty("No.")
    private Integer no;

    @ExcelProperty("Study ID")
    private String studyId;

    @ExcelProperty("Site ID")
    private String siteId;

    @ExcelProperty("Subject Code")
    private String subjectCode;

    @ExcelProperty("Visit Name")
    private String visitName;

    @ExcelProperty("Form Name")
    private String formName;

    @ExcelProperty("Sn")
    private String sn;

    @ExcelProperty("Verbatims")
    private String verbatims;

    @ExcelProperty("Verbatims Comments")
    private String verbatimsComments;

    @ExcelProperty("Indication")
    private String indication;

    @ExcelProperty("Route")
    private String route;

    @ExcelProperty("Drug Name_CN")
    private String drugNameCn;

    @ExcelProperty("Drug Name_EN")
    private String drugNameEn;

    @ExcelProperty("Drug Code")
    private String drugCode;

    @ExcelProperty("Preferred Name_CN")
    private String preferredNameCn;

    @ExcelProperty("Preferred Name_EN")
    private String preferredNameEn;

    @ExcelProperty("Preferred Code")
    private String preferredCode;

    @ExcelProperty("Preferred Base_CN")
    private String preferredBaseCn;

    @ExcelProperty("Preferred Base_EN")
    private String preferredBaseEn;

    @ExcelProperty("Preferred Base code")
    private String preferredBaseCode;

    @ExcelProperty("ATCCODE")
    private String atcCode;

    @ExcelProperty("ATC_CN")
    private String atcCn;

    @ExcelProperty("ATC_EN")
    private String atcEn;

    @ExcelProperty("ATC1CODE")
    private String atc1Code;

    @ExcelProperty("ATC1_CN")
    private String atc1Cn;

    @ExcelProperty("ATC1_EN")
    private String atc1En;

    @ExcelProperty("ATC2CODE")
    private String atc2Code;

    @ExcelProperty("ATC2_CN")
    private String atc2Cn;

    @ExcelProperty("ATC2_EN")
    private String atc2En;

    @ExcelProperty("ATC3CODE")
    private String atc3Code;

    @ExcelProperty("ATC3_CN")
    private String atc3Cn;

    @ExcelProperty("ATC3_EN")
    private String atc3En;

    @ExcelProperty("ATC4CODE")
    private String atc4Code;

    @ExcelProperty("ATC4_CN")
    private String atc4Cn;

    @ExcelProperty("ATC4_EN")
    private String atc4En;

    @ExcelProperty("Generic")
    private String generic;

    @ExcelProperty("Query")
    private String query;

    @ExcelProperty("Dictionary Name")
    private String dictionaryName;

    @ExcelProperty("Dictionary Version")
    private String dictionaryVersion;

    @ExcelProperty("Updated time")
    private String updatedTime;

    @ExcelProperty("Coding Method")
    private String codingMethod;

    // Getters and Setters
    public Integer getNo() { return no; }
    public void setNo(Integer no) { this.no = no; }
    public String getStudyId() { return studyId; }
    public void setStudyId(String studyId) { this.studyId = studyId; }
    public String getSiteId() { return siteId; }
    public void setSiteId(String siteId) { this.siteId = siteId; }
    public String getSubjectCode() { return subjectCode; }
    public void setSubjectCode(String subjectCode) { this.subjectCode = subjectCode; }
    public String getVisitName() { return visitName; }
    public void setVisitName(String visitName) { this.visitName = visitName; }
    public String getFormName() { return formName; }
    public void setFormName(String formName) { this.formName = formName; }
    public String getSn() { return sn; }
    public void setSn(String sn) { this.sn = sn; }
    public String getVerbatims() { return verbatims; }
    public void setVerbatims(String verbatims) { this.verbatims = verbatims; }
    public String getVerbatimsComments() { return verbatimsComments; }
    public void setVerbatimsComments(String verbatimsComments) { this.verbatimsComments = verbatimsComments; }
    public String getIndication() { return indication; }
    public void setIndication(String indication) { this.indication = indication; }
    public String getRoute() { return route; }
    public void setRoute(String route) { this.route = route; }
    public String getDrugNameCn() { return drugNameCn; }
    public void setDrugNameCn(String drugNameCn) { this.drugNameCn = drugNameCn; }
    public String getDrugNameEn() { return drugNameEn; }
    public void setDrugNameEn(String drugNameEn) { this.drugNameEn = drugNameEn; }
    public String getDrugCode() { return drugCode; }
    public void setDrugCode(String drugCode) { this.drugCode = drugCode; }
    public String getPreferredNameCn() { return preferredNameCn; }
    public void setPreferredNameCn(String preferredNameCn) { this.preferredNameCn = preferredNameCn; }
    public String getPreferredNameEn() { return preferredNameEn; }
    public void setPreferredNameEn(String preferredNameEn) { this.preferredNameEn = preferredNameEn; }
    public String getPreferredCode() { return preferredCode; }
    public void setPreferredCode(String preferredCode) { this.preferredCode = preferredCode; }
    public String getPreferredBaseCn() { return preferredBaseCn; }
    public void setPreferredBaseCn(String preferredBaseCn) { this.preferredBaseCn = preferredBaseCn; }
    public String getPreferredBaseEn() { return preferredBaseEn; }
    public void setPreferredBaseEn(String preferredBaseEn) { this.preferredBaseEn = preferredBaseEn; }
    public String getPreferredBaseCode() { return preferredBaseCode; }
    public void setPreferredBaseCode(String preferredBaseCode) { this.preferredBaseCode = preferredBaseCode; }
    public String getAtcCode() { return atcCode; }
    public void setAtcCode(String atcCode) { this.atcCode = atcCode; }
    public String getAtcCn() { return atcCn; }
    public void setAtcCn(String atcCn) { this.atcCn = atcCn; }
    public String getAtcEn() { return atcEn; }
    public void setAtcEn(String atcEn) { this.atcEn = atcEn; }
    public String getAtc1Code() { return atc1Code; }
    public void setAtc1Code(String atc1Code) { this.atc1Code = atc1Code; }
    public String getAtc1Cn() { return atc1Cn; }
    public void setAtc1Cn(String atc1Cn) { this.atc1Cn = atc1Cn; }
    public String getAtc1En() { return atc1En; }
    public void setAtc1En(String atc1En) { this.atc1En = atc1En; }
    public String getAtc2Code() { return atc2Code; }
    public void setAtc2Code(String atc2Code) { this.atc2Code = atc2Code; }
    public String getAtc2Cn() { return atc2Cn; }
    public void setAtc2Cn(String atc2Cn) { this.atc2Cn = atc2Cn; }
    public String getAtc2En() { return atc2En; }
    public void setAtc2En(String atc2En) { this.atc2En = atc2En; }
    public String getAtc3Code() { return atc3Code; }
    public void setAtc3Code(String atc3Code) { this.atc3Code = atc3Code; }
    public String getAtc3Cn() { return atc3Cn; }
    public void setAtc3Cn(String atc3Cn) { this.atc3Cn = atc3Cn; }
    public String getAtc3En() { return atc3En; }
    public void setAtc3En(String atc3En) { this.atc3En = atc3En; }
    public String getAtc4Code() { return atc4Code; }
    public void setAtc4Code(String atc4Code) { this.atc4Code = atc4Code; }
    public String getAtc4Cn() { return atc4Cn; }
    public void setAtc4Cn(String atc4Cn) { this.atc4Cn = atc4Cn; }
    public String getAtc4En() { return atc4En; }
    public void setAtc4En(String atc4En) { this.atc4En = atc4En; }
    public String getGeneric() { return generic; }
    public void setGeneric(String generic) { this.generic = generic; }
    public String getQuery() { return query; }
    public void setQuery(String query) { this.query = query; }
    public String getDictionaryName() { return dictionaryName; }
    public void setDictionaryName(String dictionaryName) { this.dictionaryName = dictionaryName; }
    public String getDictionaryVersion() { return dictionaryVersion; }
    public void setDictionaryVersion(String dictionaryVersion) { this.dictionaryVersion = dictionaryVersion; }
    public String getUpdatedTime() { return updatedTime; }
    public void setUpdatedTime(String updatedTime) { this.updatedTime = updatedTime; }
    public String getCodingMethod() { return codingMethod; }
    public void setCodingMethod(String codingMethod) { this.codingMethod = codingMethod; }

    @Override
    public String toString() {
        return "CsvDataModel{" +
                "no=" + no +
                ", studyId='" + studyId + '\'' +
                ", siteId='" + siteId + '\'' +
                ", subjectCode='" + subjectCode + '\'' +
                ", visitName='" + visitName + '\'' +
                ", formName='" + formName + '\'' +
                ", sn='" + sn + '\'' +
                ", verbatims='" + verbatims + '\'' +
                ", verbatimsComments='" + verbatimsComments + '\'' +
                ", indication='" + indication + '\'' +
                ", route='" + route + '\'' +
                ", drugNameCn='" + drugNameCn + '\'' +
                ", drugNameEn='" + drugNameEn + '\'' +
                ", drugCode='" + drugCode + '\'' +
                ", preferredNameCn='" + preferredNameCn + '\'' +
                ", preferredNameEn='" + preferredNameEn + '\'' +
                ", preferredCode='" + preferredCode + '\'' +
                ", preferredBaseCn='" + preferredBaseCn + '\'' +
                ", preferredBaseEn='" + preferredBaseEn + '\'' +
                ", preferredBaseCode='" + preferredBaseCode + '\'' +
                ", atcCode='" + atcCode + '\'' +
                ", atcCn='" + atcCn + '\'' +
                ", atcEn='" + atcEn + '\'' +
                ", atc1Code='" + atc1Code + '\'' +
                ", atc1Cn='" + atc1Cn + '\'' +
                ", atc1En='" + atc1En + '\'' +
                ", atc2Code='" + atc2Code + '\'' +
                ", atc2Cn='" + atc2Cn + '\'' +
                ", atc2En='" + atc2En + '\'' +
                ", atc3Code='" + atc3Code + '\'' +
                ", atc3Cn='" + atc3Cn + '\'' +
                ", atc3En='" + atc3En + '\'' +
                ", atc4Code='" + atc4Code + '\'' +
                ", atc4Cn='" + atc4Cn + '\'' +
                ", atc4En='" + atc4En + '\'' +
                ", generic='" + generic + '\'' +
                ", query='" + query + '\'' +
                ", dictionaryName='" + dictionaryName + '\'' +
                ", dictionaryVersion='" + dictionaryVersion + '\'' +
                ", updatedTime='" + updatedTime + '\'' +
                ", codingMethod='" + codingMethod + '\'' +
                '}';
    }
}
