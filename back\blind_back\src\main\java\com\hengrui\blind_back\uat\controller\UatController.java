package com.hengrui.blind_back.uat.controller;

import com.hengrui.blind_back.uat.service.UATService;
import com.hengrui.blind_back.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName UatController
 * @Description pass the parameter to call SAS
 * <AUTHOR>
 * @Date 2024/5/22 9:46
 * @Version 1.0
 **/
@RestController
@Slf4j
public class UatController {


    @Autowired
    UATService uatService;

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/submitToUATSAS")

    public Map<String, Object> submitToUATSAS(String taskId,
                                              String server,
                                              String projectId) {
        UatController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> results = uatService.submitToUATSAS(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", results);
        return result;
    }

}
