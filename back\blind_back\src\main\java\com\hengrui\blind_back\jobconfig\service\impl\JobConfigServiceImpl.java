package com.hengrui.blind_back.jobconfig.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hengrui.blind_back.blind.mapper.BlindBackMapper;
import com.hengrui.blind_back.blind.utils.ULIDGenerator;
import com.hengrui.blind_back.jobconfig.mapper.JobConfigMapper;
import com.hengrui.blind_back.jobconfig.service.JobConfigService;
import com.hengrui.blind_back.utils.CDTMSAPI;
import com.hengrui.blind_back.utils.XxlJobUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Map;

@Service
@Slf4j
public class JobConfigServiceImpl implements JobConfigService {

    @Autowired
    JobConfigMapper jobConfigMapper;

    @Autowired
    BlindBackMapper blindBackMapper;


    @Autowired
    XxlJobUtil xxlJobUtil;

    @Override
    public String configSchedule(String taskId, String projectId) {
        //1.获取配置页面的参数，包含调1.调度信息：开始时间，调度频率（存到调度表）  2.节点信息：调度节点，节点参数（存到业务表）
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        if (!ObjectUtils.isEmpty(formInfo.get("param")) && !formInfo.get("param").isEmpty()) {
            JSONObject formData = JSON.parseObject(formInfo.get("param"));
            String id = ULIDGenerator.generateULID();
            String userId = formData.get("userid").toString();
            String nodeName = formData.get("checkarea").toString();
            String version = formData.get("version_zt").toString();
            String studyId = formData.get("studyid").toString();
            //运行频率：1.周/1次 2.2周/1次 3.1月/次 4.3月/1次 5.自定义
            String batchrun = formData.get("batchrun").toString();
            JobConfigServiceImpl.log.info("-----------------------------------------------batchrun:{}", batchrun);
            if (!batchrun.isEmpty()) {
                String cornTag = batchrun;
                switch (cornTag) {
                    case "qw":
                        batchrun = "0 0 0 ? * THU";
                        break;
                    case "q2w":
                        batchrun = "0 0 0 1,15 * ?";
                        break;
                    case "qm":
                        batchrun = "0 0 1 1 * ?";
                        break;
                    case "q3m":
                        batchrun = "0 0 0 1 1/3 ?";
                        break;
                    case "PRN":
                        batchrun = formData.get("corn").toString();
                        break;
                    default:
                        break;

                }

                //更新调度频率到xxl_job对应的调度任务中
                if (nodeName.contains("定期审核")) {
                    JSONObject json = new JSONObject();
                    json.put("reviewer_name", "System");
                    json.put("zq", 6);
                    if(nodeName.contains("账号审核")){
                        json.put("type", "1");
                    }else if(nodeName.contains("质疑管理")){
                        json.put("type", "2");
                    }else if(nodeName.contains("稽查轨迹")){
                        json.put("type", "3");
                    }
                    //存到调度业务节点记录表中,方便对应的执行器进行调度
                    blindBackMapper.addScheduleRecord(id, userId, nodeName, version, studyId, batchrun, json.toJSONString(),cornTag);
                    //更新定期审核对应的调度频率
                    jobConfigMapper.configSchedule(batchrun, nodeName);
                    //获取该调度的id,触发式更新
                    int susarJobId = jobConfigMapper.getJobId(nodeName);
                    xxlJobUtil.pause(susarJobId);
                    xxlJobUtil.start(susarJobId);
                }
                return "success";
            } else {
                return "fail";
            }
        }
        return "fail";

    }


}
