package com.hengrui.blind_back.ecrf_transfer.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

public interface ECRFTransferService {
    Map<String, Object> getECRFTransferFile(String taskId, String projectId);

    Map<String, String> ECRFOnlineApproval(String taskId, String projectId);

    Map<String, String> ECRFMatcheByList(String taskId, String projectId);


}
