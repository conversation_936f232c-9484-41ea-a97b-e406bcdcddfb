-- 编盲文件数据表
CREATE TABLE ex_csv_store_data (
                                   id VARCHAR ( 255 ) NOT NULL,
                                   file_id VARCHAR ( 255 ) NOT NULL,
                                   var1 VARCHAR ( 255 ),
                                   var2 VARCHAR ( 255 ),
                                   var3 VARCHAR ( 255 ),
                                   var4 VARCHAR ( 255 ),
                                   var5 VARCHAR ( 255 ),
                                   var6 VARCHAR ( 255 ),
                                   var7 VARCHAR ( 255 ),
                                   var8 VARCHAR ( 255 ),
                                   var9 VARCHAR ( 255 ),
                                   var10 VARCHAR ( 255 ),
                                   var11 VARCHAR ( 255 ),
                                   var12 VARCHAR ( 255 ),
                                   var13 VARCHAR ( 255 ),
                                   var14 VARCHAR ( 255 ),
                                   var15 VARCHAR ( 255 ),
                                   var16 VARCHAR ( 255 ),
                                   var17 VARCHAR ( 255 ),
                                   var18 VARCHAR ( 255 ),
                                   var19 VARCHAR ( 255 ),
                                   var20 VARCHAR ( 255 ),
                                   var21 VARCHAR ( 255 ),
                                   var22 VARCHAR ( 255 ),
                                   var23 VARCHAR ( 255 ),
                                   var24 VARCHAR ( 255 ),
                                   var25 VARCHAR ( 255 ),
                                   var26 VARCHAR ( 255 ),
                                   var27 VARCHAR ( 255 ),
                                   var28 VARCHAR ( 255 ),
                                   var29 VARCHAR ( 255 ),
                                   var30 VARCHAR ( 255 ),
                                   var31 VARCHAR ( 255 ),
                                   var32 VARCHAR ( 255 ),
                                   var33 VARCHAR ( 255 ),
                                   var34 VARCHAR ( 255 ),
                                   var35 VARCHAR ( 255 ),
                                   var36 VARCHAR ( 255 ),
                                   var37 VARCHAR ( 255 ),
                                   var38 VARCHAR ( 255 ),
                                   var39 VARCHAR ( 255 ),
                                   var40 VARCHAR ( 255 ),
                                   var41 VARCHAR ( 255 ),
                                   var42 VARCHAR ( 255 ),
                                   var43 VARCHAR ( 255 ),
                                   var44 VARCHAR ( 255 ),
                                   var45 VARCHAR ( 255 ),
                                   var46 VARCHAR ( 255 ),
                                   var47 VARCHAR ( 255 ),
                                   var48 VARCHAR ( 255 ),
                                   var49 VARCHAR ( 255 ),
                                   var50 VARCHAR ( 255 ),
                                   create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                   row_seq INT  NOT NULL COMMENT '行数据顺序',
                                   PRIMARY KEY ( id )
);
-- 编盲数据列标题记录表
CREATE TABLE ex_csv_col (
                            id VARCHAR ( 255 ) NOT NULL,
                            file_id VARCHAR ( 255 ) NOT NULL,
                            map_seq VARCHAR ( 255 ),
                            map_val VARCHAR ( 255 ),
                            create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            seq int not null COMMENT '表头顺序',
                            PRIMARY KEY ( id )
);

CREATE TABLE ex_csv_blind_record (
                                     id VARCHAR ( 255 ) NOT NULL,
                                     uuid VARCHAR ( 255 ) NOT NULL,
                                     file_id VARCHAR ( 255 ) NOT NULL,
                                     blind_content text,
                                     file_path  VARCHAR ( 255 ) NOT NULL,
                                     create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                     PRIMARY KEY ( id )
);

-- 编盲操作记录表
CREATE TABLE ex_blind_operation_record (
                                           id VARCHAR ( 255 ) NOT NULL,
                                           file_id VARCHAR ( 255 ) NOT NULL,
                                           blind_records text,
                                           user_id VARCHAR ( 255 ) NOT NULL,
                                           create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                           project_name VARCHAR ( 255 ) NOT NULL,
                                           file_name VARCHAR ( 255 ) NOT NULL,
                                           role VARCHAR ( 255 ) NOT NULL,
                                           is_approve char (1)  NOT NULL,
                                           PRIMARY KEY ( id )
);

ALTER TABLE ex_blind_operation_record ADD COLUMN qc_user_id  VARCHAR ( 255 ) NULL ;

ALTER TABLE ex_blind_operation_record ADD COLUMN update_time TIMESTAMP NULL ;

ALTER TABLE ex_blind_operation_record ADD COLUMN original_file_id VARCHAR ( 255 ) NULL ;

ALTER TABLE ex_blind_operation_record ADD COLUMN file_path VARCHAR ( 255 ) NULL ;

ALTER TABLE ex_blind_operation_record ADD COLUMN operate_name VARCHAR ( 255 ) NULL ;

-- 编盲原始文件记录表
CREATE TABLE ex_blind_original_file_record (
                                               id VARCHAR ( 255 ) NOT NULL,
                                               project_name VARCHAR ( 255 ) NOT NULL,
                                               file_name VARCHAR ( 255 ) NOT NULL,
                                               file_path VARCHAR ( 255 ) NOT NULL,
                                               create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                               PRIMARY KEY ( id )
);

ALTER TABLE ex_blind_original_file_record ADD COLUMN update_time TIMESTAMP  DEFAULT CURRENT_TIMESTAMP ;
ALTER TABLE ex_blind_operation_record ADD COLUMN ex_data_id VARCHAR ( 255 ) NULL ;
ALTER TABLE ex_blind_operation_record ADD COLUMN sas_log_path VARCHAR ( 255 ) NULL ;
ALTER TABLE ex_blind_operation_record ADD COLUMN blind_status CHAR ( 1 ) NULL;
ALTER TABLE ex_blind_operation_record ADD COLUMN data_type VARCHAR ( 255 ) NULL;
ALTER TABLE ex_blind_operation_record ADD COLUMN operate_name VARCHAR ( 255 ) NULL;
ALTER TABLE ex_blind_operation_record ADD COLUMN compare_file_path text NULL;
ALTER TABLE ex_blind_operation_record ADD COLUMN demand text NULL;
ALTER TABLE ex_blind_operation_record ADD COLUMN project_member text NULL;

-- 邮件发送记录表
CREATE TABLE ex_blind_email_record (
                                       id VARCHAR ( 255 ) NOT NULL,
                                       user_name VARCHAR(255) not null,
                                       project_name VARCHAR ( 255 ) NOT NULL,
                                       file_name VARCHAR ( 255 ) NOT NULL,
                                       file_id VARCHAR ( 255 ) NOT NULL,
                                       original_file_id VARCHAR(255) not NULL,
                                       receive_emails VARCHAR(255) not null,
                                       create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                       type char(1) not null,
                                       PRIMARY KEY ( id )
);
ALTER TABLE ex_blind_operation_record ADD COLUMN blind_member_with_mail TEXT;
ALTER TABLE ex_blind_email_record ADD COLUMN ex_data_id VARCHAR ( 255 ) NULL ;
ALTER TABLE ex_blind_email_record ADD COLUMN qc_comments VARCHAR ( 255 ) NULL ;


CREATE TABLE ex_sso ( token VARCHAR ( 255 ) NULL, create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, is_expired CHAR ( 1 ) NOT NULL DEFAULT '1' );



--ecrf 报告解锁操作记录表
-- 编盲操作记录表
CREATE TABLE operation_records (
                                           id VARCHAR ( 255 ) NOT NULL,
                                           file_id VARCHAR ( 255 ) NOT NULL,
                                           unlock_records text,
                                           user_id VARCHAR ( 255 ) NOT NULL,
                                           create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                           study_name VARCHAR ( 255 ) NOT NULL,
                                           file_name VARCHAR ( 255 ) NOT NULL,
                                           role VARCHAR ( 255 ) NOT NULL,
                                           PRIMARY KEY ( id )
);

--rave 数据集 密码存储表
DROP TABLE IF EXISTS `data_set_pass`;
CREATE TABLE `data_set_pass`  (
                                  `id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                  `token` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                  `user_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                  `role` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                  `project_id` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NULL DEFAULT NULL,
                                  `PASSWORD` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NULL DEFAULT NULL,
                                  `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP,
                                  `update_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0),
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_unicode_ci ROW_FORMAT = Dynamic;


--rave 数据集 上传记录表
DROP TABLE IF EXISTS `rave_data_set_upload_record`;
CREATE TABLE `rave_data_set_upload_record`  (
                                                `id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                                `file_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                                `user_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                                `role` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
                                                `project_id` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NULL DEFAULT NULL,
                                                `file_path` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NULL DEFAULT NULL,
                                                `minio_path` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NULL DEFAULT NULL,
                                                `create_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP,
                                                `update_time` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0),
                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_unicode_ci ROW_FORMAT = Dynamic;

-- 随机申请表记录
CREATE TABLE rtsm_apply (
                            id VARCHAR ( 255 ) NOT NULL,
                            user_id VARCHAR ( 255 ) NOT NULL,
                            user_name VARCHAR ( 255 ) NOT NULL,
                            file_name VARCHAR ( 255 ) NOT NULL,
                            file_key VARCHAR ( 255 ) NOT NULL,
                            create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            study_name VARCHAR ( 255 ) NOT NULL,
                            remark VARCHAR ( 255 ) NOT NULL,
                            PRIMARY KEY ( id )
);

ALTER TABLE rtsm_apply ADD COLUMN is_approve CHAR ( 1 ) DEFAULT 'N' ;
ALTER TABLE rtsm_apply ADD COLUMN reviewer VARCHAR ( 255 ) NULL ;
ALTER TABLE rtsm_apply ADD COLUMN update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE rtsm_apply ADD COLUMN batch_num VARCHAR ( 255 ) NOT NULL ;
ALTER TABLE rtsm_apply ADD COLUMN valid_code VARCHAR ( 255 )  NULL;
ALTER TABLE rtsm_apply ADD COLUMN valid_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE rtsm_apply ADD COLUMN version_num VARCHAR ( 255 )  NULL;
ALTER TABLE rtsm_apply ADD COLUMN apply_date VARCHAR ( 255 )  NULL;
ALTER TABLE rtsm_apply ADD COLUMN is_deleted CHAR ( 1 )  DEFAULT 'N';
ALTER TABLE rtsm_apply  MODIFY remark text not  NULL;
ALTER TABLE rtsm_apply ADD COLUMN sign_file_path VARCHAR ( 255 )  NULL;


--调度业务节点记录表
CREATE TABLE `schedule_node_info` (
                                      id VARCHAR ( 255 ) NOT NULL,
                                      user_id VARCHAR ( 255 ) NOT NULL,
                                      node_name VARCHAR ( 255 ) NOT NULL,
                                      cron VARCHAR ( 255 ) NOT NULL,
                                      version VARCHAR ( 255 ) NOT NULL,
                                      studyId VARCHAR ( 255 ) NOT NULL,
                                      param VARCHAR ( 255 ) NOT NULL,
                                      create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                      update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                      remark VARCHAR ( 255 ) NOT NULL,
                                      PRIMARY KEY ( id )
)


--随机申请表统计师信息表
CREATE TABLE rtsm_apply_reviewer (
                                     id VARCHAR ( 255 ) NOT NULL,
                                     name VARCHAR ( 255 ) NOT NULL,
                                     email VARCHAR ( 255 ) NOT NULL,
                                     rtsm_apply_id varchar(255) NOT NULL,
                                     create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                     update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                     PRIMARY KEY ( id )
)


-- 随机审批表审核表记录
CREATE TABLE rtsm_audit_approval (
                                     id VARCHAR ( 255 ) NOT NULL,
                                     user_name VARCHAR ( 255 ) NOT NULL,
                                     file_name VARCHAR ( 255 ) NOT NULL,
                                     file_key VARCHAR ( 255 ) NOT NULL,
                                     create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                     update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                     study_name VARCHAR ( 255 ) NOT NULL,
                                     is_approve CHAR ( 1 ) DEFAULT 'N' ,
                                     reviewer VARCHAR ( 255 ) NULL ,
                                     remark VARCHAR ( 255 ) NOT NULL,
                                     batch_num VARCHAR ( 255 ) NOT NULL,
                                     valid_code VARCHAR ( 255 )  NULL,
                                     valid_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                     version_num VARCHAR ( 255 )  NULL,
                                     is_deleted CHAR ( 1 )  DEFAULT 'N',
                                     sign_file_path VARCHAR ( 255 )  NULL,
                                     PRIMARY KEY ( id )
);




