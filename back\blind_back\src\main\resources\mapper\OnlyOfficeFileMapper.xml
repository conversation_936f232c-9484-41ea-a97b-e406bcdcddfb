<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengrui.blind_back.onlyOffice.mapper.OnlyOfficeFileMapper">
   <update id="insertApplyEditRwecord" parameterType="string">
       update rtsm_apply set user_id=#{userId},user_name=#{userName},file_name=#{fileName},file_key=#{key},update_time=CURRENT_TIMESTAMP where batch_num=#{batchNum} and study_name=#{studyId};
   </update>

    <insert id="insertApplyInfo" parameterType="string">
        insert into rtsm_apply
        (id,user_id,user_name,file_name,file_key,study_name,batch_num,reviewer,version_num,remark,apply_date)values(#{id},#{id},#{userName},#{fileName},#{id},#{studyId},#{id},#{accountName},#{versionNum},#{param},#{applyDate})
    </insert>


    <insert id="insertAuditApproval" parameterType="string">
        insert into rtsm_audit_approval
        (id,user_name,file_name,file_key,study_name,batch_num,reviewer,version_num,remark)values(#{id},#{userName},#{fileName},#{id},#{studyId},#{id},#{accountName},#{versionNum},#{param})
    </insert>


    <insert id="insertApplyReviewers" parameterType="string">
        insert into rtsm_apply_reviewer
        (id,rtsm_apply_id,name,email)values(#{reviewerId},#{uuid},#{accountName},#{accountEmail})
    </insert>

    <insert id="insertAAReviewers" parameterType="string">
        insert into rtsm_audit_approval_reviewer
            (id,rtsm_apply_id,name,email)values(#{reviewerId},#{uuid},#{accountName},#{accountEmail})
    </insert>

    <select id="getApplyInfo" parameterType="string" resultType="map">
        select user_id,user_name,file_name,file_key,is_approve, batch_num,remark,is_signed ,version_num, DATE_FORMAT( create_time, '%Y-%m-%d' ) AS apply_date  from rtsm_apply where study_name = #{studyId} and batch_num=#{batchNum} and is_deleted='N'  order by  create_time desc limit 1;
    </select>

    <select id="getAuditApprovalInfo" parameterType="string" resultType="map">
        select user_name,file_name,file_key,is_approve, batch_num,remark,is_signed ,version_num from rtsm_audit_approval where study_name = #{studyId} and batch_num=#{batchNum} and is_deleted='N'  order by  create_time desc limit 1;
    </select>

    <select id="getApplyInfoByVersion" parameterType="string" resultType="int">
        select count(1) from rtsm_apply  where study_name = #{studyId} and file_key = #{fileKey};
    </select>


    <select id="checkPDFFileIsExist" parameterType="string" resultType="int">
        select count(1) from rtsm_apply  where study_name = #{studyId} and file_key = #{fileKey} and sign_file_path is not null and is_approve = 'Y';
    </select>

    <select id="checkAAPDFFileIsExist" parameterType="string" resultType="int">
        select count(1) from rtsm_audit_approval  where study_name = #{studyId} and file_key = #{fileKey} and sign_file_path is not null and is_approve = 'Y';
    </select>

    <update id="updateApproveStatus" parameterType="string" >
        update rtsm_apply set is_approve = 'Y',reviewer=#{userName},version_num=#{versionNum},sign_file_path=#{signFilePath}, update_time=CURRENT_TIMESTAMP where study_name = #{studyId} and file_key = #{fileKey};
    </update>

    <update id="updateAASignFilePath" parameterType="string" >
        update rtsm_audit_approval set sign_file_path=#{signFilePath}, update_time=CURRENT_TIMESTAMP where study_name = #{studyId} and file_key = #{fileKey};
    </update>

    <update id="updateFormInfo" parameterType="string">
        update rtsm_apply set remark=#{formInfo},update_time=CURRENT_TIMESTAMP,user_name=#{userName} where  study_name = #{studyId} and batch_num=#{batchNum};
    </update>

    <update id="deleteBatchRecord" parameterType="string">
        update rtsm_apply set is_deleted='Y',update_time=CURRENT_TIMESTAMP,user_name=#{userName} where study_name = #{studyId} and batch_num=#{batchNum};
    </update>

    <select id="getApprovedFilePath" parameterType="string" resultType="string">
        select
            file_name
        from
            rtsm_apply
        where
            study_name = #{studyId}
          and file_key =#{fileKey}
    </select>

    <select id="getAAApprovedFilePath" parameterType="string" resultType="string">
        select
            file_name
        from
            rtsm_audit_approval
        where
            study_name = #{studyId}
          and file_key =#{fileKey}
    </select>

    <select id="getBatchRecords" parameterType="string" resultType="map">
        SELECT
            any_value ( rtsm_apply.user_id ) AS user_id,
            any_value ( rtsm_apply.user_name ) AS user_name,
            any_value ( rtsm_apply.file_name ) AS file_name,
            any_value ( rtsm_apply.file_key ) AS file_key,
            any_value ( rtsm_apply.is_approve ) AS is_approve,
            any_value ( rtsm_apply.create_time ) AS create_time,
            any_value ( rtsm_apply.reviewer ) AS reviewer,
            any_value ( rtsm_apply.apply_date ) AS applyDate,
            any_value ( rtsm_apply.version_num ) AS versionNum,
            any_value ( rtsm_apply.is_deleted ) AS is_deleted,
            any_value ( rtsm_apply.remark ) AS form_param,
            CASE
                WHEN any_value ( rtsm_apply.is_approve ) = 'N' THEN
                    '审核中'
                WHEN any_value ( rtsm_apply.is_approve ) = 'Y' THEN
                    '已审核'
                END status,
            rtsm_apply.batch_num,
            CASE
                when any_value(esign_sign_tasks.status) = 4 then '已签署'
                else '未签署'
                END is_signed
        FROM
            rtsm_apply
                left join
            esign_sign_tasks on
                        rtsm_apply.study_name = esign_sign_tasks.study_id
                    and rtsm_apply.file_key = esign_sign_tasks.file_key
        where study_name = #{studyId} and batch_num is not null and batch_num != 'null'
        GROUP BY
            batch_num
        ORDER BY
            create_time DESC;
    </select>


    <select id="getAABatchRecords" parameterType="string" resultType="map">
        SELECT
            any_value ( rtsm_audit_approval.user_name ) AS user_name,
            any_value ( rtsm_audit_approval.file_name ) AS file_name,
            any_value ( rtsm_audit_approval.file_key ) AS file_key,
            any_value ( rtsm_audit_approval.create_time ) AS create_time,
            any_value ( rtsm_audit_approval.reviewer ) AS reviewer,
            any_value ( rtsm_audit_approval.version_num ) AS versionNum,
            any_value ( rtsm_audit_approval.is_deleted ) AS is_deleted,
            any_value ( rtsm_audit_approval.remark ) AS form_param,
            CASE
                WHEN any_value ( rtsm_audit_approval.is_approve ) = 'N' THEN
                    '审核中'
                WHEN any_value ( rtsm_audit_approval.is_approve ) = 'Y' THEN
                    '已审核'
                END status,
            rtsm_audit_approval.batch_num,
            CASE
                when any_value(esign_sign_tasks.status) = 4 then '已签署'
                else '未签署'
                END is_signed
        FROM
            rtsm_audit_approval
                left join
            esign_sign_tasks on
                        rtsm_audit_approval.study_name = esign_sign_tasks.study_id
                    and rtsm_audit_approval.file_key = esign_sign_tasks.file_key
        where study_name = #{studyId} and batch_num is not null and batch_num != 'null'
        GROUP BY
            batch_num
        ORDER BY
            create_time DESC;
    </select>

    <update id="updateValidCodeAndTime" parameterType="string">
        WITH target_apply AS (
            SELECT id
            FROM rtsm_apply
            WHERE batch_num = #{batchNum}
              AND study_name = #{studyId}
        )
        update rtsm_apply_reviewer set valid_code = #{code},valid_time=CURRENT_TIMESTAMP where rtsm_apply_id = (SELECT id FROM target_apply);
    </update>


    <update id="updateRTSMApplyMailInfo" parameterType="string">
        update rtsm_apply set sign_mail_subject = #{title},sign_mail_content=#{content},sign_mail_receivers=#{receivers} where file_key =  #{fileKey};
    </update>

    <update id="updateRTSMAAMailInfo" parameterType="string">
        update rtsm_audit_approval set sign_mail_subject = #{title},sign_mail_content=#{content},sign_mail_receivers=#{receivers} where file_key =  #{fileKey};
    </update>

    <select id="getValidCode" parameterType="string" resultType="map">
        WITH target_apply AS (
            SELECT id
            FROM rtsm_apply
            WHERE batch_num = #{batchNum}
              AND study_name = #{studyId}
        )
        select valid_code as validCode,valid_time as validTime  from rtsm_apply_reviewer where rtsm_apply_id = (SELECT id FROM target_apply)  and valid_code is not null ;
    </select>

    <select id="getFormInfo" parameterType="string" resultType="string">
        select remark from rtsm_apply where batch_num = #{batchNum} and remark is not null and is_deleted='N' order by update_time desc  limit 1;
    </select>



    <select id="getVersionNum" parameterType="string" resultType="int">
        select count(1) from(SELECT  distinct batch_num from rtsm_apply WHERE study_name= #{studyId}  and version_num like CONCAT(#{approveVersion},'%' )) result;
    </select>

    <select id="getAAVersionNum" parameterType="string" resultType="int">
        select count(1) from(SELECT  distinct batch_num from rtsm_audit_approval WHERE study_name= #{studyId}  and version_num like CONCAT(#{approveVersion},'%' )) result;
    </select>

    <select id="getVersionNumPrefix" parameterType="string" resultType="int">
        select count(1) from(SELECT  distinct batch_num from rtsm_apply WHERE study_name= #{studyId} and is_approve = 'Y') result;
    </select>

    <select id="getAAVersionNumPrefix" parameterType="string" resultType="int">
        select count(1) from(SELECT  distinct batch_num from rtsm_audit_approval WHERE study_name= #{studyId} and is_approve = 'Y') result;
    </select>

    <update id="updateApplyDate" parameterType="string" >
        update rtsm_apply set apply_date = #{applyDate} where study_name = #{studyId} and batch_num = #{batchNum};
    </update>

    <select id="getRecordByFileName" parameterType="string" resultType="map">
        select
            id,
            remark ,
            apply_date
        from
            rtsm_apply
        where
            study_name = #{studyId}
          and file_name = #{filePath}
    </select>

    <select id="getSignFilePath" parameterType="string" resultType="string">
        select
            sign_file_path
        from
            rtsm_apply
        where
            file_key = #{fileKey}
          and study_name =#{studyId}
    </select>



    <select id="getAASignFilePath" parameterType="string" resultType="string">
        select
            sign_file_path
        from
            rtsm_audit_approval
        where
            file_key = #{fileKey}
          and study_name =#{studyId}
    </select>

    <select id="getRecordParamByFileKey" parameterType="string" resultType="string">
        select
            remark
        from
            rtsm_apply
        where
            file_key = #{fileKey}
          and study_name = #{studyId}
    </select>

    <select id="getAARecordParamByFileKey" parameterType="string" resultType="string">
        select
            remark
        from
            rtsm_audit_approval
        where
            file_key = #{fileKey}
          and study_name = #{studyId}
    </select>

    <insert id="insertSignTask" parameterType="map">
        insert into esign_sign_tasks (task_id,file_id,file_code,created_by,study_id,expiration_date,email,file_key) values (#{taskId},#{fileId},#{fileCode},#{userName},#{studyId},#{expirationDate},#{email},#{fileKey})
    </insert>

    <insert id="insertSignUsers" parameterType="string">
        insert into esign_users (user_id,task_id,username,email,sign_reason,sign_url,sign_status) values (#{uuid},#{taskId},#{userName},#{email},#{signReason},#{signUrl},2)
    </insert>

    <update id="deleteSignUser" parameterType="string">
            update esign_users set is_deleted = 'Y' where task_id = #{taskId} and email = #{userEmail}
    </update>

    <select id="getSignerList" parameterType="string" resultType="map">
        select username,email from esign_users where task_id=#{taskId} and is_deleted = 'N'
    </select>

    <update id="updateSignerStatus" parameterType="map">
        update esign_users
        set sign_status = #{status},
        <if test="reason != null">
            sign_reason = #{reason},
        </if>
            updated_at=CURRENT_TIMESTAMP
        where task_id = #{taskId} and email = #{email} and is_deleted = 'N'
    </update>

    <update id="updateSignFileStatus" parameterType="map">
        update esign_sign_tasks
        set status = #{status},
        <if test="outputUrl != null and outputUrl != ''">
            output_url = #{outputUrl},
        </if>
        <if test="md5 != null and md5 != ''">
            md5 = #{md5},
        </if>
            sign_status = #{signStatus}
        where task_id = #{taskId};
    </update>


    <select id="getFileNameByTaskId" parameterType="string" resultType="string">
        SELECT
            f.file_name
        FROM
            esign_sign_files AS f
                INNER JOIN
            esign_sign_tasks AS t
            ON f.file_id = t.file_id
                AND t.task_id = #{taskId}
    </select>

    <select id="getTaskExpireDate" parameterType="string" resultType="string">
        select  DATE_FORMAT(expiration_date, '%Y-%m-%d %H:%i:%s') as expiration_date  from esign_sign_tasks where task_id = #{taskId}
    </select>

    <select id="getSingTimeByTaskId" parameterType="string" resultType="map">
        select 	DATE_FORMAT( updated_at, '%Y-%m-%d %H:%i:%s' ) AS signTime,
                  username AS name,
                  sign_url as signURL from esign_users where task_id=#{taskId} and email=#{email} and sign_status = 1 and is_deleted = 'N'
    </select>

    <select id="getAuthorEmailByTaskId" parameterType="string" resultType="map">
        select email,created_by as name,file_code as fileCode from esign_sign_tasks where task_id = #{taskId}
    </select>

    <insert id="insertSignFile" parameterType="string" >
        insert into esign_sign_files (file_id,file_name,file_path,file_hash,created_by,studyId,file_code) values (#{fileId},#{fileName},#{filePath},#{fileMd5},#{createBy},#{studyId},#{fileCode})
    </insert>

    <select id="getSignUrlByTaskId" parameterType="string" resultType="string">
        select sign_url from esign_users where task_id=#{taskId} and email = #{signerEmail} and is_deleted = 'N'
    </select>

    <update id="updateTaskStatus" parameterType="map" >
        update esign_sign_tasks set status = #{status},sign_status=#{signStatus} where task_id = #{taskId}
    </update>


    <update id="updateSingerStatus" parameterType="map">
        update esign_users set sign_status = #{status},updated_at=CURRENT_TIMESTAMP where task_id = #{taskId} and email = #{email} and is_deleted = 'N'
    </update>


    <select id="getEsignInfo" parameterType="com.hengrui.blind_back.rtsm.entity.EsignEntity" resultType="map">
        WITH base_data AS (
        SELECT
        est.task_id,
        est.md5 as signFileMd5,
        est.file_id,
        est.created_by as author,
        est.STATUS,
        DATE_FORMAT(est.created_at, '%Y-%m-%d %H:%i:%s') as createTime,
        DATE_FORMAT(est.updated_at, '%Y-%m-%d %H:%i:%s') as updateTime,
        eu.sign_status,
        DATE_FORMAT(est.expiration_date, '%Y-%m-%d %H:%i:%s') as expirationDate,
        DATE_FORMAT(eu.updated_at, '%Y-%m-%d %H:%i:%s') as SignTime,
        est.email as authorEmail,
        est.study_id,
        eu.username,
        eu.email as userEmail,
        eu.sign_reason,
        eu.sign_url,
        esf.file_name,
        esf.file_hash,
        esf.file_path,
        esf.file_code,
        COALESCE(ra.file_key, raa.file_key) as fileKey,
        est.output_url,
        est.cancel_name,
        est.cancel_email,
        IFNULL(DATE_FORMAT(est.cancel_time, '%Y-%m-%d %H:%i:%s'), '') as cancelTime,
        ROW_NUMBER() OVER (PARTITION BY est.task_id ORDER BY est.created_at DESC) as rn
        FROM esign_sign_tasks est
        LEFT JOIN esign_users eu ON est.task_id = eu.task_id
        LEFT JOIN esign_sign_files esf ON est.file_id = esf.file_id
        LEFT JOIN rtsm_apply ra ON ra.study_name = est.study_id
        AND ra.sign_file_path LIKE CONCAT('%', esf.file_name, '%')
        AND ra.file_key = est.file_key
        LEFT JOIN rtsm_audit_approval raa ON raa.study_name = est.study_id
        AND raa.sign_file_path LIKE CONCAT('%', esf.file_name, '%')
        AND raa.file_key = est.file_key
        WHERE (ra.file_key IS NOT NULL OR raa.file_key IS NOT NULL)
        AND eu.is_deleted = 'N'
        <if test="taskId != null and taskId != ''">
            AND est.task_id = #{taskId}
        </if>
        <if test="fileId != null and fileId != ''">
            AND est.file_id = #{fileId}
        </if>
        <if test="author != null and author != ''">
            AND est.created_by = #{author}
        </if>
        <if test="status != null">
            AND est.status = #{status}
        </if>
        <if test="signStatus != null">
            AND eu.sign_status = #{signStatus}
        </if>
        <if test="authorEmail != null and authorEmail != ''">
            AND est.email = #{authorEmail}
        </if>
        <if test="fileName != null and fileName != ''">
            AND esf.file_name = #{fileName}
        </if>
        <if test="fileCode != null and fileCode != ''">
            AND esf.file_code = #{fileCode}
        </if>
        <if test="userName != null and userName != ''">
            AND eu.username = #{userName}
        </if>
        <if test="userEmail != null and userEmail != ''">
            AND eu.email = #{userEmail}
        </if>
        <if test="signReason != null and signReason != ''">
            AND eu.sign_reason = #{signReason}
        </if>
        <if test="studyId != null and studyId != ''">
            AND est.study_id = #{studyId}
        </if>
        )
        SELECT
        task_id as taskId,
        signFileMd5,
        file_id as fileId,
        author,
        STATUS,
        createTime,
        updateTime,
        sign_status as signStatus,
        expirationDate,
        SignTime,
        authorEmail,
        study_id as studyId,
        username as userName,
        userEmail,
        sign_reason as signReason,
        sign_url as signUrl,
        file_name as fileName,
        file_hash as fileHash,
        file_path as filePath,
        file_code as fileCode,
        fileKey,
        output_url as outputUrl,
        cancel_name as cancelName,
        cancel_email as cancelEmail,
        cancelTime
        FROM base_data
        WHERE rn = 1
        ORDER BY createTime DESC
    </select>





    <select id="getAccountEmail" parameterType="string" resultType="map">
            WITH target_apply AS (
                SELECT id
                FROM rtsm_apply
                WHERE file_key = #{fileKey}
                AND study_name =#{studyId})
                    select email ,name  from rtsm_apply_reviewer where rtsm_apply_id = (SELECT id FROM target_apply)  ;
    </select>

    <insert id="addSendMailRecord" parameterType="string" >
        insert into esign_email_recipients (id,email,name,task_id,content,subject) values (#{uuid},#{email},#{name},#{taskId},#{content},#{subject})
    </insert>

    <select id="getMailInfoByTaskIdEmail" parameterType="string" resultType="map">
        select subject,content from esign_email_recipients where email=#{email} and task_id=#{taskId} and name=#{name} and subject like '%需要签署！%' order by created_at desc limit 1
    </select>

    <select id="getReviewersByRtsmApplyId" parameterType="string" resultType="map">
        SELECT name,email  FROM rtsm_apply_reviewer WHERE rtsm_apply_id = #{rtsmAaplyId}
    </select>

    <select id="judgeIsAllApprove" parameterType="string" resultType="int">
        WITH temp1 AS ( SELECT id FROM rtsm_apply WHERE file_key = #{fileKey} AND study_name = #{studyId}),
             temp2 AS (
                 SELECT
                     CASE

                         WHEN
                                 ( SELECT COUNT( 1 ) FROM rtsm_apply_reviewer WHERE rtsm_apply_id = ( SELECT id FROM temp1 ) ) = ( SELECT COUNT( 1 ) FROM rtsm_apply_reviewer WHERE rtsm_apply_id = ( SELECT id FROM temp1 ) AND is_approve = 'Y' ) THEN
                             TRUE ELSE FALSE
                         END AS all_approved
             ) SELECT
            all_approved
        FROM
            temp2;
    </select>

    <update id="updateReviewerApproveStatus" parameterType="string" >
        WITH target_apply AS (
            SELECT id
            FROM rtsm_apply
            WHERE file_key = #{fileKey}
              AND study_name = #{studyId}
        )
        UPDATE rtsm_apply_reviewer
        SET is_approve = 'Y'
        WHERE rtsm_apply_id = (SELECT id FROM target_apply)
    </update>


    <update id="updateCancelSign" parameterType="string">
        update esign_sign_tasks
        set cancel_name=#{userName},
            cancel_email=#{userEmail},
            status=3,
            cancel_time=CURRENT_TIMESTAMP
        where task_id = #{taskId}
    </update>

    <update id="updateRtsmApplySignStatus" parameterType="string">
        update rtsm_apply set is_signed = 'Y' , signed_file_path=#{signedFilePath} where file_key in ( select file_key from esign_sign_tasks where task_id = #{taskId} )
    </update>

    <update id="updateRtsmAuditApprovalSignStatus" parameterType="string">
        update rtsm_audit_approval set is_signed = 'Y' ,signed_file_path=#{signedFilePath} where file_key in ( select file_key from esign_sign_tasks where task_id = #{taskId} )
    </update>


    <select id="getRtsmAuditApprovalInfo" parameterType="string" resultType="map">
        select  study_name,remark from  rtsm_audit_approval where file_key in ( select file_key from esign_sign_tasks where task_id = #{taskId} )
    </select>



    <select id="getRtsmAccountInfo" parameterType="string" resultType="map">
        select  remark from  rtsm_apply where file_key in ( select file_key from esign_sign_tasks where task_id = #{taskId} )
    </select>
    
    <select id="getAccountSignEmailParams"  parameterType="string" resultType="map">
        select study_name,reviewer,batch_num from rtsm_apply where file_key = #{fileKey}
    </select>


    <select id="getAAAccountSignEmailParams"  parameterType="string" resultType="map">
        select study_name,batch_num from rtsm_audit_approval where file_key = #{fileKey}
    </select>

    <select id="getApproveStatusByName" parameterType="string" resultType="string">
        select
            is_approve
        from
            rtsm_apply_reviewer
        where
                rtsm_apply_id in (
                select
                    id
                from
                    rtsm_apply
                where
                    study_name = #{studyId}
                  and batch_num =  #{batchNum})
          and name = #{userName}
    </select>

    <select id="getSignPosition" parameterType="string" resultType="map">
        SELECT
            name_pos_x,
            name_pos_y,
            date_pos_x,
            date_pos_y
        FROM
            sign_position_config
        WHERE
            func_name = #{funcName}
          AND type = #{type}
    </select>

    <select id="getLatestVersion" parameterType="string" resultType="string">
        select  version_num from rtsm_apply WHERE study_name=#{studyId}  order  by create_time  desc limit 1;
    </select>

    <select id="getAALatestVersion" parameterType="string" resultType="string">
        select  version_num from rtsm_audit_approval WHERE study_name=#{studyId}  order  by create_time  desc limit 1;
    </select>

    <select id="getExpiringSignTasks" resultType="map">
        SELECT
            task_id AS taskId,
            expiration_date AS expirationDate,
            esign_sign_files.file_name AS fileName
        FROM
            esign_sign_tasks
                LEFT JOIN esign_sign_files ON esign_sign_tasks.file_id = esign_sign_files.file_id
        WHERE
            STATUS = 2
          AND expiration_date BETWEEN NOW( )
            AND DATE_ADD( NOW( ), INTERVAL 24 HOUR )
          AND NOT EXISTS ( SELECT 1 FROM rtsm_sign_notify WHERE task_id = esign_sign_tasks.task_id AND notify_type = 'expiring' )
    </select>


    <select id="getUnsignedSigners" parameterType="string" resultType="map">
        SELECT
            email,
            username,
            sign_url  as signUrl
            FROM
            esign_users
        WHERE
            task_id = #{taskId}
          and sign_status=2 and is_deleted = 'N'
    </select>

    <insert id="updateSignerNotifyStatus" parameterType="string">
        INSERT INTO rtsm_sign_notify (id, task_id, email, notify_type, notify_time) VALUES (#{uuid}, #{taskId}, #{email}, 'expiring', NOW())
    </insert>

    <select id="getMailInfoByFileName" parameterType="string" resultType="map">
            select sign_mail_subject as subject, sign_mail_content as content from rtsm_apply where sign_file_path like concat('%',#{fileName},'%')
            order by create_time desc   limit  1;
    </select>

    <select id="getAAMailInfoByFileName" parameterType="string" resultType="map">
        select sign_mail_subject as subject, sign_mail_content as content from rtsm_audit_approval where sign_file_path like concat('%',#{fileName},'%')
        order by create_time desc   limit  1;
    </select>


    <select id="getMailInfoByFileKey" parameterType="string" resultType="map">
        select sign_mail_subject as title, sign_mail_content as content,remark, sign_file_path as signFilePath from rtsm_apply where study_name=#{studyId} and file_key=#{fileKey}
    </select>

    <select id="getAAMailInfoByFileKey" parameterType="string" resultType="map">
        select sign_mail_subject as title, sign_mail_content as content,remark,sign_file_path as signFilePath from rtsm_audit_approval where study_name=#{studyId} and file_key=#{fileKey}
    </select>

    <select id="getSignerListByFileKey" parameterType="string" resultType="string">
        select sign_mail_receivers  from rtsm_apply where study_name=#{studyId} and file_key=#{fileKey}
    </select>

    <select id="getAASignerListByFileKey" parameterType="string" resultType="string">
        select sign_mail_receivers  from rtsm_audit_approval where study_name=#{studyId} and file_key=#{fileKey}
    </select>


    <update id="updateApproveStatusByFileKey" parameterType="string">
        update rtsm_apply set is_approve = 'N' where file_key = #{fileKey}
    </update>

    <update id="updateAAApproveStatusByFileKey" parameterType="string">
        update rtsm_audit_approval set is_approve = 'N' where file_key = #{fileKey}
    </update>

    <update id="updateAuditApprovalById" parameterType="string">
        update rtsm_audit_approval set file_name = #{fileName} where id = #{uuid}
    </update>

    <update id="updateAAApproveStatus" parameterType="string">
        update rtsm_audit_approval set is_approve = 'Y' where file_key = #{fileKey} and study_name = #{studyId}
    </update>

    <select id="getApplyFileSignStatusByName" parameterType="string" resultType="Integer">
        select
             status
        from
            esign_sign_tasks
        where
                file_key in (
                select
                    file_key
                from
                    rtsm_apply
                where
                    study_name = #{studyId}
                  and file_name  LIKE CONCAT('%', #{fileName}, '%') )
    </select>

    <select id="getApprovalFileSignStatusByName" parameterType="string" resultType="Integer">
        select
             status
        from
            esign_sign_tasks
        where
                file_key in (
                select
                    file_key
                from
                    rtsm_audit_approval
                where
                    study_name = #{studyId}
                  and file_name  LIKE CONCAT('%', #{fileName}, '%') )
    </select>


    <select id="getAASignFilePathByTaskId" parameterType="string" resultType="map">
        select
            sign_file_path ,signed_file_path
        from rtsm_audit_approval
        where
            file_key in (
                select
                    file_key
                from
                    esign_sign_tasks
                where
                    task_id = #{taskId} )
    </select>


    <select id="getSignFilePathByTaskId" parameterType="string" resultType="map">
        select
            sign_file_path ,signed_file_path
        from rtsm_apply
        where
            file_key in (
                select
                    file_key
                from
                    esign_sign_tasks
                where
                    task_id = #{taskId} )
    </select>

</mapper>
