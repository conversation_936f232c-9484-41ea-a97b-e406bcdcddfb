package com.hengrui.blind_back.ecrf_unlock.service;

import com.hengrui.blind_back.blind.entity.CSVTableDataEntity;

import java.util.Map;

public interface ECRFUnLockService {
    CSVTableDataEntity getCurrentData(String fileId);

    Map<String,String> submitToSASECRF(String param, String studyId, String userName, String role, String taskId);

    String uploadUnLockData(String taskId, String projectId, String fid);

    String getEDCName(String taskId,String projectId);
}
