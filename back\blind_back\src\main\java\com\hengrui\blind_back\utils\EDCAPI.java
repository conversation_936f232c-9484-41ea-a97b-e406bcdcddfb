package com.hengrui.blind_back.utils;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.hengrui.blind_back.blind.constant.BlindConstant;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.net.ssl.HttpsURLConnection;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName EDCAPI
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/20 13:47
 * @Version 1.0
 **/

@Slf4j
@Component
public class EDCAPI {


    public static String getToken(String userName, String pwd, String studyCode, String env) {
        EDCAPI.log.info("The request studyCode is: " + studyCode + " and env is: " + env);
        String token = "";
        String requestURL = SASOnlieConstant.EDC_API_PRFIX + "report/v4.1/app/getToken.do";
        try {
            URL url = new URL(requestURL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setDoOutput(true); // Set this before setting the request method
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Connection", "keep-alive");
            connection.setDoOutput(true);
            // Pass the parameters to the API
            String jsonStr = String.format("{\"userName\": \"%s\", \"pwd\": \"%s\", \"studyCode\": \"%s\", \"env\": \"%s\"}", userName, pwd, studyCode, env);
            EDCAPI.log.info("-----------------------------------the getToken params is : " + jsonStr);
//            String demoStr = "{\"userName\": \"<EMAIL>\", \"pwd\": \"Zh123456\", \"studyCode\": \"EDC-TEST-WTJ-01\", \"env\": \"pro\"}";
            // Write the request body
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonStr.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            int responseCode = connection.getResponseCode();
            String requestMethod = connection.getRequestMethod();
            EDCAPI.log.info("requestMethod is : " + requestMethod);
            EDCAPI.log.info("Response Code: " + responseCode);
            if (responseCode == HttpsURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                JSONObject jsonResponse = new JSONObject(response.toString());
                if (!jsonResponse.isEmpty() && !jsonResponse.toString().equals("true")) {
                    EDCAPI.log.info("----------------------------the getToken response is :" + jsonResponse.toString());
                    JSONObject data = new JSONObject(jsonResponse.get("data").toString());
                    token = data.get("token").toString();
                    EDCAPI.log.info("Token is: " + token);
                }

            } else {
                EDCAPI.log.info("POST request not worked");
                return "fail";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return token;
    }


    //获取中心名称，根据中心编号和受试者名称
    public static String getSiteName(String studyCode, String env, String siteId, String subjectName)  {
        String siteName="";
        //1.call getToken API
        String token = "";
        token = EDCAPI.getToken(SASOnlieConstant.EDC_API_USER, SASOnlieConstant.EDC_API_PASS, studyCode, env);
        if (token.isEmpty()) {
            EDCAPI.log.warn("------------------------------------------token is null !!!--------------------------------------------------");
        }
        //2.call getSubjectsInfo API
        EDCAPI.log.info("The request studyCode is: " + studyCode + " and env is: " + env + " and tid is : " + "SUBJECT");
        String requestURL = SASOnlieConstant.EDC_API_PRFIX + "report/v4.1/app/dataList.do";
        try {
            URL url = new URL(requestURL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setDoOutput(true); // Set this before setting the request method
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Connection", "keep-alive");
            connection.setRequestProperty("token", token);
            connection.setDoOutput(true);
            // Pass the parameters to the API
            String jsonStr="";
            jsonStr = String.format("{\"studyCode\": \"%s\", \"env\": \"%s\", \"tid\": \"%s\"}", studyCode, env, "SUBJECT");

            // Write the request body
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonStr.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            int responseCode = connection.getResponseCode();
            String requestMethod = connection.getRequestMethod();
            EDCAPI.log.info("requestMethod is : " + requestMethod);
            EDCAPI.log.info("Response Code: " + responseCode);
            if (responseCode == HttpsURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                JSONObject jsonResponse = new JSONObject(response.toString());
                JSONObject data = new JSONObject(jsonResponse.get("data").toString()).getJSONObject("SUBJECT");
                EDCAPI.log.info("------------------------------------------------------The data is: " + data);
                if (!data.isEmpty()) {
                    JSONArray subjectList = data.getJSONArray("list");
                        for (int i = 0; i < subjectList.size(); i++) {
                            JSONObject obj = subjectList.getJSONObject(i);
                            if (obj.get("SUBJID").toString().equals(subjectName)&&obj.get("SITECODE").toString().equals(siteId)) {
                                siteName= obj.get("SITENAME").toString();
                            }
                        }

                }

            } else {
                EDCAPI.log.info("POST request not worked");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return siteName;

    }



    public static JSONObject getSubjectsInfo(String studyCode, String env, String tid, Boolean isEarliest) {
        JSONObject subjectInfo = new JSONObject();
        //1.call getToken API
        String token = "";
        token = EDCAPI.getToken(SASOnlieConstant.EDC_API_USER, SASOnlieConstant.EDC_API_PASS, studyCode, env);
        if (token.isEmpty()) {
            EDCAPI.log.warn("------------------------------------------token is null !!!--------------------------------------------------");
        }
        //2.call getSubjectsInfo API
        EDCAPI.log.info("The request studyCode is: " + studyCode + " and env is: " + env + " and tid is : " + tid);
        String requestURL = SASOnlieConstant.EDC_API_PRFIX + "report/v4.1/app/dataList.do";
        try {
            URL url = new URL(requestURL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setDoOutput(true); // Set this before setting the request method
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Connection", "keep-alive");
            connection.setRequestProperty("token", token);
            connection.setDoOutput(true);
            // Pass the parameters to the API
            String jsonStr="";

            jsonStr = String.format("{\"studyCode\": \"%s\", \"env\": \"%s\", \"tid\": \"%s\", \"page\": \"%s\", \"size\": \"%s\"}", studyCode, env, tid,1,25000);


            // Write the request body
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonStr.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            int responseCode = connection.getResponseCode();
            String requestMethod = connection.getRequestMethod();
            EDCAPI.log.info("requestMethod is : " + requestMethod);
            EDCAPI.log.info("Response Code: " + responseCode);
            if (responseCode == HttpsURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                JSONObject jsonResponse = new JSONObject(response.toString());
                JSONObject data = new JSONObject(jsonResponse.get("data").toString()).getJSONObject(tid);
                EDCAPI.log.info("------------------------------------------------------The data is: " + data);
                if (!data.isEmpty()) {
                    JSONArray subjectList = data.getJSONArray("list");
                    if (subjectList.size()==25000){
                        subjectInfo.put("isFull",true);
                    }else{
                        subjectInfo.put("isFull",false);
                    }
                    subjectInfo.put("data", getSubjectInfo(subjectList, isEarliest,studyCode,env));

                }

            } else {
                EDCAPI.log.info("POST request not worked");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return subjectInfo;
    }

    public static JSONObject getSubjectInfo(JSONArray list, Boolean isEarliest,String studyCode,String env)  {
        // Convert JSONArray to List<JSONObject>
        List<JSONObject> jsonObjectList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            if(!(list.getJSONObject(i).getStr("VISDAT") == null) && !list.getJSONObject(i).getStr("VISDAT").isEmpty()){
                jsonObjectList.add(list.getJSONObject(i));
            }

        }
        SimpleDateFormat dateFormat;
        // Define a date format
        if(isEarliest){
            SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat format2 = new SimpleDateFormat("dd MMM yyyy", Locale.ENGLISH);
            // Sort the list based on CTIME
            // Sort the list based on VISDAT
            jsonObjectList.sort(Comparator.comparing(obj -> {
                String dateStr = obj.getStr("VISDAT");
                try {
                    // Try parsing with first format
                    return format1.parse(dateStr);
                } catch (Exception e1) {
                    try {
                        // If first format fails, try second format
                        return format2.parse(dateStr);
                    } catch (Exception e2) {
                        throw new RuntimeException("--------------------------------------Unable to parse date: " + dateStr, e2);
                    }
                }
            }));
        } else{
            SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat format2 = new SimpleDateFormat("dd MMM yyyy", Locale.ENGLISH);
            // Sort the list based on VISDAT
            // Sort the list based on VISDAT
            jsonObjectList.sort(Comparator.comparing(obj -> {
                String dateStr = obj.getStr("VISDAT");
                try {
                    // Try parsing with first format
                    return format1.parse(dateStr);
                } catch (Exception e1) {
                    try {
                        // If first format fails, try second format
                        return format2.parse(dateStr);
                    } catch (Exception e2) {
                        throw new RuntimeException("--------------------------------------Unable to parse date: " + dateStr, e2);
                    }
                }
            }));
        }


        if(isEarliest){
            // Retrieve the earliest and latest elements
            JSONObject earliestElement = jsonObjectList.get(0);
            if(jsonObjectList.size()>1){
                    // 使用正则表达式匹配数字部分
                    String numberPartA =  jsonObjectList.get(1).get("SUBJID").toString().replaceAll("\\D+", ""); // 去掉非数字字符
                     int i = Integer.parseInt(numberPartA);// 转换为整数
                    String numberPartB =  jsonObjectList.get(0).get("SUBJID").toString().replaceAll("\\D+", ""); // 去掉非数字字符
                     int j = Integer.parseInt(numberPartB);// 转换为整数
                   if(i<j&&jsonObjectList.get(0).get("VISDAT").toString().equals(jsonObjectList.get(1).get("VISDAT").toString())){
                       earliestElement = jsonObjectList.get(1);
                   }
            }

            //封装 json 对象
            JSONObject earsubjectInfo = new JSONObject();
            earsubjectInfo.put("center",earliestElement.get("SITECODE"));
            earsubjectInfo.put("subject_identification",earliestElement.get("SUBJID"));
            // Format the date to yyyy-MM-dd regardless of original format
            String dateStr = earliestElement.get("VISDAT").toString();
            Date date;
                try {
                    // Try first format (yyyy-MM-dd)
                    date = new SimpleDateFormat("yyyy-MM-dd").parse(dateStr);
                } catch (Exception e1) {
                    // Try second format (dd MMM yyyy)
                    try {
                        date = new SimpleDateFormat("dd MMM yyyy", Locale.ENGLISH).parse(dateStr);
                    } catch (ParseException e) {
                        throw new RuntimeException("--------------------------------------Unable to parse date: " + dateStr, e);
                    }
                }
                // Format to yyyy-MM-dd
                String formattedDate = new SimpleDateFormat("yyyy-MM-dd").format(date);
                earsubjectInfo.put("visit_date", formattedDate);

            String siteName = getSiteName(studyCode, env, earliestElement.get("SITECODE").toString(), earliestElement.get("SUBJID").toString());
            earsubjectInfo.put("site_name",siteName);
            return earsubjectInfo;
        }else{
            JSONObject latestElement = jsonObjectList.get(jsonObjectList.size() - 1);
            JSONObject latestsubjectInfo = new JSONObject();
            latestsubjectInfo.put("center",latestElement.get("SITECODE"));
            latestsubjectInfo.put("subject_identification",latestElement.get("SUBJID"));
            // Format the date to yyyy-MM-dd regardless of original format
            String dateStr = latestElement.get("VISDAT").toString();
            try {
                Date date;
                try {
                    // Try first format (yyyy-MM-dd)
                    date = new SimpleDateFormat("yyyy-MM-dd").parse(dateStr);
                } catch (Exception e1) {
                    // Try second format (dd MMM yyyy)
                    date = new SimpleDateFormat("dd MMM yyyy", Locale.ENGLISH).parse(dateStr);
                }
                // Format to yyyy-MM-dd
                String formattedDate = new SimpleDateFormat("yyyy-MM-dd").format(date);
                latestsubjectInfo.put("visit_date", formattedDate);
            } catch (Exception e) {
                latestsubjectInfo.put("visit_date", dateStr);
            }



            String siteName = getSiteName(studyCode, env, latestElement.get("SITECODE").toString(), latestElement.get("SUBJID").toString());
            latestsubjectInfo.put("dept_id",siteName);
            return latestsubjectInfo;
        }





    }
}
