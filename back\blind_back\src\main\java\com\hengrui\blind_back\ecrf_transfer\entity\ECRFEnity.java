package com.hengrui.blind_back.ecrf_transfer.entity;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * @ClassName ECRFEnity
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/8/20 14:08
 * @Version 1.0
 **/
public class ECRFEnity {
    //1.研究标识符
    @ExcelProperty("研究标识符")
    private String studyid;
    //2.受试者标识符
    @ExcelProperty("受试者标识符")
    private String ecrfNum;
    //3.是否死亡
    @ExcelProperty("是否死亡")
    private String isDeath;
    //4.是否严重不良事件
    @ExcelProperty("是否严重不良事件")
    private String isAE;
    //3.是否死亡
    @ExcelProperty("是否因AE退出治疗")
    private String isQuit;

    public String getStudyid() {
        return studyid;
    }

    public void setStudyid(String studyid) {
        this.studyid = studyid;
    }

    public String getEcrfNum() {
        return ecrfNum;
    }

    public void setEcrfNum(String ecrfNum) {
        this.ecrfNum = ecrfNum;
    }

    public String getIsDeath() {
        return isDeath;
    }

    public void setIsDeath(String isDeath) {
        this.isDeath = isDeath;
    }

    public String getIsAE() {
        return isAE;
    }

    public void setIsAE(String isAE) {
        this.isAE = isAE;
    }

    public String getIsQuit() {
        return isQuit;
    }

    public void setIsQuit(String isQuit) {
        this.isQuit = isQuit;
    }
}
