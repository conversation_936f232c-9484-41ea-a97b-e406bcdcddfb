package com.hengrui.blind_back.test_file.utils;

import org.springframework.stereotype.Component;

import java.io.*;


@Component
public class SASForDtaIOM {
    public  String readSAS(String codePath,String dtaFilePath,String studyName) throws IOException {
        String replaceStr1 = "&dta_name.";
        String replaceStr2 = "&studycode.";

        File file = new File(codePath);
        InputStreamReader in = new InputStreamReader ( new FileInputStream(file),"UTF-8");
        BufferedReader bufIn = new BufferedReader(in);

//        NtlmPasswordAuthentication auth = new NtlmPasswordAuthentication(USER_DOMAIN, USER_ACCOUNT, USER_PWS);
//        SmbFile remoteFile = new SmbFile(path, auth);
//        InputStreamReader in = new InputStreamReader ( new SmbFileInputStream(remoteFile),"GBK");
//        BufferedReader bufIn = new BufferedReader(in);

        // 替换
        String line = "";
        StringBuilder stringBuffer = new StringBuilder();
        while ((line = bufIn.readLine()) != null) {
            line = line.replaceAll(replaceStr1, dtaFilePath).replaceAll(replaceStr2, studyName);
            stringBuffer.append(line);
        }
        return stringBuffer.toString();
    }
}
