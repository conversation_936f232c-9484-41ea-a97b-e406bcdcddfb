package com.hengrui.blind_back.parse_excel_toDB.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSON;
import com.hengrui.blind_back.parse_excel_toDB.entity.DBDefineEntity;
import com.hengrui.blind_back.utils.ExcelReader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName DBDefineListener
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/2 9:23
 * @Version 1.0
 **/
@Slf4j
@Component
public class DBDefineListener implements ReadListener<DBDefineEntity> {
    private List<DBDefineEntity> DEDefineDataList = new ArrayList<>();

    private static final int BATCH_COUNT = 100;

    /**
     * 缓存的数据
     */
    private List<DBDefineEntity> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    @Override
    public void invoke(DBDefineEntity dbDefineEntity, AnalysisContext analysisContext) {
        log.info("解析到一条数据:{}", JSON.toJSONString(dbDefineEntity));
        cachedDataList.add(dbDefineEntity);
        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (cachedDataList.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }

    }

    private void saveData() {
        log.info("{}条数据，开始存储数据库！", cachedDataList.size());
        ExcelReader.setStudyId(cachedDataList);
        log.info("存储数据库成功！");
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        DBDefineListener.log.info("---------------------All  DBDefine Excel data parsed.----------------------------------------------.");
    }

    public List<DBDefineEntity> getDBDefineList() {
        return DEDefineDataList;
    }
}
