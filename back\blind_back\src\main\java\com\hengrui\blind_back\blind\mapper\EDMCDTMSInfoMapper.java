package com.hengrui.blind_back.blind.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Component
@Mapper
@Repository
@DS("slave_2")
public interface EDMCDTMSInfoMapper {
    String getEDMMail(String projectName);
    String getEDMQCMail(String projectName);
    List<Map<String, Object>> getProjectMemberMail(String projectName);
    Map<String, String> getUserInfoByMail(String userName);
    String getEDCName(String studyId);

}
