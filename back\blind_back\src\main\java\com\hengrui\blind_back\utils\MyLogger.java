package com.hengrui.blind_back.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @ClassName MyLogger
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/8/1 14:09
 * @Version 1.0
 **/

@Component
@Slf4j
public class MyLogger implements com.jcraft.jsch.Logger{
    static java.util.Hashtable<Integer, String> name = new java.util.Hashtable<>();
    static {
        name.put(DEBUG, "DEBUG");
        name.put(INFO, "INFO");
        name.put(WARN, "WARN");
        name.put(ERROR, "ERROR");
        name.put(FATAL, "FATAL");
    }
    @Override
    public boolean isEnabled(int i) {
        return true;
    }

    @Override
    public void log(int i, String s) {
        MyLogger.log.info(name.get(i) + ": " + s);
    }
}
