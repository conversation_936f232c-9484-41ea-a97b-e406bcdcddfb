package com.hengrui.blind_back.edc_definition.controller;

import com.hengrui.blind_back.edc_definition.service.EDCDefinitionService;
import com.hengrui.blind_back.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName EDCDefinitionController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/26 16:06
 * @Version 1.0
 **/
@RestController
@Slf4j
public class EDCDefinitionController {

    @Autowired
    EDCDefinitionService edcDefinitionService;

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getEDCDefinitionReport")
    public Map<String, Object> getEDCDefinitionReport(String taskId,
                                                      String server,
                                                      String projectId) {
        EDCDefinitionController.log.info("server is :" + server);
        FileUtils.getCDTMSAPI(server);
        FileUtils.getCDTMSAPIProjectId(server);
        FileUtils.getCDTMSAPIPre(server);
        Map<String, String> results = edcDefinitionService.getEDCDefinitionReport(taskId, projectId);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", results);
        return result;
    }
}
