package com.hengrui.blind_back.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hengrui.blind_back.constant.SASOnlieConstant;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.ProtocolException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.cert.CertificateException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;


/**
 * @ClassName EsignAPI
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/29 10:16
 * @Version 1.0
 **/

@Slf4j
@Component
public class EsignAPI {
    public static Map<String, String> uploadFile(String filePath, String uploadUrl) throws Exception {
        String result = "";
        Map<String, String> map = new HashMap<>();
        // Create a trust manager that trusts all certificates
        // Note: In production, you should properly validate certificates
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) throws CertificateException {
                    }

                    @Override
                    public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) throws CertificateException {
                    }

                    @Override
                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return new java.security.cert.X509Certificate[]{};
                    }
                }
        };

        // Create SSL context with trust manager
        SSLContext sslContext = SSLContext.getInstance("SSL");
        sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

        // Create OkHttpClient with SSL settings
        OkHttpClient client = new OkHttpClient.Builder()
                .sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustAllCerts[0])
                .hostnameVerifier((hostname, session) -> true)
                .build();

        // Create the file object
        File file = new File(filePath);

        // Create request body
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("files", file.getName(),
                        RequestBody.create(MediaType.parse("application/octet-stream"), file))
                .build();

        // Build the request
        Request request = new Request.Builder()
                .url(uploadUrl)
                .post(requestBody)
                .build();

        // Execute the request
        try (Response response = client.newCall(request).execute()) {
            System.out.println("Response status code: " + response.code());
            if (response.code() == 200) {
                result = response.body().string();
                JSONObject resObject = JSONObject.parseObject(result);
                JSONObject dataObject = JSONObject.parseObject(resObject.get("data").toString());
                String items = dataObject.get("items").toString();
                JSONArray objects = JSONArray.parseArray(items);
                String fileId = ((JSONObject) objects.get(0)).get("file_id").toString();
                String filename = ((JSONObject) objects.get(0)).get("filename").toString();
                map.put("fileId", fileId);
                map.put("filename", filename);
            }
        }
        return map;
    }


    public static cn.hutool.json.JSONObject getRequest(String param) {
        String requestURL = param;
        try {
            URL url = new URL(requestURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            log.info("Response Code: " + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                cn.hutool.json.JSONObject jsonResponse = new cn.hutool.json.JSONObject(response.toString());
                // Extract the study_id
                if (!ObjectUtils.isEmpty(jsonResponse.getJSONObject("data"))) {
                    return jsonResponse.getJSONObject("data");
                }

            } else {
                log.info("GET request not worked");
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public static String postRequest(String requestURL, String param) {
        StringBuilder response = new StringBuilder();
        log.info("---------------------------------------the dataSave request URL is :" + requestURL);
        try {
            URL url = new URL(requestURL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setDoOutput(true); // Set this before setting the request method
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Connection", "keep-alive");
            connection.setDoOutput(true);
            // Pass the parameters to the API
            String jsonStr = param;
            log.info("-----------------usersyn dataSave API  post body data is :" + jsonStr);
            // Write the request body
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonStr.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            int responseCode = connection.getResponseCode();
            String requestMethod = connection.getRequestMethod();
            log.info("requestMethod is : " + requestMethod);
            log.info("Response Code: " + responseCode);
            if (responseCode == HttpsURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing


            }
            log.info("call ESIGN POST API response is :" + response.toString());
        } catch (ProtocolException e) {
            throw new RuntimeException(e);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return response.toString();

    }

    public static cn.hutool.json.JSONObject RTSMGetRequest(String param) {
        String requestURL = param;
        try {
            URL url = new URL(requestURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            log.info("Response Code: " + responseCode);
            if (responseCode == 0) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                cn.hutool.json.JSONObject jsonResponse = new cn.hutool.json.JSONObject(response.toString());
                // Extract the study_id
                if (!ObjectUtils.isEmpty(jsonResponse.getJSONObject("data"))) {
                    return jsonResponse.getJSONObject("data");
                }

            } else {
                log.info("GET request not worked");
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public static String RTSMPostRequest(String requestURL, String param) {
        StringBuilder response = new StringBuilder();
        log.info("---------------------------------------the dataSave request URL is :" + requestURL);
        try {
            URL url = new URL(requestURL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setDoOutput(true); // Set this before setting the request method
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Connection", "keep-alive");
            connection.setDoOutput(true);
            // Pass the parameters to the API
            String jsonStr = param;
            log.info("-----------------RTSM GEN  post body data is :" + jsonStr);
            // Write the request body
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonStr.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            int responseCode = connection.getResponseCode();
            String requestMethod = connection.getRequestMethod();
            log.info("requestMethod is : " + requestMethod);
            log.info("Response Code: " + responseCode);
            if (responseCode == 0) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing


            }
            log.info("call RTSM GEN POST API response is :" + response.toString());
        } catch (ProtocolException e) {
            throw new RuntimeException(e);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return response.toString();

    }



    public static String putRequest(String requestURL, String param) {
        StringBuilder response = new StringBuilder();
        log.info("PUT Request URL: {}", requestURL);

        HttpsURLConnection connection = null;
        try {
            // Create connection
            URL url = new URL(requestURL);
            connection = (HttpsURLConnection) url.openConnection();

            // Configure connection for PUT
            connection.setRequestMethod("PUT");
            connection.setDoOutput(true);  // Enable writing output (needed for PUT)

            // Set headers
            connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Connection", "keep-alive");

            // Write request body
            log.info("Request body: {}", param);
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = param.getBytes(StandardCharsets.UTF_8);
                os.write(input);
                os.flush();
            }

            // Get response
            int responseCode = connection.getResponseCode();
            log.info("Response Code: {}", responseCode);

            // Read response
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(
                            responseCode >= 400
                                    ? connection.getErrorStream()
                                    : connection.getInputStream(),
                            StandardCharsets.UTF_8))) {

                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
            }

            log.info("Response body: {}", response);
            return response.toString();

        } catch (Exception e) {
            log.error("Error during PUT request", e);
            throw new RuntimeException("Failed to execute PUT request", e);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }



    public static String deleteRequest(String requestURL) {
        StringBuilder response = new StringBuilder();
        log.info("DELETE Request URL: {}", requestURL);

        HttpsURLConnection connection = null;
        try {
            // Create connection
            URL url = new URL(requestURL);
            connection = (HttpsURLConnection) url.openConnection();

            // Configure connection for PUT
            connection.setRequestMethod("DELETE");
            connection.setDoOutput(true);  // Enable writing output (needed for PUT)

            // Set headers
            connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Connection", "keep-alive");


            // Get response
            int responseCode = connection.getResponseCode();
            log.info("Response Code: {}", responseCode);

            // Read response
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(
                            responseCode >= 400
                                    ? connection.getErrorStream()
                                    : connection.getInputStream(),
                            StandardCharsets.UTF_8))) {

                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
            }

            log.info("Response body: {}", response);
            return response.toString();

        } catch (Exception e) {
            log.error("Error during DELETE request", e);
            throw new RuntimeException("Failed to execute DELETE request", e);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }


    public static int addSigner(String taskId, String nameEn, String nameZh, String email, String fileCode, String signReason, int isAuthor) {
        JSONObject signer = new JSONObject();
        signer.put("name_en", nameEn);
        signer.put("name_zh", nameZh);
        signer.put("email", Base64.getEncoder().encodeToString(email.getBytes()));
        signer.put("verification_code", fileCode);
        //signer.put("sign_reason", signReason);
        signer.put("is_author", isAuthor);

        // 调用API添加签署人
        String signerInfo = EsignAPI.postRequest(SASOnlieConstant.ESIGN_TST_TASK + "/" + taskId + "/users", signer.toJSONString());
        JSONObject signerJson = JSONObject.parseObject(signerInfo);
        return (int) JSONObject.parseObject(signerJson.get("data").toString()).get("id");
    }


    //设置电子签默认位置
    public static JSONObject setDefaultSignerPosition(String taskId,int userid, String px,String py,String dpx,String dpy,int page, JSONObject signPosObj) {
        JSONArray signItems = new JSONArray();
        // 创建第一个item对象
        JSONObject item = new JSONObject();
        item.put("page", page);
        item.put("position_x", px);
        item.put("position_y", py);
        item.put("date_position_x", dpx);
        item.put("date_position_y", dpy);
        // 将item对象添加到items数组
        signItems.add(item);
        // 将items数组添加到JSON对象
        signPosObj.put("items", signItems);
        //更新签署人坐标-作者
        String updateResult = EsignAPI.putRequest(SASOnlieConstant.ESIGN_TST_TASK+"/" + taskId + "/users/" + userid, signPosObj.toString());
        return signPosObj;
    }



}
