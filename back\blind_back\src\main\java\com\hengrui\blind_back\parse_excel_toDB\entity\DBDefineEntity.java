package com.hengrui.blind_back.parse_excel_toDB.entity;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * @ClassName DBDefineEntity
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/1 17:31
 * @Version 1.0
 **/
public class DBDefineEntity {

    private String uuId;

    private String studyid;

    private  String versionstatus;

    private String versionv;
    //1.表名
    @ExcelProperty("表名")
    private String tablenamech;
    //2.表
    @ExcelProperty("表")
    private String tablenameen;
    //3.标准表
    @ExcelProperty("标准表")
    private String standardtablename;
    //4.引用表
    @ExcelProperty("引用表")
    private String referencetablename;
    //5.变量名
    @ExcelProperty("变量名")
    private String variablename;
    //6.变量
    @ExcelProperty("变量")
    private String variable;
    //7.变量类型
    @ExcelProperty("变量类型")
    private String variabletype;
    public String getStudyid() {
        return studyid;
    }

    public void setStudyid(String studyid) {
        this.studyid = studyid;
    }

    public String getTablenamech() {
        return tablenamech;
    }

    public void setTablenamech(String tablenamech) {
        this.tablenamech = tablenamech;
    }

    public String getTablenameen() {
        return tablenameen;
    }

    public void setTablenameen(String tablenameen) {
        this.tablenameen = tablenameen;
    }

    public String getStandardtablename() {
        return standardtablename;
    }

    public void setStandardtablename(String standardtablename) {
        this.standardtablename = standardtablename;
    }

    public String getReferencetablename() {
        return referencetablename;
    }

    public void setReferencetablename(String referencetablename) {
        this.referencetablename = referencetablename;
    }

    public String getVariablename() {
        return variablename;
    }

    public void setVariablename(String variablename) {
        this.variablename = variablename;
    }

    public String getVariable() {
        return variable;
    }

    public void setVariable(String variable) {
        this.variable = variable;
    }

    public String getVariabletype() {
        return variabletype;
    }

    public void setVariabletype(String variabletype) {
        this.variabletype = variabletype;
    }

    public String getUuId() {
        return uuId;
    }

    public void setUuId(String uuId) {
        this.uuId = uuId;
    }

    public String getVersionstatus() {
        return versionstatus;
    }

    public void setVersionstatus(String versionstatus) {
        this.versionstatus = versionstatus;
    }

    public String getVersionv() {
        return versionv;
    }

    public void setVersionv(String versionv) {
        this.versionv = versionv;
    }
}
