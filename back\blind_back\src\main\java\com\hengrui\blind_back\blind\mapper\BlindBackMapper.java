package com.hengrui.blind_back.blind.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hengrui.blind_back.blind.entity.CSVMappingEntity;
import com.hengrui.blind_back.blind.entity.CSVMetaEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Component
@Mapper
@Repository
@DS("slave_3")
public interface BlindBackMapper {


    //批量新增CSV数据
    int insertBatch(List<CSVMetaEntity> entities);

    int insertColMap(CSVMappingEntity entity);

    List<CSVMappingEntity> getCSVCol(String fileId);


    List<CSVMetaEntity> getCSVMeta(String fileId);

    //新增编盲记录
    int insertBlindRecords(String id, String jsonData, String userName, String projectName, String fileName, String role, String exDataId, String dataType, String blindOperateName, String blindDemand, String blindMember, String blindMemberWithMail);

    String getBlindRecords(String projectName, String fileName);

    List<Map<String, String>> getBlindHistory(String projectName, String fileName);

    String getBlindOperate(String historyId);

    Map<String, String> getDemandAndName(String historyId);

    String getJsonFileName(String projectName, String fileName);

    int updateFileID(String fileID, String id, String originalFileId, String localPath, String sasLogPath, String isSuccess, String compareFilePath);

    void updateBlindStatus(String fileId, String isApprove, String userName);

    void insertOriginalFileRecord(String fileId, String projectName, String fileName, String filePath);

    //获取待编盲原始文件id
    String unBlindFileId(String fileName, String projectName);

    //从编盲记录里获取原始文件id
    String getOriginalFileId(String fileId);

    void updateOriginalFileStatus(String originalFileId, String isApprove);

    String getBlindFilePath(String fileId);

    //QC是否发过邮件
    String getQCStatus(String fileId);

    //获取是否通过
    String getBlindIsApprove(String fileId);

    void insertEmailRecords(@Param("id") String id, @Param("fileId") String fileId, @Param("originalFileId") String originalFileId,
                            @Param("userName") String userName, @Param("projectName") String projectName, @Param("fileName") String fileName,
                            @Param("edmMail") String edmMail, @Param("type") String type, @Param("exDataId") String exDataId,
                            @Param("comments") String comments);
    //获取已经通过QC的文件id

    String getApprovedFileId(String fileName, String projectName);

    int getMailIsSend(String approvedFileId);

    String getApprovedFilePath(String fileId);

    int getFileIsSendCount(String fileName, String projectName);


    int judgeIsBlinded(String fileId);


    List<Map<String, Object>> getOperateAndEmailHistory(String exDataId);

    Map<String, Object> getOriginalIdAndStatus(String projectName, String fileName);

    String getUnApprovedFileIdByOriginalId(String originalFileId);

    String getCompareFileUrl(String fileId);

    Map<String, String> getQCFileInfo(String fileName, String projectName);

    int checkTokenIsExist(String token);

    void insertTokenRecord(String token);

    String getBlindedFile(String fileId);

    String getMemberInfoByFileId(String fileId);

    String getBlindMemberWithMail(String fileId);

    int insertDataSetPassword(String id,String token, String password, String userName, String role, String projectName);

    List<Map<String, String>> getDataSetPassword();

    int addScheduleRecord(String id, String userId, String nodeName, String version, String studyId, String batchrun,String jsonParam,String remark);

    List<Map<String, String>> getScheduleReviewRecord();

    List<Map<String, String>> getQueryReviewRecord();

    List<Map<String, String>> getTrailReviewRecord();

    String getDataSetPass(String studyId);

    int insertRaveDataUploadRecord(String id, String userName, String studyId, String originalFilename, String fileLocalPath, String fileMinioPath);
}
